<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<groupId>com.actonia</groupId>
	<artifactId>backend</artifactId>
	<version>0.0.1-SNAPSHOT</version>
	<packaging>jar</packaging>
	<name>backend</name>
	<url>http://www.seoclarity.com</url>

	<properties>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
		<project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
		<java.version>11</java.version>
		<!--		<shineio.repo.internal>http://192.168.0.223:8081</shineio.repo.internal>-->
		<shineio.repo.external>http://52.116.38.151:8081/</shineio.repo.external>

		<project.youtube.version>v3-rev182-1.22.0</project.youtube.version>
		<project.youtube.analytics.version>v1-rev63-1.22.0</project.youtube.analytics.version>
		<project.youtube.reporting.version>v1-rev10-1.22.0</project.youtube.reporting.version>
		<kafka.version>0.10.2.2</kafka.version>
	</properties>

	<repositories>
		<!--Dell789 internal <repository> <id>central-internal</id> <name>central-internal</name>
			<url>${shineio.repo.internal}/repository/maven-public/</url> <layout>default</layout>
			</repository> -->
		<repository>
			<id>central-external</id>
			<name>central-external</name>
			<url>${shineio.repo.external}/repository/maven-public/</url>
			<layout>default</layout>
		</repository>
		<!--Dell789 external -->
	</repositories>


	<dependencyManagement>
		<dependencies>
			<dependency>
				<groupId>com.google.cloud</groupId>
				<artifactId>libraries-bom</artifactId>
				<version>26.61.0</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>
			<dependency>
				<groupId>software.amazon.awssdk</groupId>
				<artifactId>bom</artifactId>
				<version>2.17.224</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>
		</dependencies>
	</dependencyManagement>

	<dependencies>
		<dependency>
			<groupId>junit</groupId>
			<artifactId>junit</artifactId>
			<version>4.12</version>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>ru.yandex.clickhouse</groupId>
			<artifactId>clickhouse-jdbc</artifactId>
			<!--			<version>0.1.13</version>-->
			<version>0.3.1-patch</version>
			<exclusions>
				<exclusion>
					<artifactId>httpclient</artifactId>
					<groupId>org.apache.httpcomponents</groupId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>dom4j</groupId>
			<artifactId>dom4j</artifactId>
			<version>1.6.1</version>
			<exclusions>
				<exclusion>
					<artifactId>xml-apis</artifactId>
					<groupId>xml-apis</groupId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>com.google.code.gson</groupId>
			<artifactId>gson</artifactId>
			<version>2.6.2</version>
		</dependency>

		<dependency>
			<groupId>commons-io</groupId>
			<artifactId>commons-io</artifactId>
			<version>2.13.0</version>
		</dependency>
		<dependency>
			<groupId>commons-lang</groupId>
			<artifactId>commons-lang</artifactId>
			<version>2.6</version>
		</dependency>
		<dependency>
			<groupId>commons-dbcp</groupId>
			<artifactId>commons-dbcp</artifactId>
			<version>1.4</version>
		</dependency>
		<dependency>
			<groupId>commons-configuration</groupId>
			<artifactId>commons-configuration</artifactId>
			<version>1.10</version>
		</dependency>
		<dependency>
			<groupId>commons-collections</groupId>
			<artifactId>commons-collections</artifactId>
			<version>3.2.2</version>
		</dependency>
		<dependency>
			<groupId>commons-net</groupId>
			<artifactId>commons-net</artifactId>
			<version>3.5</version>
		</dependency>
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-compress</artifactId>
			<version>1.13</version>
		</dependency>

		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-csv</artifactId>
			<version>1.5</version>
		</dependency>
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-math3</artifactId>
			<version>3.6.1</version>
		</dependency>

		<dependency>
			<groupId>software.amazon.awssdk</groupId>
			<artifactId>cloudwatch</artifactId>
		</dependency>
		<dependency>
			<groupId>software.amazon.awssdk</groupId>
			<artifactId>cloudwatchevents</artifactId>
		</dependency>
		<dependency>
			<groupId>software.amazon.awssdk</groupId>
			<artifactId>cloudwatchlogs</artifactId>
		</dependency>
		<dependency>
			<groupId>software.amazon.awssdk</groupId>
			<artifactId>apigateway</artifactId>
		</dependency>
		<dependency>
			<groupId>software.amazon.awssdk</groupId>
			<artifactId>sts</artifactId>
		</dependency>
		<dependency>
			<groupId>software.amazon.awssdk</groupId>
			<artifactId>auth</artifactId>
		</dependency>
		<dependency>
			<groupId>software.amazon.awssdk</groupId>
			<artifactId>aws-core</artifactId>
		</dependency>
		<dependency>
			<groupId>software.amazon.awssdk</groupId>
			<artifactId>secretsmanager</artifactId>
		</dependency>
		<dependency>
			<groupId>software.amazon.awssdk</groupId>
			<artifactId>regions</artifactId>
		</dependency>



		<dependency>
			<groupId>org.springframework.data</groupId>
			<artifactId>spring-data-jpa</artifactId>
			<version>1.10.5.RELEASE</version>
		</dependency>

		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>druid</artifactId>
			<version>1.2.18</version>
		</dependency>

		<dependency>
			<groupId>mysql</groupId>
			<artifactId>mysql-connector-java</artifactId>
			<version>5.1.21</version>
		</dependency>

		<dependency>
			<groupId>com.mysql</groupId>
			<artifactId>mysql-connector-j</artifactId>
			<version>8.0.33</version>
		</dependency>

		<dependency>
			<groupId>com.facebook</groupId>
			<artifactId>fb303</artifactId>
			<version>1.0</version>
		</dependency>

		<dependency>
			<groupId>org.apache</groupId>
			<artifactId>thrift</artifactId>
			<version>1.0</version>
		</dependency>
		<dependency>
			<groupId>org.mongodb</groupId>
			<artifactId>bson</artifactId>
			<version>3.2.1</version>
		</dependency>
		<dependency>
			<groupId>org.mongodb</groupId>
			<artifactId>mongo-java-driver</artifactId>
			<version>3.2.1</version>
		</dependency>

		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-web</artifactId>
			<version>4.3.4.RELEASE</version>
		</dependency>
		<dependency>
			<groupId>org.apache.solr</groupId>
			<artifactId>solr-solrj</artifactId>
			<version>5.3.1</version>
			<exclusions>
				<exclusion>
					<artifactId>httpclient</artifactId>
					<groupId>org.apache.httpcomponents</groupId>
				</exclusion>
				<exclusion>
					<artifactId>httpcore</artifactId>
					<groupId>org.apache.httpcomponents</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<!-- <dependency> <groupId>com.amazonaws</groupId> <artifactId>aws-java-sdk</artifactId>
			<version>1.11.66</version> </dependency> -->
		<dependency>
			<groupId>org.apache.poi</groupId>
			<artifactId>poi</artifactId>
			<version>3.15</version>
		</dependency>
		<dependency>
			<groupId>org.apache.poi</groupId>
			<artifactId>poi-ooxml</artifactId>
			<version>3.15</version>
		</dependency>
		<dependency>
			<groupId>com.amazonaws</groupId>
			<artifactId>aws-java-sdk-s3</artifactId>
			<version>1.11.701</version>
		</dependency>
		<dependency>
			<groupId>com.amazonaws</groupId>
			<artifactId>aws-java-sdk-core</artifactId>
			<version>1.11.701</version>
			<exclusions>
				<exclusion>
					<artifactId>httpclient</artifactId>
					<groupId>org.apache.httpcomponents</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.amazonaws</groupId>
			<artifactId>aws-java-sdk-sqs</artifactId>
			<version>1.11.701</version>
		</dependency>
		<dependency>
			<groupId>com.amazonaws</groupId>
			<artifactId>aws-java-sdk-sns</artifactId>
			<version>1.11.701</version>
		</dependency>
		<dependency>
			<groupId>com.amazonaws</groupId>
			<artifactId>jmespath-java</artifactId>
			<version>1.11.701</version>
		</dependency>


		<dependency>
			<groupId>com.vertica</groupId>
			<artifactId>vertica-jdbc-driver</artifactId>
			<version>07.00.0100</version>
		</dependency>
		<dependency>
			<groupId>org.slf4j</groupId>
			<artifactId>slf4j-log4j12</artifactId>
			<version>1.7.21</version>
		</dependency>


		<dependency>
			<groupId>ch.ethz.ganymed</groupId>
			<artifactId>ganymed-ssh2</artifactId>
			<version>build210</version>
		</dependency>

		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
			<version>1.18.22</version>
			<scope>provided</scope>
		</dependency>

		<!-- ali json -->
		<dependency>
			<groupId>com.alibaba</groupId>
			<artifactId>fastjson</artifactId>
			<version>2.0.44</version>
		</dependency>

		<!-- https://mvnrepository.com/artifact/com.google.code.gson/gson -->
		<dependency>
			<groupId>com.google.code.gson</groupId>
			<artifactId>gson</artifactId>
			<version>2.2.4</version>
		</dependency>
		<!-- https://mvnrepository.com/artifact/info.debatty/java-string-similarity -->
		<dependency>
			<groupId>info.debatty</groupId>
			<artifactId>java-string-similarity</artifactId>
			<version>1.1.0</version>
		</dependency>
		<dependency>
			<groupId>com.github.mpkorstanje</groupId>
			<artifactId>simmetrics-core</artifactId>
			<version>4.1.1</version>
		</dependency>

		<dependency>
			<groupId>com.google.protobuf</groupId>
			<artifactId>protobuf-java</artifactId>
			<version>4.31.1</version>
		</dependency>

		<!-- YouTube Data V3 support -->
		<dependency>
			<groupId>com.google.apis</groupId>
			<artifactId>google-api-services-youtube</artifactId>
			<version>${project.youtube.version}</version>
		</dependency>

		<!-- Required for any code that makes calls to the YouTube Analytics API -->
		<dependency>
			<groupId>com.google.apis</groupId>
			<artifactId>google-api-services-youtubeAnalytics</artifactId>
			<version>${project.youtube.analytics.version}</version>
		</dependency>

		<!-- Required for any code that makes calls to the YouTube Reporting API -->
		<dependency>
			<groupId>com.google.apis</groupId>
			<artifactId>google-api-services-youtubereporting</artifactId>
			<version>${project.youtube.reporting.version}</version>
		</dependency>
		<!-- new lucene start-->
		<!--		<dependency>
                    <groupId>org.apache.lucene</groupId>
                    <artifactId>lucene-core</artifactId>
                    <version>9.11.1</version>
                </dependency>-->
		<dependency>
			<groupId>org.apache.lucene</groupId>
			<artifactId>lucene-analysis-kuromoji</artifactId>
			<version>9.11.1</version>
		</dependency>
		<dependency>
			<groupId>org.apache.lucene</groupId>
			<artifactId>lucene-analysis-stempel</artifactId>
			<version>9.11.1</version>
		</dependency>
		<dependency>
			<groupId>org.apache.lucene</groupId>
			<artifactId>lucene-analysis-nori</artifactId>
			<version>9.11.1</version>
		</dependency>
		<dependency>
			<groupId>org.apache.lucene</groupId>
			<artifactId>lucene-analysis-smartcn</artifactId>
			<version>9.11.1</version>
		</dependency>

		<!--  new lucene end -->
		<!--		<dependency>-->
		<!--			<groupId>org.apache.lucene</groupId>-->
		<!--			<artifactId>lucene-analyzers-kuromoji</artifactId>-->
		<!--			<version>8.4.1</version>-->
		<!--			<exclusions>-->
		<!--				<exclusion>-->
		<!--					<artifactId>lucene-core</artifactId>-->
		<!--					<groupId>org.apache.lucene</groupId>-->
		<!--				</exclusion>-->
		<!--			</exclusions>-->
		<!--		</dependency>-->

		<!--		<dependency>-->
		<!--			<groupId>org.apache.lucene</groupId>-->
		<!--			<artifactId>lucene-analyzers-stempel</artifactId>-->
		<!--			<version>8.4.1</version>-->
		<!--			<exclusions>-->
		<!--				<exclusion>-->
		<!--					<artifactId>lucene-core</artifactId>-->
		<!--					<groupId>org.apache.lucene</groupId>-->
		<!--				</exclusion>-->
		<!--			</exclusions>-->
		<!--		</dependency>-->

		<!--		<dependency>-->
		<!--			<groupId>org.apache.lucene</groupId>-->
		<!--			<artifactId>lucene-analyzers-nori</artifactId>-->
		<!--			<version>8.4.1</version>-->
		<!--			<exclusions>-->
		<!--				<exclusion>-->
		<!--					<artifactId>lucene-core</artifactId>-->
		<!--					<groupId>org.apache.lucene</groupId>-->
		<!--				</exclusion>-->
		<!--			</exclusions>-->
		<!--		</dependency>-->
		<!--		&lt;!&ndash; https://mvnrepository.com/artifact/com.github.magese/ik-analyzer &ndash;&gt;-->
		<!--		<dependency>-->
		<!--			<groupId>com.github.magese</groupId>-->
		<!--			<artifactId>ik-analyzer</artifactId>-->
		<!--			<version>8.5.0</version>-->
		<!--			<exclusions>-->
		<!--				<exclusion>-->
		<!--					<artifactId>lucene-core</artifactId>-->
		<!--					<groupId>org.apache.lucene</groupId>-->
		<!--				</exclusion>-->
		<!--			</exclusions>-->
		<!--		</dependency>-->

		<dependency>
			<groupId>org.apache.kafka</groupId>
			<artifactId>kafka_2.10</artifactId>
			<version>${kafka.version}</version>
		</dependency>

		<!-- https://mvnrepository.com/artifact/org.jsoup/jsoup -->
		<dependency>
			<groupId>org.jsoup</groupId>
			<artifactId>jsoup</artifactId>
			<version>1.18.1</version>
		</dependency>

		<!-- https://mvnrepository.com/artifact/org.apache.velocity/velocity -->
		<dependency>
			<groupId>org.apache.velocity</groupId>
			<artifactId>velocity</artifactId>
			<version>1.6.2</version>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-context-support</artifactId>
			<version>4.1.9.RELEASE</version>
		</dependency>
		<!-- https://mvnrepository.com/artifact/org.apache.velocity/velocity-tools -->
		<dependency>
			<groupId>org.apache.velocity</groupId>
			<artifactId>velocity-tools</artifactId>
			<version>2.0</version>
		</dependency>
		<!-- https://mvnrepository.com/artifact/org.ogema.widgets/widget-collection -->
		<dependency>
			<groupId>org.ogema.widgets</groupId>
			<artifactId>widget-collection</artifactId>
			<version>2.2.0</version>
		</dependency>
		<dependency>
			<groupId>net.dongliu</groupId>
			<artifactId>requests</artifactId>
			<version>4.18.1</version>
		</dependency>

		<dependency>
			<groupId>ch.ethz.ganymed</groupId>
			<artifactId>ganymed-ssh2</artifactId>
			<version>build210</version>
		</dependency>

		<dependency>
			<groupId>net.java.dev.jets3t</groupId>
			<artifactId>jets3t</artifactId>
			<version>0.9.0</version>
			<exclusions>
				<exclusion>
					<artifactId>httpclient</artifactId>
					<groupId>org.apache.httpcomponents</groupId>
				</exclusion>
				<exclusion>
					<artifactId>httpcore</artifactId>
					<groupId>org.apache.httpcomponents</groupId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>com.google.cloud</groupId>
			<artifactId>google-cloud-bigquery</artifactId>
			<version>2.50.1</version>
			<exclusions>
				<exclusion>
					<artifactId>httpclient</artifactId>
					<groupId>org.apache.httpcomponents</groupId>
				</exclusion>
				<exclusion>
					<artifactId>httpcore</artifactId>
					<groupId>org.apache.httpcomponents</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.google.api-client</groupId>
			<artifactId>google-api-client</artifactId>
			<version>2.7.1</version>
			<exclusions>
				<exclusion>
					<artifactId>httpclient</artifactId>
					<groupId>org.apache.httpcomponents</groupId>
				</exclusion>
				<exclusion>
					<artifactId>httpcore</artifactId>
					<groupId>org.apache.httpcomponents</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.google.oauth-client</groupId>
			<artifactId>google-oauth-client-jetty</artifactId>
			<version>1.32.1</version>
		</dependency>
		<dependency>
			<groupId>com.google.apis</groupId>
			<artifactId>google-api-services-gmail</artifactId>
			<version>v1-rev110-1.25.0</version>
		</dependency>
		<dependency>
			<groupId>com.google.cloud</groupId>
			<artifactId>google-cloud-storage</artifactId>
		</dependency>
		<dependency>
			<groupId>com.google.oauth-client</groupId>
			<artifactId>google-oauth-client-java6</artifactId>
			<version>1.33.0</version>
		</dependency>
		<dependency>
			<groupId>com.google.http-client</groupId>
			<artifactId>google-http-client-gson</artifactId>
			<version>1.47.0</version>
		</dependency>

		<dependency>
			<groupId>com.google.http-client</groupId>
			<artifactId>google-http-client</artifactId>
			<version>1.47.0</version>
			<exclusions>
				<exclusion>
					<artifactId>httpclient</artifactId>
					<groupId>org.apache.httpcomponents</groupId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>com.google.apis</groupId>
			<artifactId>google-api-services-compute</artifactId>
			<version>v1-rev214-1.25.0</version>
		</dependency>

		<dependency>
			<groupId>net.sourceforge.javacsv</groupId>
			<artifactId>javacsv</artifactId>
			<version>2.0</version>
		</dependency>

		<dependency>
			<groupId>redis.clients</groupId>
			<artifactId>jedis</artifactId>
			<version>3.1.0</version>
		</dependency>

		<dependency>
			<groupId>com.squareup.okhttp3</groupId>
			<artifactId>okhttp</artifactId>
			<version>4.10.0</version>
		</dependency>

		<!-- https://mvnrepository.com/artifact/com.github.houbb/opencc4j -->
		<dependency>
			<groupId>com.github.houbb</groupId>
			<artifactId>opencc4j</artifactId>
			<version>1.0.3</version>
		</dependency>

		<dependency>
			<groupId>com.jcraft</groupId>
			<artifactId>jsch</artifactId>
			<version>0.1.55</version>
		</dependency>

		<dependency>
			<groupId>com.opencsv</groupId>
			<artifactId>opencsv</artifactId>
			<version>4.4</version>
		</dependency>


		<dependency>
			<groupId>com.amazonaws</groupId>
			<artifactId>aws-java-sdk-sts</artifactId>
			<version>1.11.714</version>
		</dependency>
		<dependency>
			<groupId>cn.hutool</groupId>
			<artifactId>hutool-all</artifactId>
			<version>5.8.11</version>
		</dependency>

		<!--		&lt;!&ndash; https://mvnrepository.com/artifact/javax.mail/mail &ndash;&gt;-->
		<!--		<dependency>-->
		<!--		    <groupId>javax.mail</groupId>-->
		<!--		    <artifactId>mail</artifactId>-->
		<!--		    <version>1.5.0</version>-->
		<!--		</dependency>-->
		<dependency>
			<groupId>com.sun.mail</groupId>
			<artifactId>javax.mail</artifactId>
			<version>1.5.0</version>
		</dependency>

		<dependency>
			<groupId>com.aliyun</groupId>
			<artifactId>aliyun-java-sdk-core</artifactId>
			<version>4.5.17</version>
			<exclusions>
				<exclusion>
					<artifactId>httpclient</artifactId>
					<groupId>org.apache.httpcomponents</groupId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>com.aliyun</groupId>
			<artifactId>aliyun-java-sdk-ecs</artifactId>
			<version>4.22.2</version>
		</dependency>
		<dependency>
			<groupId>com.aliyun.oss</groupId>
			<artifactId>aliyun-sdk-oss</artifactId>
			<version>3.17.4</version>
		</dependency>

		<dependency>
			<groupId>org.apache.parquet</groupId>
			<artifactId>parquet-avro</artifactId>
			<version>1.11.1</version>
		</dependency>
		<dependency>
			<groupId>org.apache.parquet</groupId>
			<artifactId>parquet-hadoop</artifactId>
			<version>1.11.1</version>
		</dependency>
		<dependency>
			<groupId>org.apache.parquet</groupId>
			<artifactId>parquet-column</artifactId>
			<version>1.11.1</version>
		</dependency>
		<dependency>
			<groupId>org.apache.parquet</groupId>
			<artifactId>parquet-common</artifactId>
			<version>1.11.1</version>
		</dependency>
		<dependency>
			<groupId>org.apache.hadoop</groupId>
			<artifactId>hadoop-common</artifactId>
			<version>3.3.0</version>
			<exclusions>
				<exclusion>
					<artifactId>httpclient</artifactId>
					<groupId>org.apache.httpcomponents</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>org.apache.hadoop</groupId>
			<artifactId>hadoop-mapreduce-client-core</artifactId>
			<version>3.3.0</version>
		</dependency>

		<dependency>
			<groupId>com.backblaze.b2</groupId>
			<artifactId>b2-sdk-core</artifactId>
			<version>5.0.0</version>
			<scope>compile</scope>
		</dependency>
		<dependency>
			<groupId>com.backblaze.b2</groupId>
			<artifactId>b2-sdk-httpclient</artifactId>
			<version>5.0.0</version>
			<scope>compile</scope>
			<exclusions>
				<exclusion>
					<artifactId>httpclient</artifactId>
					<groupId>org.apache.httpcomponents</groupId>
				</exclusion>
			</exclusions>
		</dependency>
		<dependency>
			<groupId>com.github.housepower</groupId>
			<artifactId>clickhouse-native-jdbc</artifactId>
			<version>1.6-stable</version>
		</dependency>
		<dependency>
			<groupId>org.testcontainers</groupId>
			<artifactId>clickhouse</artifactId>
			<version> 1.15.0</version>
		</dependency>

		<dependency>
			<groupId>com.googlecode.json-simple</groupId>
			<artifactId>json-simple</artifactId>
			<version>1.1.1</version>
		</dependency>

		<!--		&lt;!&ndash; https://mvnrepository.com/artifact/com.amazonaws/aws-lambda-java-core &ndash;&gt;-->
		<!--		<dependency>-->
		<!--			<groupId>com.amazonaws</groupId>-->
		<!--			<artifactId>aws-lambda-java-core</artifactId>-->
		<!--			<version>1.2.1</version>-->
		<!--		</dependency>-->

		<!--		&lt;!&ndash; https://mvnrepository.com/artifact/com.amazonaws/aws-java-sdk-lambda &ndash;&gt;-->
		<!--		<dependency>-->
		<!--			<groupId>com.amazonaws</groupId>-->
		<!--			<artifactId>aws-java-sdk-lambda</artifactId>-->
		<!--			<version>1.12.137</version>-->
		<!--		</dependency>-->


		<!-- 	<dependency>
                <groupId>com.actonia</groupId>
                <artifactId>sc-common-vo</artifactId>
                <version>1.0.1-SNAPSHOT</version>
            </dependency>

                <dependency>
                  <groupId>com.actonia</groupId>
                  <artifactId>sc-common-vo</artifactId>
                  <version>1.0.1-SNAPSHOT</version>
                  <scope>system</scope>
                  <systemPath>${project.basedir}/lib/sc-common-vo-1.0.1-SNAPSHOT.jar</systemPath>
                </dependency>-->
		<dependency>
			<groupId>org.jasypt</groupId>
			<artifactId>jasypt</artifactId>
			<version>1.9.3</version>
		</dependency>

		<dependency>
			<groupId>com.knuddels</groupId>
			<artifactId>jtokkit</artifactId>
			<version>0.6.1</version>
		</dependency>
		<dependency>
			<groupId>org.bouncycastle</groupId>
			<artifactId>bcpg-jdk15on</artifactId>
			<version>1.50</version>
		</dependency>

		<dependency>
			<groupId>com.konghq</groupId>
			<artifactId>unirest-java</artifactId>
			<version>3.14.5</version>
			<exclusions>
				<exclusion>
					<artifactId>httpclient</artifactId>
					<groupId>org.apache.httpcomponents</groupId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>com.google.api-ads</groupId>
			<artifactId>google-ads</artifactId>
			<version>38.0.0</version>
		</dependency>

		<!-- opensearch需要的依赖 开始 -->
		<dependency>
			<groupId>org.apache.httpcomponents</groupId>
			<artifactId>httpclient</artifactId>
			<version>4.5.14</version>
		</dependency>
		<dependency>
			<groupId>org.apache.httpcomponents</groupId>
			<artifactId>httpcore</artifactId>
			<version>4.4.16</version>
		</dependency>
		<dependency>
			<groupId>org.opensearch.client</groupId>
			<artifactId>opensearch-rest-high-level-client</artifactId>
			<version>2.17.0</version>
		</dependency>
		<dependency>
			<groupId>org.opensearch.client</groupId>
			<artifactId>opensearch-rest-client-sniffer</artifactId>
			<version>2.17.0</version>
		</dependency>
		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-core</artifactId>
			<version>2.17.0</version>
		</dependency>
		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-databind</artifactId>
			<version>2.17.0</version> <!-- 使用最新稳定版本 -->
		</dependency>
		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-annotations</artifactId>
			<version>2.17.0</version>
		</dependency>
		<dependency>
			<groupId>com.google.guava</groupId>
			<artifactId>guava</artifactId>
			<version>32.1.3-jre</version>
		</dependency>
		<dependency>
			<groupId>org.opensearch.client</groupId>
			<artifactId>opensearch-java</artifactId>
			<version>2.22.0</version>
		</dependency>
		<dependency>
			<groupId>org.apache.httpcomponents.client5</groupId>
			<artifactId>httpclient5</artifactId>
			<version>5.4.2</version>
		</dependency>

		<!-- opensearch需要的依赖 结束 -->

		<dependency>
			<groupId>com.github.ben-manes.caffeine</groupId>
			<artifactId>caffeine</artifactId>
			<version>3.1.8</version>
		</dependency>

		<dependency>
			<groupId>org.apache.kafka</groupId>
			<artifactId>kafka-clients</artifactId>
			<version>3.3.1</version>
		</dependency>


	</dependencies>

	<build>
		<defaultGoal>compile</defaultGoal>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<configuration>
					<fork>true</fork>
					<executable>/opt/jdk-17.0.7/bin/javac</executable>
					<source>17</source>
					<target>17</target>
				</configuration>
			</plugin>
		</plugins>
	</build>

</project>
