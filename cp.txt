/Users/<USER>/.m2/repository/junit/junit/4.12/junit-4.12.jar:/Users/<USER>/.m2/repository/org/hamcrest/hamcrest-core/1.3/hamcrest-core-1.3.jar:/Users/<USER>/.m2/repository/ru/yandex/clickhouse/clickhouse-jdbc/0.3.1-patch/clickhouse-jdbc-0.3.1-patch.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpmime/4.5.13/httpmime-4.5.13.jar:/Users/<USER>/.m2/repository/org/lz4/lz4-java/1.7.1/lz4-java-1.7.1.jar:/Users/<USER>/.m2/repository/org/slf4j/slf4j-api/1.7.30/slf4j-api-1.7.30.jar:/Users/<USER>/.m2/repository/dom4j/dom4j/1.6.1/dom4j-1.6.1.jar:/Users/<USER>/.m2/repository/com/google/code/gson/gson/2.2.4/gson-2.2.4.jar:/Users/<USER>/.m2/repository/commons-io/commons-io/2.13.0/commons-io-2.13.0.jar:/Users/<USER>/.m2/repository/commons-lang/commons-lang/2.6/commons-lang-2.6.jar:/Users/<USER>/.m2/repository/commons-dbcp/commons-dbcp/1.4/commons-dbcp-1.4.jar:/Users/<USER>/.m2/repository/commons-pool/commons-pool/1.5.4/commons-pool-1.5.4.jar:/Users/<USER>/.m2/repository/commons-configuration/commons-configuration/1.10/commons-configuration-1.10.jar:/Users/<USER>/.m2/repository/commons-logging/commons-logging/1.1.1/commons-logging-1.1.1.jar:/Users/<USER>/.m2/repository/commons-collections/commons-collections/3.2.2/commons-collections-3.2.2.jar:/Users/<USER>/.m2/repository/commons-net/commons-net/3.5/commons-net-3.5.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-compress/1.13/commons-compress-1.13.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-csv/1.14.0/commons-csv-1.14.0.jar:/Users/<USER>/.m2/repository/commons-codec/commons-codec/1.18.0/commons-codec-1.18.0.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-math3/3.6.1/commons-math3-3.6.1.jar:/Users/<USER>/.m2/repository/software/amazon/awssdk/cloudwatch/2.17.224/cloudwatch-2.17.224.jar:/Users/<USER>/.m2/repository/software/amazon/awssdk/aws-query-protocol/2.17.224/aws-query-protocol-2.17.224.jar:/Users/<USER>/.m2/repository/software/amazon/awssdk/protocol-core/2.17.224/protocol-core-2.17.224.jar:/Users/<USER>/.m2/repository/software/amazon/awssdk/sdk-core/2.17.224/sdk-core-2.17.224.jar:/Users/<USER>/.m2/repository/org/reactivestreams/reactive-streams/1.0.3/reactive-streams-1.0.3.jar:/Users/<USER>/.m2/repository/software/amazon/awssdk/http-client-spi/2.17.224/http-client-spi-2.17.224.jar:/Users/<USER>/.m2/repository/software/amazon/awssdk/annotations/2.17.224/annotations-2.17.224.jar:/Users/<USER>/.m2/repository/software/amazon/awssdk/utils/2.17.224/utils-2.17.224.jar:/Users/<USER>/.m2/repository/software/amazon/awssdk/metrics-spi/2.17.224/metrics-spi-2.17.224.jar:/Users/<USER>/.m2/repository/software/amazon/awssdk/apache-client/2.17.224/apache-client-2.17.224.jar:/Users/<USER>/.m2/repository/software/amazon/awssdk/netty-nio-client/2.17.224/netty-nio-client-2.17.224.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec-http/4.1.77.Final/netty-codec-http-4.1.77.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec-http2/4.1.77.Final/netty-codec-http2-4.1.77.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-codec/4.1.77.Final/netty-codec-4.1.77.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport/4.1.77.Final/netty-transport-4.1.77.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-resolver/4.1.77.Final/netty-resolver-4.1.77.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-handler/4.1.77.Final/netty-handler-4.1.77.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport-classes-epoll/4.1.77.Final/netty-transport-classes-epoll-4.1.77.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-transport-native-unix-common/4.1.77.Final/netty-transport-native-unix-common-4.1.77.Final.jar:/Users/<USER>/.m2/repository/software/amazon/awssdk/cloudwatchevents/2.17.224/cloudwatchevents-2.17.224.jar:/Users/<USER>/.m2/repository/software/amazon/awssdk/aws-json-protocol/2.17.224/aws-json-protocol-2.17.224.jar:/Users/<USER>/.m2/repository/software/amazon/awssdk/third-party-jackson-core/2.17.224/third-party-jackson-core-2.17.224.jar:/Users/<USER>/.m2/repository/software/amazon/awssdk/cloudwatchlogs/2.17.224/cloudwatchlogs-2.17.224.jar:/Users/<USER>/.m2/repository/software/amazon/awssdk/apigateway/2.17.224/apigateway-2.17.224.jar:/Users/<USER>/.m2/repository/software/amazon/awssdk/sts/2.17.224/sts-2.17.224.jar:/Users/<USER>/.m2/repository/software/amazon/awssdk/profiles/2.17.224/profiles-2.17.224.jar:/Users/<USER>/.m2/repository/software/amazon/awssdk/auth/2.17.224/auth-2.17.224.jar:/Users/<USER>/.m2/repository/software/amazon/awssdk/json-utils/2.17.224/json-utils-2.17.224.jar:/Users/<USER>/.m2/repository/software/amazon/eventstream/eventstream/1.0.1/eventstream-1.0.1.jar:/Users/<USER>/.m2/repository/software/amazon/awssdk/aws-core/2.17.224/aws-core-2.17.224.jar:/Users/<USER>/.m2/repository/software/amazon/awssdk/secretsmanager/2.17.224/secretsmanager-2.17.224.jar:/Users/<USER>/.m2/repository/software/amazon/awssdk/regions/2.17.224/regions-2.17.224.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-jpa/1.10.5.RELEASE/spring-data-jpa-1.10.5.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/data/spring-data-commons/1.12.5.RELEASE/spring-data-commons-1.12.5.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/spring-orm/4.2.8.RELEASE/spring-orm-4.2.8.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/spring-jdbc/4.2.8.RELEASE/spring-jdbc-4.2.8.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/spring-context/4.2.8.RELEASE/spring-context-4.2.8.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/spring-expression/4.2.8.RELEASE/spring-expression-4.2.8.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/spring-aop/4.2.8.RELEASE/spring-aop-4.2.8.RELEASE.jar:/Users/<USER>/.m2/repository/aopalliance/aopalliance/1.0/aopalliance-1.0.jar:/Users/<USER>/.m2/repository/org/springframework/spring-tx/4.2.8.RELEASE/spring-tx-4.2.8.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/spring-beans/4.2.8.RELEASE/spring-beans-4.2.8.RELEASE.jar:/Users/<USER>/.m2/repository/org/springframework/spring-core/4.2.8.RELEASE/spring-core-4.2.8.RELEASE.jar:/Users/<USER>/.m2/repository/org/aspectj/aspectjrt/1.8.9/aspectjrt-1.8.9.jar:/Users/<USER>/.m2/repository/org/slf4j/jcl-over-slf4j/1.7.21/jcl-over-slf4j-1.7.21.jar:/Users/<USER>/.m2/repository/com/alibaba/druid/1.2.18/druid-1.2.18.jar:/Users/<USER>/.m2/repository/mysql/mysql-connector-java/5.1.21/mysql-connector-java-5.1.21.jar:/Users/<USER>/.m2/repository/com/mysql/mysql-connector-j/8.0.33/mysql-connector-j-8.0.33.jar:/Users/<USER>/.m2/repository/com/facebook/fb303/1.0/fb303-1.0.jar:/Users/<USER>/.m2/repository/org/apache/thrift/1.0/thrift-1.0.jar:/Users/<USER>/.m2/repository/org/mongodb/bson/3.2.1/bson-3.2.1.jar:/Users/<USER>/.m2/repository/org/mongodb/mongo-java-driver/3.2.1/mongo-java-driver-3.2.1.jar:/Users/<USER>/.m2/repository/org/springframework/spring-web/4.3.4.RELEASE/spring-web-4.3.4.RELEASE.jar:/Users/<USER>/.m2/repository/org/apache/solr/solr-solrj/5.3.1/solr-solrj-5.3.1.jar:/Users/<USER>/.m2/repository/org/apache/zookeeper/zookeeper/3.4.6/zookeeper-3.4.6.jar:/Users/<USER>/.m2/repository/org/codehaus/woodstox/stax2-api/3.1.4/stax2-api-3.1.4.jar:/Users/<USER>/.m2/repository/org/codehaus/woodstox/woodstox-core-asl/4.4.1/woodstox-core-asl-4.4.1.jar:/Users/<USER>/.m2/repository/org/noggit/noggit/0.6/noggit-0.6.jar:/Users/<USER>/.m2/repository/org/apache/poi/poi/3.15/poi-3.15.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-collections4/4.1/commons-collections4-4.1.jar:/Users/<USER>/.m2/repository/org/apache/poi/poi-ooxml/3.15/poi-ooxml-3.15.jar:/Users/<USER>/.m2/repository/org/apache/poi/poi-ooxml-schemas/3.15/poi-ooxml-schemas-3.15.jar:/Users/<USER>/.m2/repository/org/apache/xmlbeans/xmlbeans/2.6.0/xmlbeans-2.6.0.jar:/Users/<USER>/.m2/repository/stax/stax-api/1.0.1/stax-api-1.0.1.jar:/Users/<USER>/.m2/repository/com/github/virtuald/curvesapi/1.04/curvesapi-1.04.jar:/Users/<USER>/.m2/repository/com/amazonaws/aws-java-sdk-s3/1.11.701/aws-java-sdk-s3-1.11.701.jar:/Users/<USER>/.m2/repository/com/amazonaws/aws-java-sdk-kms/1.11.701/aws-java-sdk-kms-1.11.701.jar:/Users/<USER>/.m2/repository/com/amazonaws/aws-java-sdk-core/1.11.701/aws-java-sdk-core-1.11.701.jar:/Users/<USER>/.m2/repository/software/amazon/ion/ion-java/1.0.2/ion-java-1.0.2.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-cbor/2.6.7/jackson-dataformat-cbor-2.6.7.jar:/Users/<USER>/.m2/repository/joda-time/joda-time/2.8.1/joda-time-2.8.1.jar:/Users/<USER>/.m2/repository/com/amazonaws/aws-java-sdk-sqs/1.11.701/aws-java-sdk-sqs-1.11.701.jar:/Users/<USER>/.m2/repository/com/amazonaws/aws-java-sdk-sns/1.11.701/aws-java-sdk-sns-1.11.701.jar:/Users/<USER>/.m2/repository/com/amazonaws/jmespath-java/1.11.701/jmespath-java-1.11.701.jar:/Users/<USER>/.m2/repository/com/vertica/vertica-jdbc-driver/07.00.0100/vertica-jdbc-driver-07.00.0100.jar:/Users/<USER>/.m2/repository/org/slf4j/slf4j-log4j12/1.7.21/slf4j-log4j12-1.7.21.jar:/Users/<USER>/.m2/repository/log4j/log4j/1.2.17/log4j-1.2.17.jar:/Users/<USER>/.m2/repository/ch/ethz/ganymed/ganymed-ssh2/build210/ganymed-ssh2-build210.jar:/Users/<USER>/.m2/repository/org/projectlombok/lombok/1.18.22/lombok-1.18.22.jar:/Users/<USER>/.m2/repository/com/alibaba/fastjson/2.0.44/fastjson-2.0.44.jar:/Users/<USER>/.m2/repository/com/alibaba/fastjson2/fastjson2-extension/2.0.44/fastjson2-extension-2.0.44.jar:/Users/<USER>/.m2/repository/com/alibaba/fastjson2/fastjson2/2.0.44/fastjson2-2.0.44.jar:/Users/<USER>/.m2/repository/info/debatty/java-string-similarity/1.1.0/java-string-similarity-1.1.0.jar:/Users/<USER>/.m2/repository/net/jcip/jcip-annotations/1.0/jcip-annotations-1.0.jar:/Users/<USER>/.m2/repository/com/github/mpkorstanje/simmetrics-core/4.1.1/simmetrics-core-4.1.1.jar:/Users/<USER>/.m2/repository/com/google/protobuf/protobuf-java/4.31.1/protobuf-java-4.31.1.jar:/Users/<USER>/.m2/repository/com/google/apis/google-api-services-youtube/v3-rev182-1.22.0/google-api-services-youtube-v3-rev182-1.22.0.jar:/Users/<USER>/.m2/repository/com/google/apis/google-api-services-youtubeAnalytics/v1-rev63-1.22.0/google-api-services-youtubeAnalytics-v1-rev63-1.22.0.jar:/Users/<USER>/.m2/repository/com/google/apis/google-api-services-youtubereporting/v1-rev10-1.22.0/google-api-services-youtubereporting-v1-rev10-1.22.0.jar:/Users/<USER>/.m2/repository/org/apache/lucene/lucene-analysis-kuromoji/9.11.1/lucene-analysis-kuromoji-9.11.1.jar:/Users/<USER>/.m2/repository/org/apache/lucene/lucene-core/9.11.1/lucene-core-9.11.1.jar:/Users/<USER>/.m2/repository/org/apache/lucene/lucene-analysis-common/9.11.1/lucene-analysis-common-9.11.1.jar:/Users/<USER>/.m2/repository/org/apache/lucene/lucene-analysis-stempel/9.11.1/lucene-analysis-stempel-9.11.1.jar:/Users/<USER>/.m2/repository/org/apache/lucene/lucene-analysis-nori/9.11.1/lucene-analysis-nori-9.11.1.jar:/Users/<USER>/.m2/repository/org/apache/lucene/lucene-analysis-smartcn/9.11.1/lucene-analysis-smartcn-9.11.1.jar:/Users/<USER>/.m2/repository/org/apache/kafka/kafka_2.10/0.10.2.2/kafka_2.10-0.10.2.2.jar:/Users/<USER>/.m2/repository/net/sf/jopt-simple/jopt-simple/5.0.3/jopt-simple-5.0.3.jar:/Users/<USER>/.m2/repository/com/yammer/metrics/metrics-core/2.2.0/metrics-core-2.2.0.jar:/Users/<USER>/.m2/repository/org/scala-lang/scala-library/2.10.6/scala-library-2.10.6.jar:/Users/<USER>/.m2/repository/com/101tec/zkclient/0.10/zkclient-0.10.jar:/Users/<USER>/.m2/repository/org/jsoup/jsoup/1.18.1/jsoup-1.18.1.jar:/Users/<USER>/.m2/repository/org/apache/velocity/velocity/1.6.2/velocity-1.6.2.jar:/Users/<USER>/.m2/repository/oro/oro/2.0.8/oro-2.0.8.jar:/Users/<USER>/.m2/repository/org/springframework/spring-context-support/4.1.9.RELEASE/spring-context-support-4.1.9.RELEASE.jar:/Users/<USER>/.m2/repository/org/apache/velocity/velocity-tools/2.0/velocity-tools-2.0.jar:/Users/<USER>/.m2/repository/commons-beanutils/commons-beanutils/1.7.0/commons-beanutils-1.7.0.jar:/Users/<USER>/.m2/repository/commons-digester/commons-digester/1.8/commons-digester-1.8.jar:/Users/<USER>/.m2/repository/commons-chain/commons-chain/1.1/commons-chain-1.1.jar:/Users/<USER>/.m2/repository/commons-validator/commons-validator/1.3.1/commons-validator-1.3.1.jar:/Users/<USER>/.m2/repository/sslext/sslext/1.2-0/sslext-1.2-0.jar:/Users/<USER>/.m2/repository/org/apache/struts/struts-core/1.3.8/struts-core-1.3.8.jar:/Users/<USER>/.m2/repository/antlr/antlr/2.7.2/antlr-2.7.2.jar:/Users/<USER>/.m2/repository/org/apache/struts/struts-taglib/1.3.8/struts-taglib-1.3.8.jar:/Users/<USER>/.m2/repository/org/apache/struts/struts-tiles/1.3.8/struts-tiles-1.3.8.jar:/Users/<USER>/.m2/repository/org/ogema/widgets/widget-collection/2.2.0/widget-collection-2.2.0.jar:/Users/<USER>/.m2/repository/org/osgi/org.osgi.core/5.0.0/org.osgi.core-5.0.0.jar:/Users/<USER>/.m2/repository/org/osgi/org.osgi.compendium/5.0.0/org.osgi.compendium-5.0.0.jar:/Users/<USER>/.m2/repository/org/apache/felix/org.apache.felix.scr.annotations/1.9.12/org.apache.felix.scr.annotations-1.9.12.jar:/Users/<USER>/.m2/repository/org/ogema/core/api/2.2.0/api-2.2.0.jar:/Users/<USER>/.m2/repository/javax/servlet/servlet-api/2.4/servlet-api-2.4.jar:/Users/<USER>/.m2/repository/org/ogema/core/models/2.2.0/models-2.2.0.jar:/Users/<USER>/.m2/repository/org/ogema/widgets/ogema-js-bundle/2.2.0/ogema-js-bundle-2.2.0.jar:/Users/<USER>/.m2/repository/org/ogema/ref-impl/internal-api/2.2.0/internal-api-2.2.0.jar:/Users/<USER>/.m2/repository/org/ogema/tools/resource-utils/2.2.0/resource-utils-2.2.0.jar:/Users/<USER>/.m2/repository/org/ogema/tools/memory-timeseries/2.2.0/memory-timeseries-2.2.0.jar:/Users/<USER>/.m2/repository/org/ogema/widgets/ogema-gui-api/2.2.0/ogema-gui-api-2.2.0.jar:/Users/<USER>/.m2/repository/org/apache/felix/org.apache.felix.gogo.runtime/1.0.6/org.apache.felix.gogo.runtime-1.0.6.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-lang3/3.5/commons-lang3-3.5.jar:/Users/<USER>/.m2/repository/commons-fileupload/commons-fileupload/1.3.1/commons-fileupload-1.3.1.jar:/Users/<USER>/.m2/repository/net/dongliu/requests/4.18.1/requests-4.18.1.jar:/Users/<USER>/.m2/repository/net/java/dev/jets3t/jets3t/0.9.0/jets3t-0.9.0.jar:/Users/<USER>/.m2/repository/com/jamesmurty/utils/java-xmlbuilder/0.4/java-xmlbuilder-0.4.jar:/Users/<USER>/.m2/repository/com/google/cloud/google-cloud-bigquery/2.50.1/google-cloud-bigquery-2.50.1.jar:/Users/<USER>/.m2/repository/com/google/cloud/google-cloud-core/2.56.0/google-cloud-core-2.56.0.jar:/Users/<USER>/.m2/repository/com/google/protobuf/protobuf-java-util/4.29.4/protobuf-java-util-4.29.4.jar:/Users/<USER>/.m2/repository/com/google/api/grpc/proto-google-common-protos/2.57.0/proto-google-common-protos-2.57.0.jar:/Users/<USER>/.m2/repository/com/google/api/grpc/proto-google-iam-v1/1.52.0/proto-google-iam-v1-1.52.0.jar:/Users/<USER>/.m2/repository/org/threeten/threetenbp/1.7.0/threetenbp-1.7.0.jar:/Users/<USER>/.m2/repository/com/google/cloud/google-cloud-core-http/2.56.0/google-cloud-core-http-2.56.0.jar:/Users/<USER>/.m2/repository/com/google/oauth-client/google-oauth-client/1.37.0/google-oauth-client-1.37.0.jar:/Users/<USER>/.m2/repository/com/google/http-client/google-http-client-apache-v2/1.47.0/google-http-client-apache-v2-1.47.0.jar:/Users/<USER>/.m2/repository/com/google/http-client/google-http-client-appengine/1.47.0/google-http-client-appengine-1.47.0.jar:/Users/<USER>/.m2/repository/com/google/api/gax-httpjson/2.66.0/gax-httpjson-2.66.0.jar:/Users/<USER>/.m2/repository/io/opencensus/opencensus-api/0.31.1/opencensus-api-0.31.1.jar:/Users/<USER>/.m2/repository/io/opencensus/opencensus-contrib-http-util/0.31.1/opencensus-contrib-http-util-0.31.1.jar:/Users/<USER>/.m2/repository/com/google/j2objc/j2objc-annotations/3.0.0/j2objc-annotations-3.0.0.jar:/Users/<USER>/.m2/repository/io/grpc/grpc-context/1.70.0/grpc-context-1.70.0.jar:/Users/<USER>/.m2/repository/org/checkerframework/checker-compat-qual/2.5.6/checker-compat-qual-2.5.6.jar:/Users/<USER>/.m2/repository/org/checkerframework/checker-qual/3.49.0/checker-qual-3.49.0.jar:/Users/<USER>/.m2/repository/com/google/auth/google-auth-library-credentials/1.35.0/google-auth-library-credentials-1.35.0.jar:/Users/<USER>/.m2/repository/com/google/auth/google-auth-library-oauth2-http/1.35.0/google-auth-library-oauth2-http-1.35.0.jar:/Users/<USER>/.m2/repository/com/google/code/findbugs/jsr305/3.0.2/jsr305-3.0.2.jar:/Users/<USER>/.m2/repository/com/google/apis/google-api-services-bigquery/v2-rev20250427-2.0.0/google-api-services-bigquery-v2-rev20250427-2.0.0.jar:/Users/<USER>/.m2/repository/com/google/api/api-common/2.49.0/api-common-2.49.0.jar:/Users/<USER>/.m2/repository/javax/annotation/javax.annotation-api/1.3.2/javax.annotation-api-1.3.2.jar:/Users/<USER>/.m2/repository/com/google/guava/failureaccess/1.0.2/failureaccess-1.0.2.jar:/Users/<USER>/.m2/repository/com/google/guava/listenablefuture/9999.0-empty-to-avoid-conflict-with-guava/listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar:/Users/<USER>/.m2/repository/com/google/api/gax/2.66.0/gax-2.66.0.jar:/Users/<USER>/.m2/repository/org/threeten/threeten-extra/1.8.0/threeten-extra-1.8.0.jar:/Users/<USER>/.m2/repository/com/google/cloud/google-cloud-bigquerystorage/3.14.1/google-cloud-bigquerystorage-3.14.1.jar:/Users/<USER>/.m2/repository/io/grpc/grpc-api/1.70.0/grpc-api-1.70.0.jar:/Users/<USER>/.m2/repository/io/grpc/grpc-stub/1.70.0/grpc-stub-1.70.0.jar:/Users/<USER>/.m2/repository/org/codehaus/mojo/animal-sniffer-annotations/1.24/animal-sniffer-annotations-1.24.jar:/Users/<USER>/.m2/repository/io/grpc/grpc-protobuf/1.70.0/grpc-protobuf-1.70.0.jar:/Users/<USER>/.m2/repository/io/grpc/grpc-protobuf-lite/1.70.0/grpc-protobuf-lite-1.70.0.jar:/Users/<USER>/.m2/repository/io/grpc/grpc-util/1.70.0/grpc-util-1.70.0.jar:/Users/<USER>/.m2/repository/io/grpc/grpc-core/1.70.0/grpc-core-1.70.0.jar:/Users/<USER>/.m2/repository/com/google/android/annotations/4.1.1.4/annotations-4.1.1.4.jar:/Users/<USER>/.m2/repository/io/perfmark/perfmark-api/0.27.0/perfmark-api-0.27.0.jar:/Users/<USER>/.m2/repository/com/google/auto/value/auto-value/1.11.0/auto-value-1.11.0.jar:/Users/<USER>/.m2/repository/com/google/api/grpc/proto-google-cloud-bigquerystorage-v1beta/3.14.1/proto-google-cloud-bigquerystorage-v1beta-3.14.1.jar:/Users/<USER>/.m2/repository/com/google/api/grpc/proto-google-cloud-bigquerystorage-v1alpha/3.14.1/proto-google-cloud-bigquerystorage-v1alpha-3.14.1.jar:/Users/<USER>/.m2/repository/com/google/api/grpc/proto-google-cloud-bigquerystorage-v1beta1/0.186.1/proto-google-cloud-bigquerystorage-v1beta1-0.186.1.jar:/Users/<USER>/.m2/repository/com/google/api/grpc/proto-google-cloud-bigquerystorage-v1beta2/0.186.1/proto-google-cloud-bigquerystorage-v1beta2-0.186.1.jar:/Users/<USER>/.m2/repository/com/google/api/gax-grpc/2.66.0/gax-grpc-2.66.0.jar:/Users/<USER>/.m2/repository/io/grpc/grpc-inprocess/1.70.0/grpc-inprocess-1.70.0.jar:/Users/<USER>/.m2/repository/io/grpc/grpc-alts/1.70.0/grpc-alts-1.70.0.jar:/Users/<USER>/.m2/repository/io/grpc/grpc-grpclb/1.70.0/grpc-grpclb-1.70.0.jar:/Users/<USER>/.m2/repository/org/conscrypt/conscrypt-openjdk-uber/2.5.2/conscrypt-openjdk-uber-2.5.2.jar:/Users/<USER>/.m2/repository/io/grpc/grpc-auth/1.70.0/grpc-auth-1.70.0.jar:/Users/<USER>/.m2/repository/io/grpc/grpc-netty-shaded/1.70.0/grpc-netty-shaded-1.70.0.jar:/Users/<USER>/.m2/repository/io/grpc/grpc-googleapis/1.70.0/grpc-googleapis-1.70.0.jar:/Users/<USER>/.m2/repository/org/json/json/20250107/json-20250107.jar:/Users/<USER>/.m2/repository/io/opentelemetry/opentelemetry-api/1.47.0/opentelemetry-api-1.47.0.jar:/Users/<USER>/.m2/repository/io/opentelemetry/opentelemetry-context/1.47.0/opentelemetry-context-1.47.0.jar:/Users/<USER>/.m2/repository/com/google/api/grpc/grpc-google-cloud-bigquerystorage-v1beta1/0.186.1/grpc-google-cloud-bigquerystorage-v1beta1-0.186.1.jar:/Users/<USER>/.m2/repository/com/google/api/grpc/grpc-google-cloud-bigquerystorage-v1beta2/0.186.1/grpc-google-cloud-bigquerystorage-v1beta2-0.186.1.jar:/Users/<USER>/.m2/repository/com/google/api/grpc/grpc-google-cloud-bigquerystorage-v1/3.14.1/grpc-google-cloud-bigquerystorage-v1-3.14.1.jar:/Users/<USER>/.m2/repository/com/google/api/grpc/proto-google-cloud-bigquerystorage-v1/3.14.1/proto-google-cloud-bigquerystorage-v1-3.14.1.jar:/Users/<USER>/.m2/repository/org/apache/arrow/arrow-vector/15.0.2/arrow-vector-15.0.2.jar:/Users/<USER>/.m2/repository/org/apache/arrow/arrow-format/15.0.2/arrow-format-15.0.2.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/datatype/jackson-datatype-jsr310/2.18.2/jackson-datatype-jsr310-2.18.2.jar:/Users/<USER>/.m2/repository/com/google/flatbuffers/flatbuffers-java/23.5.26/flatbuffers-java-23.5.26.jar:/Users/<USER>/.m2/repository/org/eclipse/collections/eclipse-collections/11.1.0/eclipse-collections-11.1.0.jar:/Users/<USER>/.m2/repository/org/eclipse/collections/eclipse-collections-api/11.1.0/eclipse-collections-api-11.1.0.jar:/Users/<USER>/.m2/repository/org/apache/arrow/arrow-memory-core/15.0.2/arrow-memory-core-15.0.2.jar:/Users/<USER>/.m2/repository/org/apache/arrow/arrow-memory-netty/15.0.2/arrow-memory-netty-15.0.2.jar:/Users/<USER>/.m2/repository/io/netty/netty-common/4.2.1.Final/netty-common-4.2.1.Final.jar:/Users/<USER>/.m2/repository/io/netty/netty-buffer/4.2.1.Final/netty-buffer-4.2.1.Final.jar:/Users/<USER>/.m2/repository/com/google/errorprone/error_prone_annotations/2.38.0/error_prone_annotations-2.38.0.jar:/Users/<USER>/.m2/repository/com/google/api-client/google-api-client/2.7.1/google-api-client-2.7.1.jar:/Users/<USER>/.m2/repository/com/google/oauth-client/google-oauth-client-jetty/1.32.1/google-oauth-client-jetty-1.32.1.jar:/Users/<USER>/.m2/repository/com/google/apis/google-api-services-gmail/v1-rev110-1.25.0/google-api-services-gmail-v1-rev110-1.25.0.jar:/Users/<USER>/.m2/repository/com/google/cloud/google-cloud-storage/2.52.3/google-cloud-storage-2.52.3.jar:/Users/<USER>/.m2/repository/com/google/http-client/google-http-client-jackson2/1.47.0/google-http-client-jackson2-1.47.0.jar:/Users/<USER>/.m2/repository/com/google/apis/google-api-services-storage/v1-rev20250509-2.0.0/google-api-services-storage-v1-rev20250509-2.0.0.jar:/Users/<USER>/.m2/repository/com/google/auto/value/auto-value-annotations/1.11.0/auto-value-annotations-1.11.0.jar:/Users/<USER>/.m2/repository/com/google/cloud/google-cloud-core-grpc/2.56.0/google-cloud-core-grpc-2.56.0.jar:/Users/<USER>/.m2/repository/com/google/api/grpc/proto-google-cloud-storage-v2/2.52.3/proto-google-cloud-storage-v2-2.52.3.jar:/Users/<USER>/.m2/repository/com/google/api/grpc/grpc-google-cloud-storage-v2/2.52.3/grpc-google-cloud-storage-v2-2.52.3.jar:/Users/<USER>/.m2/repository/com/google/api/grpc/gapic-google-cloud-storage-v2/2.52.3/gapic-google-cloud-storage-v2-2.52.3.jar:/Users/<USER>/.m2/repository/io/opentelemetry/opentelemetry-sdk/1.47.0/opentelemetry-sdk-1.47.0.jar:/Users/<USER>/.m2/repository/io/opentelemetry/opentelemetry-sdk-trace/1.47.0/opentelemetry-sdk-trace-1.47.0.jar:/Users/<USER>/.m2/repository/io/opentelemetry/opentelemetry-sdk-logs/1.47.0/opentelemetry-sdk-logs-1.47.0.jar:/Users/<USER>/.m2/repository/io/grpc/grpc-opentelemetry/1.70.0/grpc-opentelemetry-1.70.0.jar:/Users/<USER>/.m2/repository/io/opentelemetry/opentelemetry-sdk-metrics/1.47.0/opentelemetry-sdk-metrics-1.47.0.jar:/Users/<USER>/.m2/repository/io/opentelemetry/opentelemetry-sdk-common/1.47.0/opentelemetry-sdk-common-1.47.0.jar:/Users/<USER>/.m2/repository/io/opentelemetry/opentelemetry-sdk-extension-autoconfigure-spi/1.47.0/opentelemetry-sdk-extension-autoconfigure-spi-1.47.0.jar:/Users/<USER>/.m2/repository/io/opentelemetry/semconv/opentelemetry-semconv/1.29.0-alpha/opentelemetry-semconv-1.29.0-alpha.jar:/Users/<USER>/.m2/repository/com/google/cloud/opentelemetry/exporter-metrics/0.33.0/exporter-metrics-0.33.0.jar:/Users/<USER>/.m2/repository/com/google/cloud/google-cloud-monitoring/3.64.0/google-cloud-monitoring-3.64.0.jar:/Users/<USER>/.m2/repository/com/google/api/grpc/proto-google-cloud-monitoring-v3/3.64.0/proto-google-cloud-monitoring-v3-3.64.0.jar:/Users/<USER>/.m2/repository/com/google/cloud/opentelemetry/shared-resourcemapping/0.33.0/shared-resourcemapping-0.33.0.jar:/Users/<USER>/.m2/repository/io/opentelemetry/contrib/opentelemetry-gcp-resources/1.37.0-alpha/opentelemetry-gcp-resources-1.37.0-alpha.jar:/Users/<USER>/.m2/repository/com/google/cloud/opentelemetry/detector-resources-support/0.33.0/detector-resources-support-0.33.0.jar:/Users/<USER>/.m2/repository/io/grpc/grpc-xds/1.70.0/grpc-xds-1.70.0.jar:/Users/<USER>/.m2/repository/io/grpc/grpc-services/1.70.0/grpc-services-1.70.0.jar:/Users/<USER>/.m2/repository/com/google/re2j/re2j/1.7/re2j-1.7.jar:/Users/<USER>/.m2/repository/io/grpc/grpc-rls/1.70.0/grpc-rls-1.70.0.jar:/Users/<USER>/.m2/repository/com/google/oauth-client/google-oauth-client-java6/1.33.0/google-oauth-client-java6-1.33.0.jar:/Users/<USER>/.m2/repository/com/google/http-client/google-http-client-gson/1.47.0/google-http-client-gson-1.47.0.jar:/Users/<USER>/.m2/repository/com/google/http-client/google-http-client/1.47.0/google-http-client-1.47.0.jar:/Users/<USER>/.m2/repository/com/google/apis/google-api-services-compute/v1-rev214-1.25.0/google-api-services-compute-v1-rev214-1.25.0.jar:/Users/<USER>/.m2/repository/net/sourceforge/javacsv/javacsv/2.0/javacsv-2.0.jar:/Users/<USER>/.m2/repository/redis/clients/jedis/3.1.0/jedis-3.1.0.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-pool2/2.6.2/commons-pool2-2.6.2.jar:/Users/<USER>/.m2/repository/com/squareup/okhttp3/okhttp/4.10.0/okhttp-4.10.0.jar:/Users/<USER>/.m2/repository/com/squareup/okio/okio-jvm/3.0.0/okio-jvm-3.0.0.jar:/Users/<USER>/.m2/repository/org/jetbrains/kotlin/kotlin-stdlib-jdk8/1.5.31/kotlin-stdlib-jdk8-1.5.31.jar:/Users/<USER>/.m2/repository/org/jetbrains/kotlin/kotlin-stdlib-jdk7/1.5.31/kotlin-stdlib-jdk7-1.5.31.jar:/Users/<USER>/.m2/repository/org/jetbrains/kotlin/kotlin-stdlib-common/1.5.31/kotlin-stdlib-common-1.5.31.jar:/Users/<USER>/.m2/repository/org/jetbrains/kotlin/kotlin-stdlib/1.6.20/kotlin-stdlib-1.6.20.jar:/Users/<USER>/.m2/repository/org/jetbrains/annotations/13.0/annotations-13.0.jar:/Users/<USER>/.m2/repository/com/github/houbb/opencc4j/1.0.3/opencc4j-1.0.3.jar:/Users/<USER>/.m2/repository/com/huaban/jieba-analysis/1.0.2/jieba-analysis-1.0.2.jar:/Users/<USER>/.m2/repository/com/jcraft/jsch/0.1.55/jsch-0.1.55.jar:/Users/<USER>/.m2/repository/com/opencsv/opencsv/4.4/opencsv-4.4.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-text/1.3/commons-text-1.3.jar:/Users/<USER>/.m2/repository/com/amazonaws/aws-java-sdk-sts/1.11.714/aws-java-sdk-sts-1.11.714.jar:/Users/<USER>/.m2/repository/cn/hutool/hutool-all/5.8.11/hutool-all-5.8.11.jar:/Users/<USER>/.m2/repository/com/sun/mail/javax.mail/1.5.0/javax.mail-1.5.0.jar:/Users/<USER>/.m2/repository/javax/activation/activation/1.1/activation-1.1.jar:/Users/<USER>/.m2/repository/com/aliyun/aliyun-java-sdk-core/4.5.17/aliyun-java-sdk-core-4.5.17.jar:/Users/<USER>/.m2/repository/javax/xml/bind/jaxb-api/2.3.1/jaxb-api-2.3.1.jar:/Users/<USER>/.m2/repository/org/jacoco/org.jacoco.agent/0.8.6/org.jacoco.agent-0.8.6-runtime.jar:/Users/<USER>/.m2/repository/org/ini4j/ini4j/0.5.4/ini4j-0.5.4.jar:/Users/<USER>/.m2/repository/io/opentracing/opentracing-api/0.33.0/opentracing-api-0.33.0.jar:/Users/<USER>/.m2/repository/io/opentracing/opentracing-util/0.33.0/opentracing-util-0.33.0.jar:/Users/<USER>/.m2/repository/io/opentracing/opentracing-noop/0.33.0/opentracing-noop-0.33.0.jar:/Users/<USER>/.m2/repository/com/aliyun/aliyun-java-sdk-ecs/4.22.2/aliyun-java-sdk-ecs-4.22.2.jar:/Users/<USER>/.m2/repository/com/aliyun/oss/aliyun-sdk-oss/3.17.4/aliyun-sdk-oss-3.17.4.jar:/Users/<USER>/.m2/repository/org/jdom/jdom2/2.0.6.1/jdom2-2.0.6.1.jar:/Users/<USER>/.m2/repository/org/codehaus/jettison/jettison/1.5.4/jettison-1.5.4.jar:/Users/<USER>/.m2/repository/com/aliyun/aliyun-java-sdk-ram/3.1.0/aliyun-java-sdk-ram-3.1.0.jar:/Users/<USER>/.m2/repository/com/aliyun/aliyun-java-sdk-kms/2.11.0/aliyun-java-sdk-kms-2.11.0.jar:/Users/<USER>/.m2/repository/org/apache/parquet/parquet-avro/1.11.1/parquet-avro-1.11.1.jar:/Users/<USER>/.m2/repository/org/apache/parquet/parquet-format-structures/1.11.1/parquet-format-structures-1.11.1.jar:/Users/<USER>/.m2/repository/org/apache/avro/avro/1.9.2/avro-1.9.2.jar:/Users/<USER>/.m2/repository/org/apache/parquet/parquet-hadoop/1.11.1/parquet-hadoop-1.11.1.jar:/Users/<USER>/.m2/repository/org/apache/parquet/parquet-jackson/1.11.1/parquet-jackson-1.11.1.jar:/Users/<USER>/.m2/repository/org/xerial/snappy/snappy-java/1.1.7.3/snappy-java-1.1.7.3.jar:/Users/<USER>/.m2/repository/org/apache/parquet/parquet-column/1.11.1/parquet-column-1.11.1.jar:/Users/<USER>/.m2/repository/org/apache/parquet/parquet-encoding/1.11.1/parquet-encoding-1.11.1.jar:/Users/<USER>/.m2/repository/org/apache/parquet/parquet-common/1.11.1/parquet-common-1.11.1.jar:/Users/<USER>/.m2/repository/org/apache/yetus/audience-annotations/0.11.0/audience-annotations-0.11.0.jar:/Users/<USER>/.m2/repository/org/apache/hadoop/hadoop-common/3.3.0/hadoop-common-3.3.0.jar:/Users/<USER>/.m2/repository/org/apache/hadoop/thirdparty/hadoop-shaded-protobuf_3_7/1.0.0/hadoop-shaded-protobuf_3_7-1.0.0.jar:/Users/<USER>/.m2/repository/org/apache/hadoop/hadoop-annotations/3.3.0/hadoop-annotations-3.3.0.jar:/Users/<USER>/.m2/repository/commons-cli/commons-cli/1.2/commons-cli-1.2.jar:/Users/<USER>/.m2/repository/javax/servlet/javax.servlet-api/3.1.0/javax.servlet-api-3.1.0.jar:/Users/<USER>/.m2/repository/javax/activation/javax.activation-api/1.2.0/javax.activation-api-1.2.0.jar:/Users/<USER>/.m2/repository/org/eclipse/jetty/jetty-server/9.4.20.v20190813/jetty-server-9.4.20.v20190813.jar:/Users/<USER>/.m2/repository/org/eclipse/jetty/jetty-http/9.4.20.v20190813/jetty-http-9.4.20.v20190813.jar:/Users/<USER>/.m2/repository/org/eclipse/jetty/jetty-io/9.4.20.v20190813/jetty-io-9.4.20.v20190813.jar:/Users/<USER>/.m2/repository/org/eclipse/jetty/jetty-util/9.4.20.v20190813/jetty-util-9.4.20.v20190813.jar:/Users/<USER>/.m2/repository/org/eclipse/jetty/jetty-servlet/9.4.20.v20190813/jetty-servlet-9.4.20.v20190813.jar:/Users/<USER>/.m2/repository/org/eclipse/jetty/jetty-security/9.4.20.v20190813/jetty-security-9.4.20.v20190813.jar:/Users/<USER>/.m2/repository/org/eclipse/jetty/jetty-webapp/9.4.20.v20190813/jetty-webapp-9.4.20.v20190813.jar:/Users/<USER>/.m2/repository/org/eclipse/jetty/jetty-xml/9.4.20.v20190813/jetty-xml-9.4.20.v20190813.jar:/Users/<USER>/.m2/repository/javax/servlet/jsp/jsp-api/2.1/jsp-api-2.1.jar:/Users/<USER>/.m2/repository/com/sun/jersey/jersey-core/1.19/jersey-core-1.19.jar:/Users/<USER>/.m2/repository/javax/ws/rs/jsr311-api/1.1.1/jsr311-api-1.1.1.jar:/Users/<USER>/.m2/repository/com/sun/jersey/jersey-servlet/1.19/jersey-servlet-1.19.jar:/Users/<USER>/.m2/repository/com/sun/jersey/jersey-json/1.19/jersey-json-1.19.jar:/Users/<USER>/.m2/repository/com/sun/xml/bind/jaxb-impl/2.2.3-1/jaxb-impl-2.2.3-1.jar:/Users/<USER>/.m2/repository/org/codehaus/jackson/jackson-core-asl/1.9.2/jackson-core-asl-1.9.2.jar:/Users/<USER>/.m2/repository/org/codehaus/jackson/jackson-mapper-asl/1.9.2/jackson-mapper-asl-1.9.2.jar:/Users/<USER>/.m2/repository/org/codehaus/jackson/jackson-jaxrs/1.9.2/jackson-jaxrs-1.9.2.jar:/Users/<USER>/.m2/repository/org/codehaus/jackson/jackson-xc/1.9.2/jackson-xc-1.9.2.jar:/Users/<USER>/.m2/repository/com/sun/jersey/jersey-server/1.19/jersey-server-1.19.jar:/Users/<USER>/.m2/repository/org/apache/commons/commons-configuration2/2.1.1/commons-configuration2-2.1.1.jar:/Users/<USER>/.m2/repository/org/apache/hadoop/hadoop-auth/3.3.0/hadoop-auth-3.3.0.jar:/Users/<USER>/.m2/repository/com/nimbusds/nimbus-jose-jwt/7.9/nimbus-jose-jwt-7.9.jar:/Users/<USER>/.m2/repository/com/github/stephenc/jcip/jcip-annotations/1.0-1/jcip-annotations-1.0-1.jar:/Users/<USER>/.m2/repository/net/minidev/json-smart/2.3/json-smart-2.3.jar:/Users/<USER>/.m2/repository/net/minidev/accessors-smart/1.2/accessors-smart-1.2.jar:/Users/<USER>/.m2/repository/org/ow2/asm/asm/5.0.4/asm-5.0.4.jar:/Users/<USER>/.m2/repository/org/apache/curator/curator-framework/4.2.0/curator-framework-4.2.0.jar:/Users/<USER>/.m2/repository/org/apache/kerby/kerb-simplekdc/1.0.1/kerb-simplekdc-1.0.1.jar:/Users/<USER>/.m2/repository/org/apache/kerby/kerb-client/1.0.1/kerb-client-1.0.1.jar:/Users/<USER>/.m2/repository/org/apache/kerby/kerby-config/1.0.1/kerby-config-1.0.1.jar:/Users/<USER>/.m2/repository/org/apache/kerby/kerb-common/1.0.1/kerb-common-1.0.1.jar:/Users/<USER>/.m2/repository/org/apache/kerby/kerb-crypto/1.0.1/kerb-crypto-1.0.1.jar:/Users/<USER>/.m2/repository/org/apache/kerby/kerb-util/1.0.1/kerb-util-1.0.1.jar:/Users/<USER>/.m2/repository/org/apache/kerby/token-provider/1.0.1/token-provider-1.0.1.jar:/Users/<USER>/.m2/repository/org/apache/kerby/kerb-admin/1.0.1/kerb-admin-1.0.1.jar:/Users/<USER>/.m2/repository/org/apache/kerby/kerb-server/1.0.1/kerb-server-1.0.1.jar:/Users/<USER>/.m2/repository/org/apache/kerby/kerb-identity/1.0.1/kerb-identity-1.0.1.jar:/Users/<USER>/.m2/repository/org/apache/kerby/kerby-xdr/1.0.1/kerby-xdr-1.0.1.jar:/Users/<USER>/.m2/repository/org/apache/curator/curator-client/4.2.0/curator-client-4.2.0.jar:/Users/<USER>/.m2/repository/org/apache/curator/curator-recipes/4.2.0/curator-recipes-4.2.0.jar:/Users/<USER>/.m2/repository/org/apache/htrace/htrace-core4/4.1.0-incubating/htrace-core4-4.1.0-incubating.jar:/Users/<USER>/.m2/repository/org/apache/kerby/kerb-core/1.0.1/kerb-core-1.0.1.jar:/Users/<USER>/.m2/repository/org/apache/kerby/kerby-pkix/1.0.1/kerby-pkix-1.0.1.jar:/Users/<USER>/.m2/repository/org/apache/kerby/kerby-asn1/1.0.1/kerby-asn1-1.0.1.jar:/Users/<USER>/.m2/repository/org/apache/kerby/kerby-util/1.0.1/kerby-util-1.0.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/woodstox/woodstox-core/5.0.3/woodstox-core-5.0.3.jar:/Users/<USER>/.m2/repository/dnsjava/dnsjava/2.1.7/dnsjava-2.1.7.jar:/Users/<USER>/.m2/repository/org/apache/hadoop/hadoop-mapreduce-client-core/3.3.0/hadoop-mapreduce-client-core-3.3.0.jar:/Users/<USER>/.m2/repository/org/apache/hadoop/hadoop-yarn-client/3.3.0/hadoop-yarn-client-3.3.0.jar:/Users/<USER>/.m2/repository/org/eclipse/jetty/websocket/websocket-client/9.4.20.v20190813/websocket-client-9.4.20.v20190813.jar:/Users/<USER>/.m2/repository/org/eclipse/jetty/jetty-client/9.4.20.v20190813/jetty-client-9.4.20.v20190813.jar:/Users/<USER>/.m2/repository/org/eclipse/jetty/websocket/websocket-common/9.4.20.v20190813/websocket-common-9.4.20.v20190813.jar:/Users/<USER>/.m2/repository/org/eclipse/jetty/websocket/websocket-api/9.4.20.v20190813/websocket-api-9.4.20.v20190813.jar:/Users/<USER>/.m2/repository/org/apache/hadoop/hadoop-yarn-api/3.3.0/hadoop-yarn-api-3.3.0.jar:/Users/<USER>/.m2/repository/org/jline/jline/3.9.0/jline-3.9.0.jar:/Users/<USER>/.m2/repository/org/apache/hadoop/hadoop-yarn-common/3.3.0/hadoop-yarn-common-3.3.0.jar:/Users/<USER>/.m2/repository/com/sun/jersey/jersey-client/1.19/jersey-client-1.19.jar:/Users/<USER>/.m2/repository/com/google/inject/guice/4.0/guice-4.0.jar:/Users/<USER>/.m2/repository/javax/inject/javax.inject/1/javax.inject-1.jar:/Users/<USER>/.m2/repository/com/sun/jersey/contribs/jersey-guice/1.19/jersey-guice-1.19.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/module/jackson-module-jaxb-annotations/2.10.3/jackson-module-jaxb-annotations-2.10.3.jar:/Users/<USER>/.m2/repository/jakarta/xml/bind/jakarta.xml.bind-api/2.3.2/jakarta.xml.bind-api-2.3.2.jar:/Users/<USER>/.m2/repository/jakarta/activation/jakarta.activation-api/1.2.1/jakarta.activation-api-1.2.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/jaxrs/jackson-jaxrs-json-provider/2.10.3/jackson-jaxrs-json-provider-2.10.3.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/jaxrs/jackson-jaxrs-base/2.10.3/jackson-jaxrs-base-2.10.3.jar:/Users/<USER>/.m2/repository/org/apache/hadoop/hadoop-hdfs-client/3.3.0/hadoop-hdfs-client-3.3.0.jar:/Users/<USER>/.m2/repository/com/squareup/okhttp/okhttp/2.7.5/okhttp-2.7.5.jar:/Users/<USER>/.m2/repository/com/squareup/okio/okio/1.6.0/okio-1.6.0.jar:/Users/<USER>/.m2/repository/com/google/inject/extensions/guice-servlet/4.0/guice-servlet-4.0.jar:/Users/<USER>/.m2/repository/io/netty/netty/3.10.6.Final/netty-3.10.6.Final.jar:/Users/<USER>/.m2/repository/com/backblaze/b2/b2-sdk-core/5.0.0/b2-sdk-core-5.0.0.jar:/Users/<USER>/.m2/repository/com/backblaze/b2/b2-sdk-httpclient/5.0.0/b2-sdk-httpclient-5.0.0.jar:/Users/<USER>/.m2/repository/com/github/housepower/clickhouse-native-jdbc/1.6-stable/clickhouse-native-jdbc-1.6-stable.jar:/Users/<USER>/.m2/repository/net/jpountz/lz4/lz4/1.3.0/lz4-1.3.0.jar:/Users/<USER>/.m2/repository/org/testcontainers/clickhouse/1.15.0/clickhouse-1.15.0.jar:/Users/<USER>/.m2/repository/org/testcontainers/testcontainers/1.15.0/testcontainers-1.15.0.jar:/Users/<USER>/.m2/repository/org/rnorth/duct-tape/duct-tape/1.0.8/duct-tape-1.0.8.jar:/Users/<USER>/.m2/repository/org/rnorth/visible-assertions/visible-assertions/2.1.2/visible-assertions-2.1.2.jar:/Users/<USER>/.m2/repository/com/github/docker-java/docker-java-api/3.2.5/docker-java-api-3.2.5.jar:/Users/<USER>/.m2/repository/com/github/docker-java/docker-java-transport-zerodep/3.2.5/docker-java-transport-zerodep-3.2.5.jar:/Users/<USER>/.m2/repository/com/github/docker-java/docker-java-transport/3.2.5/docker-java-transport-3.2.5.jar:/Users/<USER>/.m2/repository/org/testcontainers/jdbc/1.15.0/jdbc-1.15.0.jar:/Users/<USER>/.m2/repository/org/testcontainers/database-commons/1.15.0/database-commons-1.15.0.jar:/Users/<USER>/.m2/repository/com/googlecode/json-simple/json-simple/1.1.1/json-simple-1.1.1.jar:/Users/<USER>/.m2/repository/org/jasypt/jasypt/1.9.3/jasypt-1.9.3.jar:/Users/<USER>/.m2/repository/com/knuddels/jtokkit/0.6.1/jtokkit-0.6.1.jar:/Users/<USER>/.m2/repository/org/bouncycastle/bcpg-jdk15on/1.50/bcpg-jdk15on-1.50.jar:/Users/<USER>/.m2/repository/org/bouncycastle/bcprov-jdk15on/1.50/bcprov-jdk15on-1.50.jar:/Users/<USER>/.m2/repository/com/konghq/unirest-java/3.14.5/unirest-java-3.14.5.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpcore-nio/4.4.13/httpcore-nio-4.4.13.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpasyncclient/4.1.5/httpasyncclient-4.1.5.jar:/Users/<USER>/.m2/repository/com/google/api-ads/google-ads/38.0.0/google-ads-38.0.0.jar:/Users/<USER>/.m2/repository/com/google/api-ads/google-ads-codegen/38.0.0/google-ads-codegen-38.0.0.jar:/Users/<USER>/.m2/repository/com/google/api-ads/google-ads-stubs-lib/38.0.0/google-ads-stubs-lib-38.0.0.jar:/Users/<USER>/.m2/repository/com/google/api-ads/google-ads-stubs-v18/38.0.0/google-ads-stubs-v18-38.0.0.jar:/Users/<USER>/.m2/repository/com/google/api-ads/google-ads-stubs-v19/38.0.0/google-ads-stubs-v19-38.0.0.jar:/Users/<USER>/.m2/repository/com/google/api-ads/google-ads-stubs-v20/38.0.0/google-ads-stubs-v20-38.0.0.jar:/Users/<USER>/.m2/repository/com/squareup/javapoet/1.11.1/javapoet-1.11.1.jar:/Users/<USER>/.m2/repository/com/google/auto/service/auto-service/1.0.1/auto-service-1.0.1.jar:/Users/<USER>/.m2/repository/com/google/auto/service/auto-service-annotations/1.0.1/auto-service-annotations-1.0.1.jar:/Users/<USER>/.m2/repository/com/google/auto/auto-common/1.2/auto-common-1.2.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpclient/4.5.14/httpclient-4.5.14.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/httpcore/4.4.16/httpcore-4.4.16.jar:/Users/<USER>/.m2/repository/org/opensearch/client/opensearch-rest-high-level-client/2.17.0/opensearch-rest-high-level-client-2.17.0.jar:/Users/<USER>/.m2/repository/org/opensearch/opensearch/2.17.0/opensearch-2.17.0.jar:/Users/<USER>/.m2/repository/org/opensearch/opensearch-common/2.17.0/opensearch-common-2.17.0.jar:/Users/<USER>/.m2/repository/org/opensearch/opensearch-core/2.17.0/opensearch-core-2.17.0.jar:/Users/<USER>/.m2/repository/org/opensearch/opensearch-compress/2.17.0/opensearch-compress-2.17.0.jar:/Users/<USER>/.m2/repository/org/opensearch/opensearch-secure-sm/2.17.0/opensearch-secure-sm-2.17.0.jar:/Users/<USER>/.m2/repository/org/opensearch/opensearch-x-content/2.17.0/opensearch-x-content-2.17.0.jar:/Users/<USER>/.m2/repository/org/yaml/snakeyaml/2.1/snakeyaml-2.1.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-smile/2.17.2/jackson-dataformat-smile-2.17.2.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/dataformat/jackson-dataformat-yaml/2.17.2/jackson-dataformat-yaml-2.17.2.jar:/Users/<USER>/.m2/repository/org/opensearch/opensearch-geo/2.17.0/opensearch-geo-2.17.0.jar:/Users/<USER>/.m2/repository/org/opensearch/opensearch-telemetry/2.17.0/opensearch-telemetry-2.17.0.jar:/Users/<USER>/.m2/repository/org/apache/lucene/lucene-backward-codecs/9.11.1/lucene-backward-codecs-9.11.1.jar:/Users/<USER>/.m2/repository/org/apache/lucene/lucene-grouping/9.11.1/lucene-grouping-9.11.1.jar:/Users/<USER>/.m2/repository/org/apache/lucene/lucene-highlighter/9.11.1/lucene-highlighter-9.11.1.jar:/Users/<USER>/.m2/repository/org/apache/lucene/lucene-join/9.11.1/lucene-join-9.11.1.jar:/Users/<USER>/.m2/repository/org/apache/lucene/lucene-memory/9.11.1/lucene-memory-9.11.1.jar:/Users/<USER>/.m2/repository/org/apache/lucene/lucene-misc/9.11.1/lucene-misc-9.11.1.jar:/Users/<USER>/.m2/repository/org/apache/lucene/lucene-queries/9.11.1/lucene-queries-9.11.1.jar:/Users/<USER>/.m2/repository/org/apache/lucene/lucene-queryparser/9.11.1/lucene-queryparser-9.11.1.jar:/Users/<USER>/.m2/repository/org/apache/lucene/lucene-sandbox/9.11.1/lucene-sandbox-9.11.1.jar:/Users/<USER>/.m2/repository/org/apache/lucene/lucene-spatial-extras/9.11.1/lucene-spatial-extras-9.11.1.jar:/Users/<USER>/.m2/repository/org/apache/lucene/lucene-spatial3d/9.11.1/lucene-spatial3d-9.11.1.jar:/Users/<USER>/.m2/repository/org/apache/lucene/lucene-suggest/9.11.1/lucene-suggest-9.11.1.jar:/Users/<USER>/.m2/repository/org/opensearch/opensearch-cli/2.17.0/opensearch-cli-2.17.0.jar:/Users/<USER>/.m2/repository/com/tdunning/t-digest/3.2/t-digest-3.2.jar:/Users/<USER>/.m2/repository/org/hdrhistogram/HdrHistogram/2.2.2/HdrHistogram-2.2.2.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-api/2.21.0/log4j-api-2.21.0.jar:/Users/<USER>/.m2/repository/org/apache/logging/log4j/log4j-jul/2.21.0/log4j-jul-2.21.0.jar:/Users/<USER>/.m2/repository/net/java/dev/jna/jna/5.13.0/jna-5.13.0.jar:/Users/<USER>/.m2/repository/com/jcraft/jzlib/1.1.3/jzlib-1.1.3.jar:/Users/<USER>/.m2/repository/io/projectreactor/reactor-core/3.5.20/reactor-core-3.5.20.jar:/Users/<USER>/.m2/repository/org/roaringbitmap/RoaringBitmap/1.2.1/RoaringBitmap-1.2.1.jar:/Users/<USER>/.m2/repository/org/opensearch/client/opensearch-rest-client/2.17.0/opensearch-rest-client-2.17.0.jar:/Users/<USER>/.m2/repository/org/opensearch/plugin/mapper-extras-client/2.17.0/mapper-extras-client-2.17.0.jar:/Users/<USER>/.m2/repository/org/opensearch/plugin/parent-join-client/2.17.0/parent-join-client-2.17.0.jar:/Users/<USER>/.m2/repository/org/opensearch/plugin/aggs-matrix-stats-client/2.17.0/aggs-matrix-stats-client-2.17.0.jar:/Users/<USER>/.m2/repository/org/opensearch/plugin/rank-eval-client/2.17.0/rank-eval-client-2.17.0.jar:/Users/<USER>/.m2/repository/org/opensearch/plugin/lang-mustache-client/2.17.0/lang-mustache-client-2.17.0.jar:/Users/<USER>/.m2/repository/com/github/spullara/mustache/java/compiler/0.9.14/compiler-0.9.14.jar:/Users/<USER>/.m2/repository/org/opensearch/client/opensearch-rest-client-sniffer/2.17.0/opensearch-rest-client-sniffer-2.17.0.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-core/2.17.0/jackson-core-2.17.0.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-databind/2.17.0/jackson-databind-2.17.0.jar:/Users/<USER>/.m2/repository/net/bytebuddy/byte-buddy/1.14.9/byte-buddy-1.14.9.jar:/Users/<USER>/.m2/repository/com/fasterxml/jackson/core/jackson-annotations/2.17.0/jackson-annotations-2.17.0.jar:/Users/<USER>/.m2/repository/com/google/guava/guava/32.1.3-jre/guava-32.1.3-jre.jar:/Users/<USER>/.m2/repository/org/opensearch/client/opensearch-java/2.22.0/opensearch-java-2.22.0.jar:/Users/<USER>/.m2/repository/org/eclipse/parsson/parsson/1.1.6/parsson-1.1.6.jar:/Users/<USER>/.m2/repository/jakarta/json/jakarta.json-api/2.1.3/jakarta.json-api-2.1.3.jar:/Users/<USER>/.m2/repository/jakarta/json/bind/jakarta.json.bind-api/2.0.0/jakarta.json.bind-api-2.0.0.jar:/Users/<USER>/.m2/repository/jakarta/annotation/jakarta.annotation-api/1.3.5/jakarta.annotation-api-1.3.5.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/core5/httpcore5/5.3.3/httpcore5-5.3.3.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/core5/httpcore5-h2/5.3.3/httpcore5-h2-5.3.3.jar:/Users/<USER>/.m2/repository/org/eclipse/yasson/2.0.2/yasson-2.0.2.jar:/Users/<USER>/.m2/repository/org/glassfish/jakarta.json/2.0.0/jakarta.json-2.0.0-module.jar:/Users/<USER>/.m2/repository/org/apache/httpcomponents/client5/httpclient5/5.4.2/httpclient5-5.4.2.jar:/Users/<USER>/.m2/repository/com/github/ben-manes/caffeine/caffeine/3.1.8/caffeine-3.1.8.jar:/Users/<USER>/.m2/repository/org/apache/kafka/kafka-clients/3.3.1/kafka-clients-3.3.1.jar:/Users/<USER>/.m2/repository/com/github/luben/zstd-jni/1.5.2-1/zstd-jni-1.5.2-1.jar