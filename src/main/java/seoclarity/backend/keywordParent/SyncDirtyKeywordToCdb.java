package seoclarity.backend.keywordParent;

import com.google.gson.Gson;
import org.apache.commons.lang.BooleanUtils;
import seoclarity.backend.dao.clickhouse.dritykeyword.RgParentChildKeywordRelDao;
import seoclarity.backend.dao.rankcheck.KeywordMonthlyRecommendDAO;
import seoclarity.backend.entity.clickhouse.dirtykeyword.RgParentChildKeywordRelEntity;
import seoclarity.backend.entity.rankcheck.KeywordMonthlyRecommend;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.SpringBeanFactory;
import seoclarity.backend.utils.keywordTokenizer.SnowBallAndNgramForForeignLanguages;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class SyncDirtyKeywordToCdb {
    private static final String TMP_TABLE_NAME = "local_rg_parent_child_keyword_relNew";
    private static final Pattern pattern = Pattern.compile("'(.*?)'");
    private static final Gson gson = new Gson();


    private static Boolean isTest;
    private KeywordMonthlyRecommendDAO keywordRecommendDAO;
    private RgParentChildKeywordRelDao rgParentChildKeywordRelDao;

    public SyncDirtyKeywordToCdb() {
        keywordRecommendDAO = SpringBeanFactory.getBean("keywordMonthlyRecommendDAO");
        rgParentChildKeywordRelDao = SpringBeanFactory.getBean("rgParentChildKeywordRelDao");
    }

    public static void main(String[] args) {
        if (args.length > 0) {
            isTest = Boolean.parseBoolean(args[0]);
        }
        if (isTest == null) {
            isTest = false;
        }
        SyncDirtyKeywordToCdb sync = new SyncDirtyKeywordToCdb();
        sync.process();
    }

    private void process() {
        // todo if data count larger need limit
        List<KeywordMonthlyRecommend> allParentKw = keywordRecommendDAO.getAllParentKw(isTest);
        if (allParentKw == null || allParentKw.isEmpty()) {
            System.out.println("====allParentKw is empty");
            return;
        }
        int syncCount = 0;
        rgParentChildKeywordRelDao.createTable(TMP_TABLE_NAME);
        System.out.println("====allParentKw size:" + allParentKw.size());
        List<RgParentChildKeywordRelEntity> insertList = new ArrayList<>();
        for (KeywordMonthlyRecommend recommend : allParentKw) {
            RgParentChildKeywordRelEntity entity = createCDBEntity(recommend);
            insertList.add(entity);
            if (insertList.size() > 500) {
                if (isTest) {
                    System.out.println("====insertList size:" + insertList.size() + " data:" + gson.toJson(insertList));
                }
                rgParentChildKeywordRelDao.insertBatch(insertList, TMP_TABLE_NAME);
                syncCount += insertList.size();
                insertList.clear();
            }
        }
        if (!insertList.isEmpty()) {
            if (isTest) {
                System.out.println("====insertList size:" + insertList.size() + " data:" + gson.toJson(insertList));
            }
            rgParentChildKeywordRelDao.insertBatch(insertList, TMP_TABLE_NAME);
            syncCount += insertList.size();
            insertList.clear();
        }
        if (!isTest) {
            rgParentChildKeywordRelDao.dropTable();
            rgParentChildKeywordRelDao.renameTable(TMP_TABLE_NAME);
        }
        System.out.println("===syncInfo sourceDataCount:" + allParentKw.size() + ", syncCount:" + syncCount);
    }

    private RgParentChildKeywordRelEntity createCDBEntity(KeywordMonthlyRecommend recommend) {
        RgParentChildKeywordRelEntity entity = new RgParentChildKeywordRelEntity();
        entity.setEngineId(recommend.getSearchEngineId());
        entity.setLanguageId(recommend.getSearchLanguageId());
        entity.setChildKeywordName(FormatUtils.decodeKeyword(recommend.getChildKeywordName()));
        List<String> streamList = convertStringToList(recommend.getChildKeywordStream());
        if (streamList.isEmpty()) {
            streamList =  SnowBallAndNgramForForeignLanguages.wordStemmerTokenizer(entity.getChildKeywordName(), "English", true, SnowBallAndNgramForForeignLanguages.STOP_KEYWORD_CHAR_SET_SIMPLE);
        }
        entity.setChildKeywordStream(streamList);
        entity.setParentKeywordName(FormatUtils.decodeKeyword(recommend.getParentKeywordName()));
        return entity;
    }

    public static List<String> convertStringToList(String input) {
        List<String> result = new ArrayList<>();
        Matcher matcher = pattern.matcher(input);
        while (matcher.find()) {
            result.add(matcher.group(1));
        }
        return result;
    }

}
