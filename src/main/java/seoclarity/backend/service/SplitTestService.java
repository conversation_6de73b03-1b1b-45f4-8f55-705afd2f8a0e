package seoclarity.backend.service;

import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import okhttp3.HttpUrl;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;
import seoclarity.backend.dao.actonia.*;
import seoclarity.backend.dao.clickhouse.realtimeurl.UrlCrawlInstanceDAO;
import seoclarity.backend.entity.actonia.*;
import seoclarity.backend.entity.clickhouse.realtimeurl.UrlCrawlInstance;
import seoclarity.backend.onetime.SplitTest;
import seoclarity.backend.onetime.SplitTest.CorrelationReport;
import seoclarity.backend.sender.realtimepagecrawl.RealTimePageCrawlUrlSender;
import seoclarity.backend.utils.EventUtils;
import seoclarity.backend.utils.Md5Util;
import seoclarity.backend.utils.SpringBeanFactory;
import seoclarity.backend.utils.WorkerUtils;
import seoclarity.backend.utils.murmurhash.MurmurHashUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

@Slf4j
public class SplitTestService {

    private static final String CONTROL_GROUP_TAG_SUFFIX = "(Control Group)";
    private static final String TEST_GROUP_TAG_SUFFIX = "(Test Group)";
    private static final String WORKER_KEY_PREFIX = "event_splittester_url_";
    // event_splittester_crawlUrl_{groupId}_{oid}_{version}
    private static final String CRAWL_WORKER_KEY_PREFIX = "event_splittester_crawlUrl_";
    private static final String KEY_REGEX = "^" + WORKER_KEY_PREFIX + "(\\d+)$";
    private static final Pattern KEY_PATTERN = Pattern.compile(KEY_REGEX);
    private final UrlSplitCorrelationInstanceDAO urlSplitCorrelationInstanceDAO;
    private final UrlSplitCorrelationDetailDAO urlSplitCorrelationDetailDAO;
    private final ResourceBatchInfoEntityDAO resourceBatchInfoEntityDAO;
    private final ResourceBatchDetailEntityDAO resourceBatchDetailEntityDAO;
    private final PageOptimizationDAO pageOptimizationDAO;
    private final PageOptimizationCaRelDAO pageOptimizationCaRelDAO;
    private final ContentAssistantDAO contentAssistantDAO;
    private final GwmDomainRelDAO gwmDomainRelDAO;
    private final GroupRelationshipEntityDAO groupRelationshipEntityDAO;
    private final CdbParentChildPageRelEntityDAO cdbParentChildPageRelEntityDAO;
    private final OwnDomainEntityDAO ownDomainEntityDAO;
    private final OwnDomainSettingEntityDAO ownDomainSettingEntityDAO;
    private final UrlCrawlInstanceDAO urlCrawlInstanceDAO;

    public SplitTestService() {
        urlSplitCorrelationInstanceDAO = SpringBeanFactory.getBean("urlSplitCorrelationInstanceDAO");
        urlSplitCorrelationDetailDAO = SpringBeanFactory.getBean("urlSplitCorrelationDetailDAO");
        resourceBatchInfoEntityDAO = SpringBeanFactory.getBean("resourceBatchInfoEntityDAO");
        resourceBatchDetailEntityDAO = SpringBeanFactory.getBean("resourceBatchDetailEntityDAO");
        this.pageOptimizationDAO = SpringBeanFactory.getBean("pageOptimizationDAO");
        this.pageOptimizationCaRelDAO = SpringBeanFactory.getBean("pageOptimizationCaRelDAO");
        this.contentAssistantDAO = SpringBeanFactory.getBean("contentAssistantDAO");
        gwmDomainRelDAO = SpringBeanFactory.getBean("gwmDomainRelDAO");
        groupRelationshipEntityDAO = SpringBeanFactory.getBean("groupRelationshipEntityDAO");
        cdbParentChildPageRelEntityDAO = SpringBeanFactory.getBean("cdbParentChildPageRelEntityDAO");
        ownDomainEntityDAO = SpringBeanFactory.getBean("ownDomainEntityDAO");
        ownDomainSettingEntityDAO = SpringBeanFactory.getBean("ownDomainSettingEntityDAO");
        urlCrawlInstanceDAO = SpringBeanFactory.getBean("urlCrawlInstanceDAO");
    }

    public static void main(String[] args) throws Exception {
        SplitTestService splitTestService = new SplitTestService();
        while (true) {
            final List<String> cacheList;
            try {
                cacheList = WorkerUtils.getCacheList(WorkerUtils.CACHE_LIST_URL + WORKER_KEY_PREFIX);
            } catch (Exception e) {
                log.error("get cache list error: {}", e.getMessage());
                Thread.sleep(60_000);
                continue;
            }
            if (CollectionUtils.isEmpty(cacheList)) {
                log.warn("not found cache need to start");
                Thread.sleep(60_000);
                continue;
            }
            final String key = cacheList.get(0);
            log.info("key: {}", key);
            final Matcher matcher = KEY_PATTERN.matcher(key);
            if (!matcher.matches()) {
                log.warn("key: {} not match pattern: {}", key, KEY_REGEX);
                continue;
            }
            final int instanceId = Integer.parseInt(matcher.group(1));
            try {
                splitTestService.start(instanceId);
            } catch (Exception e) {
                log.error("instanceId: {}, start error: {}", instanceId, e.getMessage(), e);
            }
            // delete key
            EventUtils.deleteKey(key);
            Thread.sleep(60_000);
        }
    }

    private static ResourceBatchInfoEntity createResourceBatchInfoEntity(int domainId) {
        final ResourceBatchInfoEntity resourceBatchInfoEntity = new ResourceBatchInfoEntity();
        resourceBatchInfoEntity.setActionType(ResourceBatchInfoEntity.TYPE_ADD);
        resourceBatchInfoEntity.setOwnDomainId(domainId);
        resourceBatchInfoEntity.setCreateDate(new Date());
        resourceBatchInfoEntity.setOperationType(ResourceBatchInfoEntity.OPERATION_TYPE_BATCH_ADD_TARGET_URL_OPTIONAL_TAG);
        resourceBatchInfoEntity.setUserId(214);
        resourceBatchInfoEntity.setStatus(ResourceBatchInfoEntity.STATUS_CREATED);
        return resourceBatchInfoEntity;
    }

    private void start(int instanceId) {
        // 1. get enabled url_split_correlation_instance but status is not started.
        final UrlSplitCorrelationInstance urlSplitCorrelationInstance = this.urlSplitCorrelationInstanceDAO.getById(instanceId);
        log.info("instance: {}", urlSplitCorrelationInstance);
        if (urlSplitCorrelationInstance == null) {
            log.warn("not found instance need to start");
            return;
        }
        log.info("instanceId: {} need to start", instanceId);
        int domainId = urlSplitCorrelationInstance.getOwnDomainId();
        // check page_optimization exist
        final long groupId = urlSplitCorrelationInstance.getGroupId();
        final PageOptimization pageOptimization = this.pageOptimizationDAO.fetchById(groupId);
        if (pageOptimization == null) {
            // pageOptimization not found means this task was deleted by user
            log.warn("instanceId: {}, pageOptimization not found", instanceId);
            return;
        } else {
            log.info("pageOptimization: {}", pageOptimization);
        }
        // 2. create metrics params
        final SplitTest.MetricsParams metricsParams = urlSplitCorrelationInstance.createMetricsParams();
        // if pageOptimization.urlDataSource is 1: input URLs, need to check domain is shared domain or not
        // check domain is shared domain or not
        final Integer metricsType = metricsParams.getMetricsType();
        if (pageOptimization.getUrlDataSource() == 1) {
            if (metricsType == 1) {
                // metricsType is GSC
                final List<GWMDomainRel> relList = this.gwmDomainRelDAO.findByReferDomainId(domainId);
                if (!relList.isEmpty()) {
                    // set domainId to shared domain
                    final GWMDomainRel gwmDomainRel1 = relList.get(0);
                    metricsParams.setShareDomainId(gwmDomainRel1.getBaseDomainId() != null ? gwmDomainRel1.getBaseDomainId() : gwmDomainRel1.getOwnDomainId());
                    final List<Integer> relIds = relList.stream().map(gwmDomainRel -> gwmDomainRel.getBaseProfileId() != null ? gwmDomainRel.getBaseProfileId() : gwmDomainRel.getId()).filter(Objects::nonNull).toList();
                    StringBuilder stringBuilder = new StringBuilder();
                    for (Integer relId : relIds) {
                        stringBuilder.append(relId).append(",");
                    }
                    metricsParams.setRelIds(stringBuilder.substring(0, stringBuilder.length() - 1));
                }
                // check this domain has parent child urls or not
                final boolean hasParentChildUrls = this.cdbParentChildPageRelEntityDAO.hasParentChildURLs(domainId);
                metricsParams.setEnableParentChildRel(hasParentChildUrls);
            } else {
                // metricsType is GA
                final GroupRelationshipEntity groupRelationshipEntity = this.groupRelationshipEntityDAO.get(domainId);
                int shareDomainId = domainId;
                if (groupRelationshipEntity != null) {
                    shareDomainId = groupRelationshipEntity.getGroupId();
                    metricsParams.setShareDomainId(shareDomainId);
                }
                // get traffic type from own_domain_setting
                final OwnDomainSettingEntity ownDomainSettingEntity = this.ownDomainSettingEntityDAO.getSettingByDomainId(shareDomainId);
                if (ownDomainSettingEntity != null && ownDomainSettingEntity.getTrafficType() != null) {
                    metricsParams.setDataSourceType(ownDomainSettingEntity.getTrafficType());
                }
                // get google_analytics_version from own_domain
                final OwnDomainEntity ownDomainEntity = this.ownDomainEntityDAO.getById(shareDomainId);
                if (ownDomainEntity != null && ownDomainEntity.getGoogleAnalyticsVersion() != null) {
                    metricsParams.setGoogleAnalyticsVersion(ownDomainEntity.getGoogleAnalyticsVersion());
                }
            }
        }
        final SplitTest.UrlFilterParam urlFilterParam = new SplitTest.UrlFilterParam();
        List<UrlSplitCorrelationDetail> urlSplitCorrelationDetails = new ArrayList<>();
        final String filterSqlCondition = urlSplitCorrelationInstance.getFilterSqlCondition();
        final boolean sqlType = StringUtils.isNotEmpty(filterSqlCondition);
        if (sqlType) {
            urlFilterParam.setFilterType("Sql");
            urlFilterParam.setSearchInput(filterSqlCondition);
        } else {
            // get urls from url_split_correlation_detail
            urlFilterParam.setFilterType("Urls");
            // use a map to store urlMurmurHash and detail for tracking child url if child url is not original url because of trailing slashes rule
            urlSplitCorrelationDetails = getNewUrlSplitCorrelationDetails(urlSplitCorrelationInstance);
            urlFilterParam.setSearchInput(urlSplitCorrelationDetails.stream()
                    .map(detail -> StringUtils.isNotBlank(detail.getBrowserMurmur3hash()) ? detail.getBrowserMurmur3hash() : detail.getUrlMurmur3Hash())
                    .collect(Collectors.joining("','", "'", "'")));
        }
        // 3. get correlation report by invoke splitTest
        metricsParams.setUrlFilter(urlFilterParam);
        log.info("instanceId: {}, metrics params: {}", instanceId, metricsParams);
        this.urlSplitCorrelationInstanceDAO.updateStatus(instanceId, UrlSplitCorrelationInstance.Status.PROCESSING);
        log.info("update status: {} to {}", instanceId, UrlSplitCorrelationInstance.Status.PROCESSING);
        // wait for splitTest finish
        CorrelationReport correlationReport;
        final int removeNonIndexableUrlsFlag = urlSplitCorrelationInstance.getRemoveNonIndexableUrlsFlag();
        try {
            SplitTest splitTest = new SplitTest(metricsParams);
            String innerSql = splitTest.getInnerSql();
            log.info("innerSql: {}", innerSql);
            // choose URL source depend on urlSplitCorrelationDetail is empty or not
            if (urlSplitCorrelationDetails.isEmpty()) {
                List<String> distinctUrlList = splitTest.getDistinctUrlList(innerSql);
                if (CollectionUtils.isEmpty(distinctUrlList)) {
                    log.warn("instanceId: {}, distinctUrlList is empty", instanceId);
                    return;
                }
                // batch insert url_split_correlation_detail by distinctUrlList
                urlSplitCorrelationDetails = distinctUrlList.stream().map(url -> {
                    final UrlSplitCorrelationDetail urlSplitCorrelationDetail = new UrlSplitCorrelationDetail();
                    urlSplitCorrelationDetail.setInstanceId(instanceId);
                    urlSplitCorrelationDetail.setOwnDomainId(domainId);
                    urlSplitCorrelationDetail.setUrl(url);
                    urlSplitCorrelationDetail.setUrlMurmur3Hash(MurmurHashUtils.getMurmurHash3_64(url));
                    urlSplitCorrelationDetail.setBrowserMurmur3hash(MurmurHashUtils.getMurmurHash3_64(encodeUrlWithOkHttp(url)));
                    return urlSplitCorrelationDetail;
                }).collect(Collectors.toList());
                this.urlSplitCorrelationDetailDAO.saveBatch(urlSplitCorrelationDetails);
                urlSplitCorrelationDetails = this.urlSplitCorrelationDetailDAO.findByInstanceId(instanceId);
                if (urlSplitCorrelationDetails.size() < distinctUrlList.size()) {
                    log.warn("instanceId: {}, distinctUrlList size: {}, urlSplitCorrelationDetails size: {}", instanceId, distinctUrlList.size(), urlSplitCorrelationDetails.size());
                    Thread.sleep(3_000);
                    urlSplitCorrelationDetails = this.urlSplitCorrelationDetailDAO.findByInstanceId(instanceId);
                }
            }
            if (removeNonIndexableUrlsFlag == 1) {
                // 1. send urls to realtime crawler
                RealTimePageCrawlUrlSender realTimePageCrawlUrlSender = new RealTimePageCrawlUrlSender();
                final int createDate = Integer.parseInt(LocalDate.now().format(DateTimeFormatter.BASIC_ISO_DATE));
                final int crawlInstanceId = realTimePageCrawlUrlSender.getInstanceId(groupId, domainId, createDate);
                final List<String> urls = urlSplitCorrelationDetails.stream().map(UrlSplitCorrelationDetail::getUrl).toList();
                realTimePageCrawlUrlSender.processUrlList(domainId, crawlInstanceId, createDate, new Date(), urls);
                final int urlSize = urls.size();
                // 2. wait for realtime crawler finish
                for(;;) {
                    try {
                        Thread.sleep(30_000);
                    } catch (InterruptedException e) {
                        log.error("Thread sleep interrupted while waiting for URL crawl.", e);
                        Thread.currentThread().interrupt();
                    }
                    final int crawledCount = this.urlCrawlInstanceDAO.countByDomainIdAndCrawlInstanceId(domainId, crawlInstanceId);
                    if (crawledCount >= urlSize) {
                        log.info("All URLs crawled. Crawled count: {}, Total size: {}", crawledCount, urlSize);
                        break;
                    }
                    log.info("Crawling progress: domainId: {}, instanceId: {}, crawledCount: {}, needCrawlCount: {}", domainId, crawlInstanceId, crawledCount, urlSize);
                }
                // 3. retrieve indexable urls
                log.info("Retrieving indexable URLs from database.");
                final List<UrlCrawlInstance> urlCrawlInstances = this.urlCrawlInstanceDAO.queryByDomainIdAndCrawlInstanceId(domainId, crawlInstanceId);
                final Map<String, UrlCrawlInstance> urlMurmur3HashToIndexable = urlCrawlInstances.stream()
                        .collect(Collectors.toMap(UrlCrawlInstance::getUrlMurmur3Hash, instance -> instance));
                // 4. update discard type = 4 for non indexable urls
                urlSplitCorrelationDetails.forEach(detail -> {
                    final String urlMurmur3Hash = detail.getUrlMurmur3Hash();
                    final Optional<UrlCrawlInstance> urlCrawlInstanceOpt = Optional.ofNullable(urlMurmur3HashToIndexable.get(urlMurmur3Hash));
                    // set discardType = 4 if indexable is not 1 or responseCode != 200
                    urlCrawlInstanceOpt.filter(instance -> instance.getIndexable() != 1 || instance.getResponseCode() != 200)
                    .ifPresent(instance -> detail.setDiscardType(UrlSplitCorrelationDetail.DISCARD_TYPE_NON_INDEXABLE));
                });
                // 5. update discard type = 4 for non indexable urls
                final List<UrlSplitCorrelationDetail> nonIndexableUrlDetails = urlSplitCorrelationDetails.stream()
                        .filter(detail -> detail.getDiscardType() == UrlSplitCorrelationDetail.DISCARD_TYPE_NOT_DISCARDED)
                        .collect(Collectors.toList());
                this.urlSplitCorrelationDetailDAO.batchUpdate(nonIndexableUrlDetails);
                log.warn("nonIndexableUrlDetails size: {}, urlSplitCorrelationDetails size: {}", nonIndexableUrlDetails.size(), urlSplitCorrelationDetails.size());
                // update innerSql for non indexable urls
                innerSql = innerSql + "AND url_murmur_hash IN ('" + nonIndexableUrlDetails.stream().map(UrlSplitCorrelationDetail::getUrlMurmur3Hash).collect(Collectors.joining("','")) + "')";
                urlSplitCorrelationDetails = this.urlSplitCorrelationDetailDAO.findByInstanceId(instanceId);
            }
            correlationReport = splitTest.start(innerSql, removeNonIndexableUrlsFlag);
        } catch (Exception e) {
            log.error("instanceId: {}, splitTest error: {}", instanceId, e.getMessage());
            throw new RuntimeException(e);
        }
        log.info("correlation report: {}", correlationReport.summary());
        // 4. update status
        log.info("update status: {} to {}", instanceId, UrlSplitCorrelationInstance.Status.COMPLETED_SUCCESSFULLY);
        this.urlSplitCorrelationInstanceDAO.saveScore(instanceId, correlationReport.getCorrelation());
        final List<SplitTest.UrlMetrics> controlGroup = correlationReport.getControlGroup();
        if (CollectionUtils.isEmpty(controlGroup)) {
            log.warn("instanceId: {}, controlGroup is empty", instanceId);
            return;
        }
        // 5. add PAGE_TAG for urls in test group and control group by QUEUE base
        final Collection<UrlSplitCorrelationDetail> urlSplitCorrelationDetailsUpdated = this.addPageTag(domainId, urlSplitCorrelationDetails, correlationReport, pageOptimization.getName());
        // update url_split_correlation_detail
        this.urlSplitCorrelationDetailDAO.batchUpdate(urlSplitCorrelationDetailsUpdated);
        // save page optimization
        final String version = this.savePageOptimization(pageOptimization, domainId, urlSplitCorrelationDetailsUpdated);
        if (removeNonIndexableUrlsFlag < 1) {
            // save worker key
            final String crawlWorkerKey = CRAWL_WORKER_KEY_PREFIX + pageOptimization.getId() + "_" + domainId + "_" + version;
            log.info("crawlWorkerKey: {}", crawlWorkerKey);
            WorkerUtils.syncEventKeyToWorkers(WorkerUtils.WORKER_BASE_URL, crawlWorkerKey, "");
        }
    }

    @SneakyThrows
    private List<UrlSplitCorrelationDetail> getNewUrlSplitCorrelationDetails(UrlSplitCorrelationInstance instance) {
        final int domainId = instance.getOwnDomainId();
        final int instanceId = instance.getId();
        final OwnDomainEntity ownDomainEntity = this.ownDomainEntityDAO.getById(domainId);
        List<UrlSplitCorrelationDetail> urlSplitCorrelationDetails = this.urlSplitCorrelationDetailDAO.findByInstanceId(instanceId);
        // create a url -> urlDetail map
        final Map<String, UrlSplitCorrelationDetail> urlDetailMap = urlSplitCorrelationDetails.stream()
                .collect(Collectors.toMap(UrlSplitCorrelationDetail::getUrl, urlSplitCorrelationDetail -> urlSplitCorrelationDetail));
        final Integer keepTrailingSlashes = ownDomainEntity.getKeepTrailingSlashes();
        final boolean removeTrailingSlashes = this.isRemoveTrailingSlashes(keepTrailingSlashes);
        Map<String, UrlSplitCorrelationDetail> urlSplitCorrelationDetailMap = new HashMap<>();
        urlSplitCorrelationDetails.forEach(urlSplitCorrelationDetail -> {
            final String url = urlSplitCorrelationDetail.getUrl();
            final String murmurHash = MurmurHashUtils.getMurmurHash3_64(formatUrlByTrailingSlashes(url, removeTrailingSlashes));
            urlSplitCorrelationDetailMap.put(murmurHash, urlSplitCorrelationDetail);
        });
        // use new murmurHashes to check if url is child url
        final List<CdbParentChildPageRelEntity> childPageRelEntities = this.cdbParentChildPageRelEntityDAO.getRelEntityByChildUrlMurmurHash(domainId, urlSplitCorrelationDetailMap.keySet());
        if (!CollectionUtils.isEmpty(childPageRelEntities)) {
            List<UrlSplitCorrelationDetail> needUpdateChildUrlDetails = new ArrayList<>(childPageRelEntities.size());
            List<UrlSplitCorrelationDetail> parentUrlDetails = new ArrayList<>(childPageRelEntities.size());
            childPageRelEntities.forEach(childPageRelEntity -> {
                final String childUrlMurmur3Hash = childPageRelEntity.getChildUrlMurmur3Hash();
                final UrlSplitCorrelationDetail urlSplitCorrelationDetail = urlSplitCorrelationDetailMap.get(childUrlMurmur3Hash);
                if (urlSplitCorrelationDetail != null) {
                    urlSplitCorrelationDetail.setDiscardType(UrlSplitCorrelationDetail.DISCARD_TYPE_CHILD_URL);
                    needUpdateChildUrlDetails.add(urlSplitCorrelationDetail);
                    // add a new urlSplitCorrelationDetail for related parentUrl to do split test
                    final String parentUrl = childPageRelEntity.getParentUrl();
                    // need to check parentUrl is exist in upload url list
                    final UrlSplitCorrelationDetail parentUrlExistDetail = urlDetailMap.get(parentUrl);
                    final long detailId = urlSplitCorrelationDetail.getId();
                    if (parentUrlExistDetail != null) {
                        parentUrlExistDetail.setChildUrlId(detailId);
                        needUpdateChildUrlDetails.add(parentUrlExistDetail);
                    } else {
                        final UrlSplitCorrelationDetail parentUrlDetail = new UrlSplitCorrelationDetail();
                        parentUrlDetail.setInstanceId(instanceId);
                        parentUrlDetail.setOwnDomainId(domainId);
                        parentUrlDetail.setUrl(parentUrl);
                        parentUrlDetail.setUrlMurmur3Hash(childPageRelEntity.getParentUrlMurmur3Hash());
                        parentUrlDetail.setBrowserMurmur3hash(MurmurHashUtils.getMurmurHash3_64(encodeUrlWithOkHttp(parentUrl)));
                        parentUrlDetail.setChildUrlId(detailId);
                        parentUrlDetails.add(parentUrlDetail);
                    }
                }
            });
            this.urlSplitCorrelationDetailDAO.batchUpdate(needUpdateChildUrlDetails);
            this.urlSplitCorrelationDetailDAO.batchSaveParentUrl(parentUrlDetails);
            Thread.sleep(2_000);
            // need update original urlSplitCorrelationDetails to replace child url with parent url to do split test
            urlSplitCorrelationDetails = this.urlSplitCorrelationDetailDAO.findParentUrlByInstanceId(instanceId);
        }
        log.info("instanceId: {}, urlList size: {}, childUrls size: {}", instanceId, urlSplitCorrelationDetailMap.size(), childPageRelEntities.size());
        return urlSplitCorrelationDetails;
    }

    private String savePageOptimization(PageOptimization pageOptimization, int domainId, Collection<UrlSplitCorrelationDetail> urlSplitCorrelationDetails) {
        final Integer createUserId = pageOptimization.getCreateUser();
        final Long pageOptimizationId = pageOptimization.getId();
        // 1. create content assistant list
        final LocalDateTime createTime = LocalDateTime.now();
        final String version = String.valueOf(System.nanoTime());
        final List<String> testGroupUrls = urlSplitCorrelationDetails.stream()
                .filter(detail -> detail.getUrlGroup() == UrlSplitCorrelationDetail.URL_GROUP_TEST)
                .map(UrlSplitCorrelationDetail::getUrl)
                .collect(Collectors.toList());
        final Collection<ContentAssistant> contentAssistantList = testGroupUrls.stream()
                .map(url -> {
                    final ContentAssistant contentAssistant = new ContentAssistant();
                    contentAssistant.setEnabled(1);
                    contentAssistant.setOwnDomainId(domainId);
                    contentAssistant.setFixType(ContentAssistant.FIX_TYPE_PAGE_OPTIMIZATION_NORMAL);
                    contentAssistant.setUrl(url);
                    contentAssistant.setCreateTime(createTime);
                    contentAssistant.setState(ContentAssistant.STATE_CREATE);
                    contentAssistant.setQaState(ContentAssistant.STATE_CREATE);
                    contentAssistant.setCreateUserId(createUserId);
                    contentAssistant.setUrlHash(MurmurHashUtils.getMurmurHash3_64_hex(encodeUrlWithOkHttp(url)));
                    contentAssistant.setUrlMurmur3Hash(MurmurHashUtils.getMurmurHash3_64(url));
                    contentAssistant.setVersion(version);
                    contentAssistant.setFixIds(StringUtils.EMPTY);
                    contentAssistant.setRuleJson(StringUtils.EMPTY);
                    return contentAssistant;
                }).collect(Collectors.toMap(ContentAssistant::getUrlHash, contentAssistant -> contentAssistant, (a, b) -> a))
                .values();
        // 3. save content assistant list
        try {
            this.contentAssistantDAO.createBatch(contentAssistantList);
        } catch (Exception e) {
            log.error("save {} content assistant error: {}", contentAssistantList.size(), e.getMessage());
            throw new RuntimeException(e);
        }
        log.info("save {} content assistant, version: {}", contentAssistantList.size(), version);
        // 4. save page optimization rel
        final List<Long> idList;
        try {
            idList = this.contentAssistantDAO.getIdsByOwnDomainIdAndFixType(domainId, ContentAssistant.FIX_TYPE_PAGE_OPTIMIZATION_NORMAL, version, contentAssistantList.size());
        } catch (Exception e) {
            log.error("save page optimization rel error: {}", e.getMessage());
            throw new RuntimeException(e);
        }
        final List<PageOptimizationCaRel> relList = idList.parallelStream().map(contentAssistantId -> {
            final PageOptimizationCaRel pageOptimizationCaRel = new PageOptimizationCaRel();
            pageOptimizationCaRel.setOptimizationId(pageOptimizationId);
            pageOptimizationCaRel.setContentAssistantId(contentAssistantId);
            pageOptimizationCaRel.setCreateUser(createUserId);
            pageOptimizationCaRel.setCreateDate(createTime);
            return pageOptimizationCaRel;
        }).collect(Collectors.toList());
        this.pageOptimizationCaRelDAO.createBatch(relList);
        return version;
    }

    private Collection<UrlSplitCorrelationDetail> addPageTag(int domainId, List<UrlSplitCorrelationDetail> urlSplitCorrelationDetails, CorrelationReport correlationReport, String testName) {
        final Map<String, UrlSplitCorrelationDetail> urlDetailMap = urlSplitCorrelationDetails.stream().collect(Collectors.toMap(UrlSplitCorrelationDetail::getUrl, detail -> detail));
        // create resource_batch_info first
        final ResourceBatchInfoEntity resourceBatchInfoEntity = createResourceBatchInfoEntity(domainId);
        final long infoId = resourceBatchInfoEntityDAO.insert(resourceBatchInfoEntity);
        log.info("resource_batch_info infoId: {}", infoId);

        final String extremeTestUrl = correlationReport.getExtremeTestUrl();
        final UrlSplitCorrelationDetail extremeTestUrlDetail = urlDetailMap.get(extremeTestUrl);
        if (extremeTestUrlDetail != null) {
            extremeTestUrlDetail.setDiscardType(UrlSplitCorrelationDetail.DISCARD_TYPE_EXTREME_DATA);
        }
        final String extremeControlUrl = correlationReport.getExtremeControlUrl();
        final UrlSplitCorrelationDetail extremeControlUrlDetail = urlDetailMap.get(extremeControlUrl);
        if (extremeControlUrlDetail != null) {
            extremeControlUrlDetail.setDiscardType(UrlSplitCorrelationDetail.DISCARD_TYPE_EXTREME_DATA);
        }
        // create resource_batch_detail
        final Integer createDate = Integer.valueOf(LocalDateTime.now().format(DateTimeFormatter.BASIC_ISO_DATE));
        final List<SplitTest.UrlMetrics> testGroup = correlationReport.getTestGroup();
        final List<SplitTest.UrlMetrics> controlGroup = correlationReport.getControlGroup();
        final List<ResourceBatchDetailEntity> controlGroupDetailList = controlGroup.stream()
                .map(SplitTest.UrlMetrics::getUrl)
                .map(url -> {
                    final UrlSplitCorrelationDetail urlSplitCorrelationDetail = urlDetailMap.get(url);
                    urlSplitCorrelationDetail.setUrlGroup(UrlSplitCorrelationDetail.URL_GROUP_CONTROL);
                    return getResourceBatchDetailEntity(domainId, testName + CONTROL_GROUP_TAG_SUFFIX, url, createDate, infoId);
                })
                .collect(Collectors.toList());
        final List<ResourceBatchDetailEntity> testGroupDetailList = testGroup.stream()
                .map(SplitTest.UrlMetrics::getUrl)
                .map(url -> {
                    final UrlSplitCorrelationDetail urlSplitCorrelationDetail = urlDetailMap.get(url);
                    urlSplitCorrelationDetail.setUrlGroup(UrlSplitCorrelationDetail.URL_GROUP_TEST);
                    return getResourceBatchDetailEntity(domainId, testName + TEST_GROUP_TAG_SUFFIX, url, createDate, infoId);
                })
                .collect(Collectors.toList());
        controlGroupDetailList.addAll(testGroupDetailList);
        if (controlGroupDetailList.size() < urlSplitCorrelationDetails.size()) {
            log.warn("resource_batch_detail size: {}, distinctUrlList size: {}", controlGroupDetailList.size(), urlSplitCorrelationDetails.size());
            // random allocate the rest of URLs into two groups
            final List<String> restUrl = urlSplitCorrelationDetails.stream()
                    .filter(urlSplitCorrelationDetail -> urlSplitCorrelationDetail.getDiscardType() < UrlSplitCorrelationDetail.DISCARD_TYPE_EXTREME_DATA)
                    .map(UrlSplitCorrelationDetail::getUrl)
                    .filter(url -> {
                        final boolean isControlGroupUrl = controlGroup.stream().anyMatch(urlMetrics -> urlMetrics.getUrl().equals(url));
                        final boolean isTestGroupUrl = testGroup.stream().anyMatch(urlMetrics -> urlMetrics.getUrl().equals(url));
                        return !url.equals(extremeTestUrl) && !url.equals(extremeControlUrl) && !isControlGroupUrl && !isTestGroupUrl;
                    }).collect(Collectors.toList());
            final int size = restUrl.size();
            if (size > 0) {
                Collections.shuffle(restUrl);

                // 获取现有组的大小
                final int existingTestGroupSize = testGroup.size();
                final int existingControlGroupSize = controlGroup.size();

                // 默认分配策略：平分，奇数时给controlGroup多一个
                int testGroupAllocation = size / 2;
                int controlGroupAllocation = size / 2;
                if (size % 2 == 1) {
                    controlGroupAllocation++; // 奇数时给controlGroup多一个
                }

                // 检查总数是否为奇数，如果是则根据existing大小关系修正
                int totalSize = existingTestGroupSize + existingControlGroupSize + size;
                if (totalSize % 2 == 1 && existingTestGroupSize < existingControlGroupSize) {
                    // 总数为奇数且testGroup较小，修正：从controlGroup转移一个给testGroup
                    testGroupAllocation++;
                    controlGroupAllocation--;
                    log.info("Total size is odd and testGroup ({}) < controlGroup ({}), adjusting allocation: testGroup={}, controlGroup={}",
                            existingTestGroupSize, existingControlGroupSize, testGroupAllocation, controlGroupAllocation);
                } else {
                    log.info("Default allocation: testGroup={}, controlGroup={} (existing: test={}, control={}, total={})",
                            testGroupAllocation, controlGroupAllocation, existingTestGroupSize, existingControlGroupSize, totalSize);
                }

                // 使用计算出的分配大小进行分组
                final List<String> randomAllocateTestGroupUrls = restUrl.subList(0, testGroupAllocation);
                final List<ResourceBatchDetailEntity> randomTestGroup = randomAllocateTestGroupUrls.stream()
                        .map(url -> {
                            final UrlSplitCorrelationDetail urlSplitCorrelationDetail = urlDetailMap.get(url);
                            urlSplitCorrelationDetail.setUrlGroup(UrlSplitCorrelationDetail.URL_GROUP_TEST);
                            urlSplitCorrelationDetail.setDiscardType(UrlSplitCorrelationDetail.DISCARD_TYPE_NO_DATA);
                            return getResourceBatchDetailEntity(domainId, testName + TEST_GROUP_TAG_SUFFIX, url, createDate, infoId);
                        })
                        .collect(Collectors.toList());
                controlGroupDetailList.addAll(randomTestGroup);

                final List<String> randomAllocateControlGroupUrls = restUrl.subList(testGroupAllocation, size);
                final List<ResourceBatchDetailEntity> randomControlGroup = randomAllocateControlGroupUrls.stream()
                        .map(url -> {
                            final UrlSplitCorrelationDetail urlSplitCorrelationDetail = urlDetailMap.get(url);
                            urlSplitCorrelationDetail.setUrlGroup(UrlSplitCorrelationDetail.URL_GROUP_CONTROL);
                            urlSplitCorrelationDetail.setDiscardType(UrlSplitCorrelationDetail.DISCARD_TYPE_NO_DATA);
                            return getResourceBatchDetailEntity(domainId, testName + CONTROL_GROUP_TAG_SUFFIX, url, createDate, infoId);
                        })
                        .collect(Collectors.toList());
                controlGroupDetailList.addAll(randomControlGroup);
                log.info("random allocate test group urls: {}, control group urls: {}", randomAllocateTestGroupUrls.size(), randomAllocateControlGroupUrls.size());
            }
        }
        resourceBatchDetailEntityDAO.insertBatchIgnoreDup(controlGroupDetailList);
        log.info("add page tag for controlGroup: {}", controlGroupDetailList.size());
        return urlDetailMap.values();
    }

    private ResourceBatchDetailEntity getResourceBatchDetailEntity(int domainId, String tagName, String url, Integer createDate, long infoId) {
        final ResourceBatchDetailEntity resourceBatchDetailEntity = createFromUrl(url);
        resourceBatchDetailEntity.setOwnDomainId(domainId);
        resourceBatchDetailEntity.setCreateDate(createDate);
        resourceBatchDetailEntity.setResourceSubordinate(tagName);
        resourceBatchDetailEntity.setInfoId(infoId);
        return resourceBatchDetailEntity;
    }

    private ResourceBatchDetailEntity createFromUrl(String url) {
        final ResourceBatchDetailEntity resourceBatchDetailEntity = new ResourceBatchDetailEntity();
        resourceBatchDetailEntity.setStatus(ResourceBatchDetailEntity.STATUS_CREATED);
        resourceBatchDetailEntity.setResourceMain(url);
        resourceBatchDetailEntity.setActionType(ResourceBatchInfoEntity.TYPE_ADD);
        resourceBatchDetailEntity.setResourceMd5(Md5Util.Md5(UUID.randomUUID().toString()));
        return resourceBatchDetailEntity;
    }

    private boolean isRemoveTrailingSlashes(Integer keepTrailingSlashes) {
        if (keepTrailingSlashes == null) {
            // default remove the end slash
            return true;
        }
        return keepTrailingSlashes < OwnDomainEntity.KEEP_TRAILING_SLASHES;
    }


    private String formatUrlByTrailingSlashes(String url, boolean removeTrailingSlashes) {
        if (url == null) {
            return null;
        }
        if (removeTrailingSlashes) {
            if (StringUtils.endsWith(url, "/")) {
                url = StringUtils.removeEnd(url, "/");
            }
        }
        return url;
    }


    /**
     * 使用 OkHttp 的 HttpUrl 模拟 Chrome 地址栏行为
     */
    public static String encodeUrlWithOkHttp(String rawUrl) {
        HttpUrl parsed = HttpUrl.parse(rawUrl);
        if (parsed == null) {
            return rawUrl;
        }
        return parsed.toString();
    }

}