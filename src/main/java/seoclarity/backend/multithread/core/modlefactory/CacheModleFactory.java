package seoclarity.backend.multithread.core.modlefactory;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.StringUtils;

import seoclarity.backend.utils.CommonUtils;
import seoclarity.backend.vectordb.EmbeddingEntity;

public class CacheModleFactory {
	private static CacheModleFactory cacheModleFactory;

	private Map<String, String> ipMap;

	private Date workDate;

	private Date rankingDate;

	private Map<String, List<String>> messageMap;
	private Map<String, List<EmbeddingEntity>> embeddingMap;
	
	private CacheModleFactory() {
		ipMap = Collections.synchronizedMap(new HashMap<String, String>());
		workDate = new Date();
		rankingDate = new Date();

		messageMap = Collections.synchronizedMap(new HashMap<>());
		embeddingMap = Collections.synchronizedMap(new HashMap<>());
	}

	public static synchronized CacheModleFactory getInstance() {
		if (cacheModleFactory == null) {
			cacheModleFactory = new CacheModleFactory();
		}
		return cacheModleFactory;
	}

	public String getAliveIpAddress() {
		Map<String, String> ipMap = getIpMap();
		String status = null;
		for (String ipAddress : ipMap.keySet()) {
			status = ipMap.get(ipAddress);
			if (CommonUtils.IP_STATUS_ALIVE.equals(status)) {
				ipMap.put(ipAddress, CommonUtils.IP_STATUS_WORKING);
				return ipAddress;
			}
		}
		return null;
	}

	public Map<String, String> getIpMap() {
		return ipMap;
	}

	public Date getWorkDate() {
		return workDate;
	}

	public void setWorkDate(Date workDate) {
		this.workDate = workDate;
	}

	public Date getRankingDate() {
		return rankingDate;
	}

	public void setRankingDate(Date rankingDate) {
		this.rankingDate = rankingDate;
	}

	public void clear() {
		ipMap = Collections.synchronizedMap(new HashMap<String, String>());
	}

	public void setAliveIpAddress(String ip) {
		ipMap.put(ip, CommonUtils.IP_STATUS_ALIVE);
	}
	

	public void setMessageMap(String ip, List<String> messages) {
		if (StringUtils.isNotBlank(ip) && messageMap.get(ip) != null) {
			List<String> messageList = messageMap.get(ip);
			messageList.addAll(messages);
			messageMap.put(ip, messageList);
		} else {
			messageMap.put(ip, messages);
		}
	}
	
	public void setMessageMap(String ip, String message) {
		List<String> messageList = null;
		if (StringUtils.isNotBlank(ip) && messageMap.get(ip) != null) {
			messageList = messageMap.get(ip);
		} else {
			messageList = new ArrayList<String>();
		}
		messageList.add(message);
		messageMap.put(ip, messageList);
	}
	
	public List<String> getMessageMap(){
		List<String> messageList = new ArrayList<>();
		for(String key : messageMap.keySet()) {
			
			messageList.addAll(messageMap.get(key));
		}
		return messageList;
	}
	
	public void clearMsgMapCache() {
		messageMap.clear();
		messageMap = Collections.synchronizedMap(new HashMap<>());
	}
	
	
	public void setEmbeddingMap(String ip, EmbeddingEntity embeddingEntity) {
		if (StringUtils.isNotBlank(ip) && embeddingMap.get(ip) != null) {
			List<EmbeddingEntity> messageList = embeddingMap.get(ip);
			messageList.add(embeddingEntity);
			embeddingMap.put(ip, messageList);
		} else {
			List<EmbeddingEntity> messageList = new ArrayList<>();
			messageList.add(embeddingEntity);
			embeddingMap.put(ip, messageList);
		}
	}
	
	public List<EmbeddingEntity> getEmbeddingMap(){
		List<EmbeddingEntity> messageList = new ArrayList<>();
		for(String key : embeddingMap.keySet()) {
			
			messageList.addAll(embeddingMap.get(key));
		}
		return messageList;
	}
	
	public void clearEmbeddingMapCache() {
		embeddingMap.clear();
		embeddingMap = Collections.synchronizedMap(new HashMap<>());
	}
}
