package seoclarity.backend.multithread.core.thread.threadpool;

import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

import seoclarity.backend.multithread.core.thread.command.BaseThreadCommand;
import seoclarity.backend.multithread.core.thread.common.ThreadPoolParameter;

public class ThreadPoolManager {
	private final int time = 1000;

	private static ThreadPoolManager threadPoolMgr;

	private static ThreadPoolExecutor threadPool;

	private static LinkedBlockingQueue<BaseThreadCommand> taskQueue = new LinkedBlockingQueue<BaseThreadCommand>();

	private int maximumPoolSizes;

	private int minPoolSizes;

	private int queueCapacity;

	private int keepAliveTime;

	private TimeUnit timeUnit;

	ListenerThread taskThread = new ListenerThread();

	boolean queueSign = true;

	private ThreadPoolManager() {
		super();
	}

	public static ThreadPoolManager getInstance() {
		if (null == threadPoolMgr) {
			threadPoolMgr = new ThreadPoolManager();
		}
		return threadPoolMgr;
	}

	public void clear() {
		if (threadPoolMgr != null)
			threadPoolMgr.destroy();
	}

	public void init() {
		try {
			taskThread = new ListenerThread();
			maximumPoolSizes = ThreadPoolParameter.getMaximumPoolSizes();

			minPoolSizes = ThreadPoolParameter.getMinPoolSizes();

			queueCapacity = ThreadPoolParameter.getQueueCapacity();

			keepAliveTime = ThreadPoolParameter.getKeepAliveTime();

			timeUnit = ThreadPoolParameter.getTimeUnit();

			ArrayBlockingQueue<Runnable> arrayQueue = new ArrayBlockingQueue<Runnable>(queueCapacity);

			threadPool = new ThreadPoolExecutor(minPoolSizes, maximumPoolSizes, keepAliveTime, timeUnit, arrayQueue,
					new ThreadPoolRejectedExecutionHandler());

			if (!queueSign) {
				queueSign = true;
			}
			taskThread.start();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	public boolean execute(BaseThreadCommand task) {
		if (queueSign && !threadPool.isShutdown()) {
			taskQueue.add(task);
			synchronized (ListenerThread.lockEmpty) {
				ListenerThread.lockEmpty.notifyAll();
			}
			return true;
		} else {
			return false;
		}
	}

	public ThreadPoolExecutor getThreadPool() {
		if (null == threadPool) {
			init();
		}
		return threadPool;
	}

	LinkedBlockingQueue<BaseThreadCommand> getTaskQueue() {
		return taskQueue;
	}

	@SuppressWarnings("static-access")
	public void destroy() {
		queueSign = false;

		while (true) {
			try {
				Thread.currentThread().sleep(time);
			} catch (InterruptedException e) {
				e.printStackTrace();
			}
			if (taskQueue.isEmpty()) {
				break;
			}
		}

		taskThread.setSign(false);
		synchronized (ListenerThread.lockEmpty) {
			ListenerThread.lockEmpty.notifyAll();
		}
		while (true) {
			if (taskThread.getState() == Thread.State.TERMINATED) {
				threadPool.shutdown();
				break;
			} else {
				try {
					Thread.currentThread().sleep(time);
				} catch (InterruptedException e) {
					e.printStackTrace();
				}
			}
		}
	}
}
