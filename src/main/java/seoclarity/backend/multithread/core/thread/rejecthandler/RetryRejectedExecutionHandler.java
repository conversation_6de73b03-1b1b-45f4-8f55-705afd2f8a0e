package seoclarity.backend.multithread.core.thread.rejecthandler;

import lombok.extern.apachecommons.CommonsLog;

import java.util.concurrent.RejectedExecutionHandler;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * <AUTHOR>
 * @create 2018-07-09 22:17
 **/
@CommonsLog
public class RetryRejectedExecutionHandler implements RejectedExecutionHandler {

    @Override
    public void rejectedExecution(Runnable runnable, ThreadPoolExecutor threadPoolExecutor) {
        try {
            log.info("threadPool & queue all full, waiting...");
            Thread.sleep(15 * 1000);
            threadPoolExecutor.execute(runnable);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }

}
