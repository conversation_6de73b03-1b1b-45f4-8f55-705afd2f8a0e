package seoclarity.backend.multithread.core.thread.command;

import seoclarity.backend.multithread.core.thread.threadpool.ListenerThread;

public abstract class BaseThreadCommand implements Runnable {

	private boolean status;

	public void run() {
		try {
			if (status) {
				execute();
			} else {
				undo();
			}
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			synchronized (ListenerThread.lock) {
				ListenerThread.lock.notifyAll();
			}
		}
	}

	protected abstract void execute() throws Exception;

	protected abstract void undo() throws Exception;

	public void setStatus(boolean status) {
		this.status = status;
	}

}
