package seoclarity.backend.multithread.core.thread.threadpool;

import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.locks.ReentrantLock;

import seoclarity.backend.multithread.core.thread.command.BaseThreadCommand;
import seoclarity.backend.multithread.core.thread.common.ThreadPoolParameter;

public class ListenerThread extends Thread {

	private int queueCapacity = ThreadPoolParameter.getQueueCapacity();

	private boolean sign = true;

	public static ReentrantLock lock = new ReentrantLock();

	public static ReentrantLock lockEmpty = new ReentrantLock();

	public boolean isSign() {
		return sign;
	}

	public void setSign(boolean sign) {
		this.sign = sign;
	}

	@SuppressWarnings("static-access")
	public void run() {
		LinkedBlockingQueue taskQueue = ThreadPoolManager.getInstance()
				.getTaskQueue();
		BaseThreadCommand task = null;
		ThreadPoolExecutor threadPool = ThreadPoolManager.getInstance()
				.getThreadPool();
		while (sign) {
			try {
				if (null == task) {
					task = (BaseThreadCommand) taskQueue.poll();
				}
				if (task != null) {
					if (threadPool.getQueue().size() >= queueCapacity) {
						synchronized (lock) {
							lock.wait();
						}
					} else {
						threadPool.execute(task);
						task = null;
					}
				} else {
					synchronized (lockEmpty) {
						lockEmpty.wait();
					}
				}
			} catch (InterruptedException e1) {
				e1.printStackTrace();
			}
		}
	}
}
