package seoclarity.backend.opensearch;

import org.apache.hc.client5.http.auth.AuthScope;
import org.apache.hc.client5.http.auth.UsernamePasswordCredentials;
import org.apache.hc.client5.http.config.ConnectionConfig;
import org.apache.hc.client5.http.impl.auth.BasicCredentialsProvider;
import org.apache.hc.client5.http.impl.nio.PoolingAsyncClientConnectionManager;
import org.apache.hc.client5.http.impl.nio.PoolingAsyncClientConnectionManagerBuilder;
import org.apache.hc.client5.http.ssl.ClientTlsStrategyBuilder;
import org.apache.hc.client5.http.ssl.TrustAllStrategy;
import org.apache.hc.core5.http.HttpHost;
import org.apache.hc.core5.http.nio.ssl.TlsStrategy;
import org.apache.hc.core5.ssl.SSLContextBuilder;
import org.apache.hc.core5.util.TimeValue;
import org.opensearch.client.opensearch.OpenSearchClient;
import org.opensearch.client.transport.OpenSearchTransport;
import org.opensearch.client.transport.httpclient5.ApacheHttpClient5TransportBuilder;

import javax.net.ssl.SSLContext;
import java.io.IOException;
import java.io.InputStream;
import java.security.KeyManagementException;
import java.security.KeyStoreException;
import java.security.NoSuchAlgorithmException;
import java.util.Properties;

public class OpenSearchClientFactory {
    private static final OpenSearchClient INSTANCE = createClient();

    public static OpenSearchClient getInstance() {
        return INSTANCE;
    }

    private static OpenSearchClient createClient() {
        Properties properties = new Properties();
        try (InputStream input = OpenSearchClientFactory.class.getResourceAsStream("/opensearch.properties")) {
            properties.load(input);
        } catch (IOException ex) {
            throw new RuntimeException("Failed to load OpenSearch properties", ex);
        }

        final String hostScheme = properties.getProperty("opensearch.host.scheme", "https");
        final String hostName = properties.getProperty("opensearch.host.name", "localhost");
        final int hostPort = Integer.parseInt(properties.getProperty("opensearch.host.port", "9200"));
        final String username = properties.getProperty("opensearch.username", "admin");
        final String password = properties.getProperty("opensearch.password", "admin");

        final HttpHost host = new HttpHost(hostScheme, hostName, hostPort);
        final BasicCredentialsProvider credentialsProvider = new BasicCredentialsProvider();
        credentialsProvider.setCredentials(new AuthScope(host), new UsernamePasswordCredentials(username, password.toCharArray()));

        final SSLContext sslcontext;
        try {
            sslcontext = SSLContextBuilder
                    .create()
                    .loadTrustMaterial(null, new TrustAllStrategy())
                    .build();
        } catch (NoSuchAlgorithmException | KeyManagementException | KeyStoreException e) {
            throw new RuntimeException(e);
        }

        final ApacheHttpClient5TransportBuilder builder = ApacheHttpClient5TransportBuilder.builder(host);
        builder.setHttpClientConfigCallback(httpClientBuilder -> {
            final TlsStrategy tlsStrategy = ClientTlsStrategyBuilder.create()
                    .setSslContext(sslcontext)
                    .build();
            final PoolingAsyncClientConnectionManager connectionManager = PoolingAsyncClientConnectionManagerBuilder
                    .create()
                    .setTlsStrategy(tlsStrategy)
                    .setMaxConnPerRoute(Integer.parseInt(properties.getProperty("opensearch.maxConnPerRoute", "30")))
                    .setMaxConnTotal(Integer.parseInt(properties.getProperty("opensearch.maxConnTotal", "30")))
                    .setDefaultConnectionConfig(ConnectionConfig.custom().setTimeToLive(TimeValue.ofMinutes(30)).build())
                    .build();
            return httpClientBuilder
                    .setDefaultCredentialsProvider(credentialsProvider)
                    .setConnectionManager(connectionManager);
        });

        final OpenSearchTransport transport = builder.build();
        return new OpenSearchClient(transport);
    }
}