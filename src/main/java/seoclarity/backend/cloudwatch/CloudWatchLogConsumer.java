package seoclarity.backend.cloudwatch;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang.time.DateUtils;

import com.google.gson.Gson;

import seoclarity.backend.dao.actonia.ApiBillingInfoEntityDAO;
import seoclarity.backend.dao.actonia.ApiBillingInstanceEntityDAO;
import seoclarity.backend.dao.actonia.ApiBillingParamsEntityDAO;
import seoclarity.backend.dao.actonia.UsersDAO;
import seoclarity.backend.entity.TUsers;
import seoclarity.backend.entity.actonia.ApiBillingInfoEntity;
import seoclarity.backend.entity.actonia.ApiBillingParamsEntity;
import seoclarity.backend.entity.actonia.CloudWatchResultVO;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.SpringBeanFactory;
import software.amazon.awssdk.auth.credentials.ProfileCredentialsProvider;
import software.amazon.awssdk.regions.Region;
import software.amazon.awssdk.services.cloudwatch.model.CloudWatchException;
import software.amazon.awssdk.services.cloudwatchlogs.CloudWatchLogsClient;
import software.amazon.awssdk.services.cloudwatchlogs.model.FilterLogEventsRequest;
import software.amazon.awssdk.services.cloudwatchlogs.model.FilterLogEventsResponse;
import software.amazon.awssdk.services.cloudwatchlogs.model.FilteredLogEvent;

/**
 * mvn exec:java -Dexec.mainClass="seoclarity.backend.cloudwatch.CloudWatchLogConsumer" -Dexec.args=""
 * mvn exec:java -Dexec.mainClass="seoclarity.backend.cloudwatch.CloudWatchLogConsumer" -Dexec.args="2022-07-18 2022-07-27 api_billing_log"
 * 
 * need to config in server
	[root@scripts16 clarity-backend-script-backup]# cat ~/.aws/credentials
	[cloudWatch]
	aws_access_key_id = ********************
	aws_secret_access_key = hWHXB3usAvd/XYqzKInIw/DYONXsKQkNng5Qcoev

 * <AUTHOR>
 *
 */ 
public class CloudWatchLogConsumer {
	
	private static String LOG_GROUP_NAME = "api_billing_log";
	
	private ApiBillingInfoEntityDAO apiBillingInfoEntityDAO;
	private ApiBillingInstanceEntityDAO apiBillingInstanceEntityDAO;
	private ApiBillingParamsEntityDAO apiBillingParamsEntityDAO;
	private UsersDAO usersDAO;
	
	public CloudWatchLogConsumer() {
		apiBillingInfoEntityDAO = SpringBeanFactory.getBean("apiBillingInfoEntityDAO");
		apiBillingInstanceEntityDAO = SpringBeanFactory.getBean("apiBillingInstanceEntityDAO");
		apiBillingParamsEntityDAO = SpringBeanFactory.getBean("apiBillingParamsEntityDAO");
		usersDAO = SpringBeanFactory.getBean("usersDAO");
	}

    public static void main(String[] args) {

        String logGroupName = LOG_GROUP_NAME;
        
        String startDate = FormatUtils.formatDate(DateUtils.addDays(new Date(), -2), "yyyy-MM-dd");
        String endDate = FormatUtils.formatDate(DateUtils.addDays(new Date(), -1), "yyyy-MM-dd");
        
        if (ArrayUtils.isNotEmpty(args) && args.length >= 2) {
        	startDate = args[0];
        	endDate = args[1];
        	
        	if (args.length >= 3) {
        		logGroupName = args[2];
			}
		}
        CloudWatchLogConsumer cloudWatchLogConsumer = new CloudWatchLogConsumer();
        cloudWatchLogConsumer.process(startDate, endDate, logGroupName);

    }
    
    
    private void process(String startDate, String endDate, String logGroupName) {
    	
    	System.out.println("processing : " + startDate + " - " + endDate);
    	System.out.println("logGroupName : " + logGroupName);
    	
    	Long startTime = FormatUtils.toDate(startDate, "yyyy-MM-dd").getTime();
    	Long endTime = FormatUtils.toDate(endDate, "yyyy-MM-dd").getTime();
    	
    	CloudWatchLogsClient cloudWatchLogsClient = CloudWatchLogsClient.builder()
                .credentialsProvider(ProfileCredentialsProvider.create("cloudWatch"))
                .region(Region.US_EAST_1)
                .build();

    	
    	 filterCWLogEventsV2(cloudWatchLogsClient, logGroupName, startTime, endTime);
         
    	 cloudWatchLogsClient.close();
    }

    private static Gson gson = new Gson();
    
    private Integer successCnt = 0;
    private Integer parseFailedCnt = 0;
    private Integer totalCnt = 0;
    
    public void filterCWLogEventsV2(CloudWatchLogsClient cloudWatchLogsClient, String logGroupName, Long startTime, Long endTime) {
        
        boolean done = false;
        String nextToken = null;
        
        List<CloudWatchResultVO> resultList = new ArrayList<CloudWatchResultVO>();

        try {
            while(!done) {

            	FilterLogEventsResponse response;

                if (nextToken == null) {
                	 FilterLogEventsRequest filterLogEventsRequest = FilterLogEventsRequest.builder()
                             .logGroupName(logGroupName)
                             .startTime(startTime)
                             .endTime(endTime)
                             .build();

                   	response = cloudWatchLogsClient.filterLogEvents(filterLogEventsRequest);
                } else {
                	 FilterLogEventsRequest filterLogEventsRequest = FilterLogEventsRequest.builder()
                             .logGroupName(logGroupName)
                             .nextToken(nextToken)
                             .startTime(startTime)
                             .endTime(endTime)
                             .build();

	                response =  cloudWatchLogsClient.filterLogEvents(filterLogEventsRequest);
	            }
                
                if (response == null || response.events() == null) {
					continue;
				}
                
                totalCnt += response.events().size();
                
	            for (FilteredLogEvent logEvent : response.events()) {
	            	try {
	            		resultList.add(gson.fromJson(formatMsg(logEvent.message()), CloudWatchResultVO.class));
	            		successCnt++;
					} catch (Exception e) {
						parseFailedCnt++;
						System.out.println("message parse failed : " + logEvent.message());
					}
	            }
	            
	            if (resultList != null && resultList.size() >= 1000) {
	            	addToDb(resultList);
	            	resultList = new ArrayList<>();
				}
	
	            if(response.nextToken() == null) {
	                done = true;
	            } else {
	                nextToken = response.nextToken();
	            }
	        }
            
            if (resultList != null && resultList.size() >= 0) {
            	addToDb(resultList);
			}

        } catch (CloudWatchException e) {
            System.err.println(e.awsErrorDetails().errorMessage());
            System.exit(1);
        }
    }
    
    private String formatMsg(String inputText) {
    	
    	inputText = StringUtils.replace(inputText, "\"\"", "\"");
    	return inputText;
    }
    
    private void addToDb(List<CloudWatchResultVO> resultList) {
    	
    	System.out.println("===== result from cloudwatch cnt : " + resultList.size());
    	
    	
    	//--------- step 1 add new info and cache
    	List<ApiBillingInfoEntity> infoList = apiBillingInfoEntityDAO.getApiTaskInfos();
    	System.out.println("===== infoList size : " + infoList.size());
    	Map<String, Integer> infoMap = infoList.stream().collect(Collectors.toMap(var1 -> (var1.getApiId() + var1.getResourcePath()), var2 -> var2.getId()));
    	
    	List<CloudWatchResultVO> newApiResultList = resultList.stream().filter(x -> !infoMap.containsKey(x.getApi_id() + x.getResource_path()))
    			.collect(Collectors.toList());
    	Map<String, String> newApiIdPathMap = new HashMap<>();
    	for(CloudWatchResultVO cloudWatchResultVO : newApiResultList) {
    		
    		if (!newApiIdPathMap.keySet().contains(cloudWatchResultVO.getApi_id())) {
    			newApiIdPathMap.put(cloudWatchResultVO.getApi_id(), cloudWatchResultVO.getResource_path());
			}
    	}
      
    	Map<String, Integer> finalInfoMap = new HashMap<>();
    			
    	if (newApiIdPathMap != null && newApiIdPathMap.size() > 0) {
    		System.out.println("Add new api to Info, cnt:" + newApiIdPathMap.size());

    		apiBillingInfoEntityDAO.batchInsertByApiIdAndPath(newApiIdPathMap);
    		
    		infoList = apiBillingInfoEntityDAO.getApiTaskInfos();
    		finalInfoMap = infoList.stream().collect(Collectors.toMap(var1 -> (var1.getApiId() + var1.getResourcePath()), var2 -> var2.getId()));
    		System.out.println("== reload info list size:" + infoList.size());
		} else {
			finalInfoMap = infoMap;
		}
    	
    	List<ApiBillingParamsEntity> paramList = new ArrayList<>();
    	
    	//--------- step 2 add instance 
    	for(CloudWatchResultVO cloudWatchResultVO : resultList) {
    		
    		try {
    			
    			if (finalInfoMap.get(cloudWatchResultVO.getApi_id() + cloudWatchResultVO.getResource_path()) == null 
    					|| finalInfoMap.get(cloudWatchResultVO.getApi_id() + cloudWatchResultVO.getResource_path()) == 0) {
    				 System.out.println("api_id is not exist in info :" + cloudWatchResultVO.getApi_id());
    				 continue;
    			}
    			 
    			Map<String, Object>	parameters = new HashMap<String, Object>();
        		parameters.put("billingInfoId", finalInfoMap.get(cloudWatchResultVO.getApi_id() + cloudWatchResultVO.getResource_path()));
        		parameters.put("requestTime", cloudWatchResultVO.getRequestTimeStr());
        		parameters.put("requestDate", NumberUtils.toInt(cloudWatchResultVO.getRequestDate()));
        		parameters.put("sourceIp", cloudWatchResultVO.getSource_ip());
        		parameters.put("accessToken", cloudWatchResultVO.getAccess_token());
        		parameters.put("responseCode", NumberUtils.toInt(cloudWatchResultVO.getStatus()));
        		parameters.put("errMsg", cloudWatchResultVO.getErr_msg());
        		parameters.put("resultCount", NumberUtils.toInt(cloudWatchResultVO.getResult_cnt()));
        		parameters.put("responseLength", NumberUtils.toInt(cloudWatchResultVO.getResponseLength()));
        		parameters.put("responseInfo", cloudWatchResultVO.getResponseInfo());
        		parameters.put("httpMethod", cloudWatchResultVO.getHttp_method());
        		parameters.put("uniqueRequestId", cloudWatchResultVO.getUnique_request_id());
        		parameters.put("createdAt", FormatUtils.formatDate(new Date(), "yyyy-MM-dd HH:mm:ss"));
        		
        		if (StringUtils.isNotBlank(cloudWatchResultVO.getAccess_token())) {
        			TUsers users = usersDAO.checkAuthority(cloudWatchResultVO.getAccess_token());
        			
        			if (users != null && users.getOwnDomainId() != null && users.getUserId() != null) {
        				parameters.put("ownDomainId", users.getOwnDomainId());
                		parameters.put("createUserId", users.getUserId());
                		
        			} else {
        				parameters.put("ownDomainId", 0);
                		parameters.put("createUserId", 0);
                		
        			}
				}
        		
        		Long instanceId = apiBillingInstanceEntityDAO.insertForLongId(parameters);
        		
        		ApiBillingParamsEntity apiBillingParamsEntity = new ApiBillingParamsEntity();
        		apiBillingParamsEntity.setBillingInstanceId(instanceId);
        		apiBillingParamsEntity.setQueryString(cloudWatchResultVO.getqString());
        		apiBillingParamsEntity.setRequestBody(cloudWatchResultVO.getrBody());
        		apiBillingParamsEntity.setUserAgent(cloudWatchResultVO.getUser_agent());
        		
        		paramList.add(apiBillingParamsEntity);
        		
			} catch (Exception e) {
				e.printStackTrace();
			}
    		
    	}
    	
    	//---------- step 3 add param
		apiBillingParamsEntityDAO.batchInsertByApiIdAndPath(paramList);
    	
    	//---------- step 4 print result
		System.out.println("totalcnt:" + totalCnt);
		System.out.println("successCnt:" + successCnt);
		System.out.println("failedCnt:" + parseFailedCnt);
		
    }
    
    
}
