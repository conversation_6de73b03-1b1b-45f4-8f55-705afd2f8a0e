package seoclarity.backend.keywordexpand.oneTime;

import com.amazonaws.services.sqs.AmazonSQS;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import seoclarity.backend.dao.rankcheck.RgRealtimeExpansionEntityDAO;
import seoclarity.backend.dao.rankcheck.RgRealtimeExpansionKeywordEntityDAO;
import seoclarity.backend.entity.KeywordProperty;
import seoclarity.backend.entity.rankcheck.RgRealtimeExpansionEntity;
import seoclarity.backend.entity.rankcheck.RgRealtimeExpansionKeywordEntity;
import seoclarity.backend.keywordexpand.RealTimeRGKeywordExpand;
import seoclarity.backend.utils.DateUtils;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.Md5Util;
import seoclarity.backend.utils.SpringBeanFactory;
import seoclarity.backend.utils.amazon.SQSUtils;

import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
public class ReSendKwToRgRealTimeQueue {

    private static final String QUEUE_NAME_DESKTOP = "SQS_IN_RG_REALTIME_EXPANSION_DESKTOP";
    private static final String QUEUE_NAME_MOBILE = "SQS_IN_RG_REALTIME_EXPANSION_MOBILE";
    private static final int DEVICE_MOBILE = 2;
    private static final int DEVICE_DESKTOP = 1;
    private static final int FREQUENCY_TRIPLE_MONTHLY = 90;
    private static final int MSG_BATCH_SIZE = 10;
    private static final Gson gson = new Gson();

    private RgRealtimeExpansionEntityDAO realtimeExpansionEntityDAO;
    private RgRealtimeExpansionKeywordEntityDAO realtimeExpansionKeywordEntityDAO;
    private RealTimeRGKeywordExpand realTimeRGKeywordExpand;

    private boolean isTest;
    private int processType;
    private AmazonSQS amazonSQS;

    public ReSendKwToRgRealTimeQueue() {
        realtimeExpansionEntityDAO = SpringBeanFactory.getBean("realtimeExpansionEntityDAO");
        realtimeExpansionKeywordEntityDAO = SpringBeanFactory.getBean("realtimeExpansionKeywordEntity");
        realTimeRGKeywordExpand = new RealTimeRGKeywordExpand();
        amazonSQS = SQSUtils.getAmazonSQS();
    }

    public static void main(String[] args) {
        new ReSendKwToRgRealTimeQueue().startProcess(args);
    }

    private void startProcess(String[] args) {
        isTest = Boolean.parseBoolean(args[0]);
        processType = Integer.parseInt(args[1]);

        if (processType == 0) {
            String createDate = args[2];
            String expansionIds = args[3];
            processMobile(createDate, expansionIds);
        } else if (processType == 1) {
            String createDate = args[2];
            String expansionIds = args[3];
            processDesktop(createDate, expansionIds);
        }
    }

    private void processDesktop(String createDate, String expansionIds) {
        String[] idArr = expansionIds.split(",");
        System.out.println("==processCnt:" + idArr.length);
        int sendToQueueDate = Integer.parseInt(createDate);

        String mobileQueueUrl = realTimeRGKeywordExpand.getQueueInfo(QUEUE_NAME_DESKTOP);

        for (String idStr : idArr) {
            int expansionId = Integer.parseInt(idStr);
            RgRealtimeExpansionEntity expansion = realtimeExpansionEntityDAO.getById(expansionId);
            int engineId = expansion.getEngineId();
            int languageId = expansion.getLanguageId();

            if (engineId != 1 || languageId != 1) {
                log.info("==skipUnProcess engineId:{} languageId:{} expansionId:{}", engineId, languageId, expansionId);
                continue;
            }

            String kfkTopicDesktop = realTimeRGKeywordExpand.getKfkTp(engineId, languageId, DEVICE_DESKTOP);
            List<RgRealtimeExpansionKeywordEntity> kwList = realtimeExpansionKeywordEntityDAO.getByExpansionIdAndState(expansionId, RgRealtimeExpansionKeywordEntity.STATE_CANDIDATE_RG_KEYWORD);
            List<RgRealtimeExpansionKeywordEntity> sendKwList = kwList.stream().filter(kw -> kw.getFrequency() == FREQUENCY_TRIPLE_MONTHLY).toList();

            List<KeywordProperty> keywordPropertyList = realTimeRGKeywordExpand.convertRGKeywordPropertyList(expansionId, engineId, languageId, sendToQueueDate, createDate, sendKwList);

            log.info("==kwCntInfo expansionId:{} kwCnt:{} sendKwCnt:{} realSendKwCnt:{}", expansionId, kwList.size(), sendKwList.size(), keywordPropertyList.size());

            for (KeywordProperty keywordProperty : keywordPropertyList) {
                keywordProperty.setFrequency(FREQUENCY_TRIPLE_MONTHLY);
                if (isTest) {
                    log.info("==perProperty expansionId:{} sv:{} kwId:{} keyword:{}", expansionId, keywordProperty.getSearchVol(), keywordProperty.getId(), keywordProperty.getKeywordText());
                }
            }
            sent(DEVICE_DESKTOP, kfkTopicDesktop, mobileQueueUrl, keywordPropertyList);
        }
    }

    private void processMobile(String createDate, String expansionIds) {
        String[] idArr = expansionIds.split(",");
        System.out.println("==processCnt:" + idArr.length);
        int sendToQueueDate = Integer.parseInt(createDate);

        String mobileQueueUrl = realTimeRGKeywordExpand.getQueueInfo(QUEUE_NAME_MOBILE);

        for (String idStr : idArr) {
            int expansionId = Integer.parseInt(idStr);
            RgRealtimeExpansionEntity expansion = realtimeExpansionEntityDAO.getById(expansionId);
            int engineId = expansion.getEngineId();
            int languageId = expansion.getLanguageId();

            String kfkTopicMobile = realTimeRGKeywordExpand.getKfkTp(engineId, languageId, DEVICE_MOBILE);

            List<RgRealtimeExpansionKeywordEntity> kwList = realtimeExpansionKeywordEntityDAO.getByExpansionIdAndState(expansionId, RgRealtimeExpansionKeywordEntity.STATE_CANDIDATE_RG_KEYWORD);
            List<KeywordProperty> keywordPropertyList = realTimeRGKeywordExpand.convertRGKeywordPropertyList(expansionId, engineId, languageId, sendToQueueDate, createDate, kwList);
            log.info("==kwCntInfo expansionId:{} kwCnt:{} sendKwCnt:{}", expansionId, kwList.size(), keywordPropertyList.size());

            sent(DEVICE_MOBILE, kfkTopicMobile, mobileQueueUrl, keywordPropertyList);
        }
    }

    public void sent(int device, String kfkTopic, String queueUrl, List<KeywordProperty> keywordPropertyList) {
        Map<String, String> messages = new HashMap<>();
        for (KeywordProperty keywordProperty : keywordPropertyList) {
            keywordProperty.setDevice(device);
            // https://www.wrike.com/open.htm?id=1688961891
            if (DEVICE_MOBILE == device) {
                keywordProperty.setFrequency(FREQUENCY_TRIPLE_MONTHLY);
            }
            keywordProperty.setKfkTpc(kfkTopic);
            String msg = gson.toJson(keywordProperty);
            if (isTest) {
                log.info("==sentToQueueMsg msg:{}", msg);
            }
            messages.put(Md5Util.Md5(msg), msg);
            if (messages.size() == MSG_BATCH_SIZE) {
                if (!isTest) {
                    try {
                        SQSUtils.sendBatchMessageToQueue(amazonSQS, queueUrl, messages);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
                messages.clear();
            }

            if (!messages.isEmpty()) {
                if (!isTest) {
                    try {
                        SQSUtils.sendBatchMessageToQueue(amazonSQS, queueUrl, messages);
                    } catch (Exception e) {
                        e.printStackTrace();
                    }
                }
                messages.clear();
            }
        }
    }
}
