package seoclarity.backend.keywordexpand.oneTime;

import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVRecord;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import seoclarity.backend.utils.FormatUtils;

import java.io.*;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.*;

public class KwParseFileOneTime {

    private static final String FILE_TYPE_TXT = ".txt";
    private static final String FILE_NAME = "mergerFile_";
    private static final String DATE_FORMAT = "yyyyMMddHHmmss";

    private boolean isTest;
    private int processType;

    public static void main(String[] args) {
        new KwParseFileOneTime().startProcess(args);
    }

    private void startProcess(String[] args) {
        isTest = Boolean.parseBoolean(args[0]);
        processType = Integer.parseInt(args[1]);
        if (processType == 0) {
            String parentFilePath = args[2];
            String outParentPath = args[3];
            boolean fileIsHaveSeId = Boolean.parseBoolean(args[4]); // 为false时 必须手动提供seId, true时必须为-
            String seId = args[5];
            if (!fileIsHaveSeId) {
                if (!StringUtils.containsIgnoreCase(seId, "_")) {
                    System.out.println("==paramInvild when fileIsHaveSeId is false seId must have value and must have '_', eg: 1_1, 6_8");
                    return;
                }
            }
            int startIndex = Integer.parseInt(args[6]);
            int encodeType = 0;
            if (args.length >= 8) {
                Integer.parseInt(args[7]);
            }
            mergerFileNoSv(parentFilePath, outParentPath, fileIsHaveSeId, seId, startIndex, getCharsetByEncodeType(encodeType));
        } else if (processType == 1) { // 分片对文件去重
            int partCnt = Integer.parseInt(args[2]);
            String sourceFilePath = args[3];
            String outParentPath = args[4];
            try {
                largeFileDeduplication(partCnt, sourceFilePath, outParentPath);
            } catch (Exception e) {
                e.printStackTrace();
            }
        } else if (processType == 2) {// 打乱文件顺序
            String sourceFilePath = args[2];
            String outFilePath = args[3];
            try {
                shuffleFile(sourceFilePath, outFilePath);
            } catch (IOException e) {
                e.printStackTrace();
            }
        } else if (processType == 3) {// 直接对文件去重
            String sourceFilePath = args[2];
            String outFilePath = args[3];
            try {
                deduplicationFile(sourceFilePath, outFilePath);
            } catch (Exception e) {
                e.printStackTrace();
            }
        } else if (processType == 4) {// 比较两个文件是否相等
            String file1 = args[2];
            String file2 = args[3];
            try {
                boolean b = areFilesEqual(file1, file2);
                System.out.println("isEqual:" + b);
            } catch (Exception e) {
                e.printStackTrace();
            }
        } else if (processType == 5) {
            String parentFilePath = args[2];
            String outParentPath = args[3];
            int startIndex = Integer.parseInt(args[4]);
            mergerFileHaveSv(parentFilePath, outParentPath, startIndex);
        }
    }

    /**
     * "1","1","10dlc","us a2p 10dlc" -> 0 -> languageId, 1 -> engineId, 3 -> kw
     * @param parentFilePath
     * @param outParentPath
     * @param startIndex
     */
    private void mergerFileHaveSv(String parentFilePath, String outParentPath, int startIndex) {
        String fileName = FILE_NAME + FormatUtils.formatDate(new Date(), DATE_FORMAT) + FILE_TYPE_TXT;
        String outFilePath = outParentPath + File.separator + fileName;
        int totalCnt = 0;
        int outputCnt = 0;
        int allCnt = 0;

        File parentFile = new File(parentFilePath);
        File[] files = parentFile.listFiles();
        List<String> fileLines = new ArrayList<>();

        for (File file : files) {
            if (file.isDirectory()) {
                System.out.println("==>file: " + file.getName() + ", outFilePath: " + outFilePath);
                continue;
            }
            String subFileName = file.getName();
            if (!StringUtils.containsIgnoreCase(subFileName, "csv")) {
                System.out.println("==>skipFile filename: " + subFileName);
                continue;
            }

            try {
                if (StringUtils.containsIgnoreCase(subFileName, "csv")) {
                    fileLines = readFileFromCsvV2(file.getAbsolutePath(), startIndex);
                } else {
                    fileLines = FileUtils.readLines(file);
                }

                if (fileLines.isEmpty()) {
                    System.out.println("==>fileEmpty: " + subFileName);
                    continue;
                }

                totalCnt+=fileLines.size();
                outputCnt+=fileLines.size();
                System.out.println("==>fileInfo processCnt:" + outputCnt + " sourceCnt:" + totalCnt + " fileCnt:" + fileLines.size() + " fileName:" + subFileName);

                FileUtils.writeLines(new File(outFilePath), fileLines, true);

                allCnt += outputCnt;
                fileLines.clear();
                outputCnt = 0;
                totalCnt = 0;
            } catch (IOException e) {
                System.out.println("==>read file error: " + subFileName);
                e.printStackTrace();
            }
        }
        System.out.println("allCnt:" + allCnt);
    }

    private Charset getCharsetByEncodeType(int encodeType) {
        Charset res = null;
        switch (encodeType) {
            case 1:
                res = StandardCharsets.US_ASCII;
                break;
            case 2:
                res = StandardCharsets.ISO_8859_1;
                break;
            case 3:
                res = StandardCharsets.UTF_16BE;
                break;
            case 4:
                res = StandardCharsets.UTF_16LE;
                break;
            case 5:
                res = StandardCharsets.UTF_16;
                break;
            default:
                res = StandardCharsets.UTF_8;
        }
        return res;
    }

    private boolean areFilesEqual(String file1, String file2) throws Exception {
        try (BufferedReader reader1 = new BufferedReader(new FileReader(file1));
             BufferedReader reader2 = new BufferedReader(new FileReader(file2))) {

            String line1, line2;

            while (true) {
                line1 = reader1.readLine();
                line2 = reader2.readLine();

                // 判断两行内容是否相等
                if (!Objects.equals(line1, line2)) {
                    return false; // 行内容不一致，文件不相等
                }

                // 如果两行都为 null，表示文件结束
                if (line1 == null) {
                    break;
                }
            }
        }

        return true; // 文件完全相等
    }

    private void deduplicationFile(String inputFile, String outputFile) throws Exception {
        Set<String> uniqueKeywords = new HashSet<>();
        try (BufferedReader reader = new BufferedReader(new FileReader(inputFile));
             BufferedWriter writer = new BufferedWriter(new FileWriter(outputFile))) {
            String line;
            while ((line = reader.readLine()) != null) {
                if (uniqueKeywords.add(line)) {
                    writer.write(line);
                    writer.newLine();
                }
            }
        }
        System.out.println("Deduplication completed. Unique keywords saved to: " + outputFile);
    }

    private void largeFileDeduplication(int partCnt, String inputFile, String outputDir) throws Exception {
        splitFileByHash(partCnt, inputFile, outputDir);

        deduplicatePartitions(partCnt, outputDir);

        mergeDeduplicatedFiles(partCnt, outputDir, outputDir + "/final_output.txt");

        System.out.println("Deduplication completed.");
    }

    private void shuffleFile(String inputFile, String outputFile) throws IOException {
        List<String> lines = new ArrayList<>();
        try (BufferedReader reader = new BufferedReader(new FileReader(inputFile))) {
            String line;
            while ((line = reader.readLine()) != null) {
                lines.add(line);
            }
        }
        Collections.shuffle(lines); // 随机打乱行顺序
        try (BufferedWriter writer = new BufferedWriter(new FileWriter(outputFile))) {
            for (String line : lines) {
                writer.write(line);
                writer.newLine();
            }
        }
    }

    private void splitFileByHash(int partCnt, String inputFile, String outputDir) throws IOException {
        List<BufferedWriter> partitionWriters = new ArrayList<>();
        for (int i = 0; i < partCnt; i++) {
            partitionWriters.add(new BufferedWriter(new FileWriter(outputDir + "partition_" + i + ".txt")));
        }

        try (BufferedReader reader = new BufferedReader(new FileReader(inputFile))) {
            String line;
            while ((line = reader.readLine()) != null) {
                // 根据哈希值分片
                int partitionIndex = (line.hashCode() & Integer.MAX_VALUE) % partCnt;
                partitionWriters.get(partitionIndex).write(line);
                partitionWriters.get(partitionIndex).newLine();
            }
        }

        // 关闭分片文件的写入流
        for (BufferedWriter writer : partitionWriters) {
            writer.close();
        }

        System.out.println("File split into " + partCnt + " partitions.");
    }

    /**
     * 对每个分片文件进行去重
     */
    private void deduplicatePartitions(int partCnt, String outputDir) throws IOException {
        for (int i = 0; i < partCnt; i++) {
            String partitionFile = outputDir + "partition_" + i + ".txt";
            String dedupedFile = outputDir + "deduped_partition_" + i + ".txt";

            deduplicateFile(partitionFile, dedupedFile);
        }
    }

    /**
     * 单个文件去重
     */
    private void deduplicateFile(String inputFile, String outputFile) throws IOException {
        Set<String> uniqueKeywords = new HashSet<>();

        try (BufferedReader reader = new BufferedReader(new FileReader(inputFile));
             BufferedWriter writer = new BufferedWriter(new FileWriter(outputFile))) {
            String line;
            while ((line = reader.readLine()) != null) {
                if (uniqueKeywords.add(line)) {
                    writer.write(line);
                    writer.newLine();
                }
            }
        }
        System.out.println("Deduplicated file: " + outputFile);
    }

    /**
     * 合并所有去重后的分片文件
     */
    private void mergeDeduplicatedFiles(int partCnt, String outputDir, String finalOutputFile) throws IOException {
        try (BufferedWriter writer = new BufferedWriter(new FileWriter(finalOutputFile))) {
            for (int i = 0; i < partCnt; i++) {
                String dedupedFile = outputDir + "deduped_partition_" + i + ".txt";
                try (BufferedReader reader = new BufferedReader(new FileReader(dedupedFile))) {
                    String line;
                    while ((line = reader.readLine()) != null) {
                        writer.write(line);
                        writer.newLine();
                    }
                }
            }
        }

        System.out.println("Merged deduplicated files into: " + finalOutputFile);
    }

    private void mergerFileNoSv(String parentFilePath, String outParentPath, boolean fileIsHaveSeId, String seId, int startIndex, Charset encode) {
        String fileName = FILE_NAME + FormatUtils.formatDate(new Date(), DATE_FORMAT) + FILE_TYPE_TXT;
        String outFilePath = outParentPath + File.separator + fileName;
        List<String> outputList = new ArrayList<>();
        int totalCnt = 0;
        int outputCnt = 0;
        int allCnt = 0;

        File parentFile = new File(parentFilePath);
        File[] files = parentFile.listFiles();
        List<String> fileLines = new ArrayList<>();

        for (File file : files) {
            if (file.isDirectory()) {
                System.out.println("==>file: " + file.getName() + ", outFilePath: " + outFilePath);
                continue;
            }
            String subFileName = file.getName();
            if (StringUtils.containsIgnoreCase(subFileName, "store")) {
                System.out.println("==>skipFile filename: " + subFileName);
                continue;
            }

            String engineId = "";
            String languageId = "";
            if (fileIsHaveSeId) {
                String[] split = subFileName.split("_");
                engineId = split[1].trim();
                languageId = split[2].split("\\.")[0].trim();
            } else {
                String[] split = seId.split("_");
                engineId = split[0].trim();
                languageId = split[1].trim();
            }

            try {
                if (StringUtils.containsIgnoreCase(subFileName, "csv")) {
                    fileLines = readFileFromCsv(file.getAbsolutePath(), startIndex, encode);
                } else {
                    fileLines = FileUtils.readLines(file);
                }

                if (fileLines.isEmpty()) {
                    System.out.println("==>fileEmpty: " + subFileName);
                    continue;
                }

                for (String line : fileLines) {
                    outputList.add(engineId + "\t" + languageId + "\t" + line.trim());
                    totalCnt++;
                    outputCnt++;
                }
                System.out.println("==>fileInfo processCnt:" + outputCnt + " sourceCnt:" + totalCnt + " fileCnt:" + fileLines.size() + " fileName:" + subFileName);

                FileUtils.writeLines(new File(outFilePath), outputList, true);

                allCnt += outputCnt;
                fileLines.clear();
                outputList.clear();
                outputCnt = 0;
                totalCnt = 0;
            } catch (IOException e) {
                System.out.println("==>read file error: " + subFileName);
                e.printStackTrace();
            }
        }
        System.out.println("allCnt:" + allCnt);
    }

    private List<String> readFileFromCsv(String filePath, int startIndex, Charset encode) {
        List<String> resultMap = new ArrayList<>();
        CSVParser csvParser = null;
        try {
            if (encode == null) {
                csvParser = CSVFormat.DEFAULT.withDelimiter('\t').withIgnoreEmptyLines(true).parse(new FileReader(filePath));
            } else {
                csvParser = CSVFormat.DEFAULT.withDelimiter('\t').withIgnoreEmptyLines(true).parse(new InputStreamReader(new FileInputStream(filePath), encode));
            }
            List<CSVRecord> rows = csvParser.getRecords();
            for (int i = startIndex; i < rows.size(); i++) {
                CSVRecord row = rows.get(i);
                String value = row.get(0).trim();
                if (StringUtils.isEmpty(value)) {
                    continue;
                }
                resultMap.add(value);
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return resultMap;
    }

    private List<String> readFileFromCsvV2(String filePath, int startIndex) {
        List<String> resultMap = new ArrayList<>();
        CSVParser csvParser = null;
        try {
            csvParser = CSVFormat.DEFAULT.withDelimiter(',').withIgnoreEmptyLines(true).parse(new FileReader(filePath));
            List<CSVRecord> rows = csvParser.getRecords();
            for (int i = startIndex; i < rows.size(); i++) {
                CSVRecord row = rows.get(i);
                String language = row.get(0).trim();
                String engine = row.get(1).trim();
                String kw = row.get(3).trim();

                resultMap.add(language + "\t" + engine + "\t" + kw);
            }
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
        return resultMap;
    }
}
