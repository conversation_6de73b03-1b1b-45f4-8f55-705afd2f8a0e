package seoclarity.backend.keywordexpand.utils;

import okhttp3.*;
import seoclarity.backend.utils.ZipFileUtils;

import java.io.BufferedReader;
import java.io.File;
import java.io.InputStreamReader;
import java.net.SocketTimeoutException;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

public class ExportDataUtil {

    public static long HTTP_EXPORT_CLARITYDB_TO_FILE_EXCEPTION = -1L;

    public static long HTTP_EXPORT_CLARITYDB_TO_FILE_FAILD = -2L;

    public static long HTTP_EXPORT_RESPONSE_ERROR = 400L;

    private static final Integer READ_TIMEOUT = 1800;

    public static Long httpExportFromClarityDB(String chServerUrl, String chDatabase, String user, String password, String sql, String fullFileName, boolean haveFileHeader,
                                                  boolean isZip, String[] err) throws Exception {
        String queryUrl = chServerUrl + "/?database=" + chDatabase + "&enable_http_compression=1&user=" + URLEncoder.encode(user,"UTF-8")  + "&password=" +
                URLEncoder.encode(password,"UTF-8") + "&query=" + URLEncoder.encode(sql, "UTF-8");
        System.out.println("====URL1:" + queryUrl);

        int retryCount = 0;
        while (true) {
            long start = System.currentTimeMillis() / 1000;
            int readTimeOut = (retryCount + 1) * READ_TIMEOUT;
            OkHttpClient.Builder builder = new OkHttpClient.Builder();
            OkHttpClient client = builder.readTimeout(readTimeOut, TimeUnit.SECONDS).build();
            MediaType mediaType = MediaType.parse("text/plain");
            RequestBody body = RequestBody.create(mediaType, "");
            Request request = new Request.Builder().url(queryUrl).method("POST", body).build();
            try (Response response = client.newCall(request).execute()) {
              /* If successful, you receive the 200 response code and the result in the response body.
                 If an error occurs, you receive the 500 response code and an error description text in the response body. */
                File file = new File(fullFileName);
                BufferedReader br = new BufferedReader(new InputStreamReader(response.body().byteStream()));
                String str = null;
                long k = 0L;
                boolean append = false;
                List<String> outputList = new ArrayList<String>();
                while ((str = br.readLine()) != null) {
                    if(response.code() >= HTTP_EXPORT_RESPONSE_ERROR) {
                        err[0] = err[0] + "\r\n" + str;
                        continue;
                    }
                    k++;
                    outputList.add(str);
                    if (k % 100000 == 0) {
                        System.out.println("  ==Row:" + k + " " + str);
                        org.apache.commons.io.FileUtils.writeLines(file, "UTF-8", outputList, append);
                        outputList = new ArrayList<String>();
                        append = true;
                    }
                }
                if(response.code() >= HTTP_EXPORT_RESPONSE_ERROR) {
                    return HTTP_EXPORT_CLARITYDB_TO_FILE_FAILD;
                }
                if (outputList.size() >= 0) {
                    org.apache.commons.io.FileUtils.writeLines(file, "UTF-8", outputList, append);
                }
                long end = System.currentTimeMillis() / 1000;
                System.out.println("###exeSql cost time:" + ((end - start) / 60) + "m" + ((end - start) % 60) + "s");
                start = System.currentTimeMillis() / 1000;
                if (isZip) {
                    String fullNameZip = String.join(".", fullFileName, "zip");
                    List<File> srcFiles = new ArrayList<>();
                    srcFiles.add(file);
                    ZipFileUtils.zipFile(fullNameZip, srcFiles);
                    file.delete();
                    end = System.currentTimeMillis() / 1000;
                    System.out.println("###zip cost time:" + ((end - start) / 60) + "m" + ((end - start) % 60) + "s");
                }

                if (haveFileHeader)
                    return k - 1;
                return k;
            } catch (SocketTimeoutException ex) {
                ex.printStackTrace();
                if (++retryCount >= 3)
                    break;
                Thread.sleep(10000);
            } catch (Exception e) {
                e.printStackTrace();
                throw e;
            }
        }
        return HTTP_EXPORT_CLARITYDB_TO_FILE_EXCEPTION;
    }
}
