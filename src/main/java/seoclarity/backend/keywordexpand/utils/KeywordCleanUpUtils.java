package seoclarity.backend.keywordexpand.utils;

import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;

import javax.annotation.Nullable;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * Process for Cleanup of keywords
 */
public class KeywordCleanUpUtils {

    private static final Logger logger = Logger.getLogger(KeywordCleanUpUtils.class);

    public static void main(String[] args) {
        String kw = "August 1, 2023 - July 31, 2024";
        String str = KeywordCleanUpUtils.cleanUp(kw);
        System.out.println(str);

    }
    private static Set<Character> containsSpecialCharSet;
    private static Set<String> beginWithSpecialCharSet;
    private static Set<String> beginWithSpecialStrSet;
    private static Set<String> replaceWithSpaceStrSet;
    private static final Pattern INVALID_PATTERN = Pattern.compile("\uFFFD"); // https://www.wrike.com/open.htm?id=1709983145

    private static Character[] containsSpecialCharArr = {
            '@', '^', '*', '=', '/',
    };

    private static String[] beginWithSpecialStrArr = {
            "cache:", "site:", "allinurl:", "related:", "info:"
    };

    private static String[] beginWithSpecialCharArr = {
            "¡", "¢", "¤", "¦", "§", "¨", "©", "ª",
            "«", "¬", "®", "¯", "–", "—", "‘", "’", "‚", "“", "”", "„", "†", "‡", "•", "…", "‰", "™", "°",
            "±", "²", "³", "´", "µ", "¶", "·", "¸", "¹", "º", "»", "¼", "½", "¾", "¿", "�", "×", "@", "~",
            "<", ">", "{", "}", "′", "″", "‾", "⁄", "℘", "ℑ", "ℜ", "ℵ", "←", "↑", "↓", "↔", "↵", "⇐", "⇑",
            "⇒", "⇓", "⇔", "∀", "∂", "∃", "∅", "∇", "∈", "∉", "∋", "∏", "∑", "−", "∗", "√", "∝", "∞",
            "∠", "∧", "∨", "∩", "∪", "∫", "∴", "∼", "≅", "≈", "≠", "≡", "≤", "≥", "⊂", "⊃", "⊄", "⊆",
            "⊇", "⊕", "⊗", "⊥", "⋅", "⌈", "⌉", "⌊", "⌋", "⟨", "⟩", "◊", "♠", "♣", "♥", "♦", "˜", "‹", "›",
            "１", "😃", "🇩🇴", "🇵🇹", "→"
    };

    public static char[] ILLEGAL_CHAR_ARRAY = new char[] {
            '{', '}', '?', '!', ',',  ':',  ';',  '~',  '\"',
            '！',  '，',  '。',  '【',  '】',  '—',  '“',  '”',
            '’',  '@',  '<',  '>',  '%',  '*',  '\\',  '^',
            '+',  '￼',  '`',  '.',  '*',  '¡',  '¢',  '£',
            '¤',  '¥',  '¦',  '§',  '¨',  '©',  'ª',  '«',
            '¬',  '®',  '¯',  '–',  '—',  '‘',  '’',  '‚',
            '“',  '”',  '„',  '†',  '‡',  '•',  '…',  '‰',
            '€',  '™',  '°',  '±',  '²',  '³',  '´',  'µ',
            '¶',  '·',  '¸',  '¹',  'º',  '»',  '¼',  '½',
            '¾',  '¿',  '×',  '�',
    };

    private static String[] replaceWithSpaceStrArr = {
            "-", "|", "#", "$", "%", ":", ".", "!", "&", "&amp;", "_", "+", ";", "'", "~", "`", "&nbsp;", "(", ")", "[", "]", "{", "}", "<", ">"
    };

    private static String parenthesisLeft = "(";
    private static String parenthesisRight = ")";
    private static String bracketLeft = "[";
    private static String bracketRight = "]";
    private static String bracesLeft = "{";
    private static String bracesRight = "}";
    private static String lessThan = "<";
    private static String moreThan = ">";

    private static char space = ' ';
    private static String doubleSpace = "  ";
    private static String threeSpaces = "   ";

    private static char commas = ',';

    private static char single_quotation_marks = '\'';
    private static char double_quotation_marks = '"';
    
    static {
        containsSpecialCharSet = Arrays.stream(containsSpecialCharArr).collect(Collectors.toSet());
        beginWithSpecialCharSet = Arrays.stream(beginWithSpecialCharArr).collect(Collectors.toSet());
        beginWithSpecialStrSet = Arrays.stream(beginWithSpecialStrArr).collect(Collectors.toSet());
        replaceWithSpaceStrSet = Arrays.stream(replaceWithSpaceStrArr).collect(Collectors.toSet());
    }

    public static String cleanUp(@Nullable String keyword) {
        keyword = eliminateCharLongerThan80(keyword);
        keyword = eliminateWordLongerThan10(keyword);
        keyword = lowercase(keyword);
        keyword = cleanUpBySpecialCharacters(keyword);
        keyword = repetitionReplace(keyword);
        keyword = replaceSpecialCharWithSpace(keyword);
        keyword = removeEnclosingQuotationMarks(keyword); // https://www.wrike.com/open.htm?id=928222467
        //keyword = replaceCommas(keyword);
        //keyword = replaceSpacialQuotationMarks(keyword);
        //keyword = removeIncorrectWords(keyword);
        //keyword = cleaningSpecialLengthAndWidthKeywords(keyword);
        keyword = removeExtraSpaces(keyword);
        keyword = removeInvalidChar(keyword); // https://www.wrike.com/open.htm?id=1703299289
        return keyword;
    }

    /**
     * s1
     */
    public static String eliminateCharLongerThan80(String keyword) {
        if (keyword == null || keyword.length() == 0 || keyword.trim().length() > 80) {
            return null;
        }
        return keyword.trim();
    }

    /**
     * s2
     */
    public static String eliminateWordLongerThan10(String keyword) {
        if (keyword == null || keyword.length() == 0) {
            return null;
        }
        String[] words = keyword.split(" ");
        if (words.length > 10) {
            return null;
        }
        return keyword;
    }

    /**
     * s3
     */
    public static String lowercase(String keyword) {
        if (keyword == null || keyword.length() == 0) {
            return null;
        }
        return keyword.toLowerCase();
    }

    /**
     * s4
     */
    public static String cleanUpBySpecialCharacters(String keyword) {
        if (keyword == null || keyword.length() == 0) {
            return null;
        }


        for (int i = 0; i < keyword.length(); i++) {
            char ch = keyword.charAt(i);
            if (containsSpecialCharSet.contains(ch)) {
                return null;
            }

            if (i == 0 && beginWithSpecialCharSet.contains(ch)) {
                return null;
            }
        }

        for (int i = 0; i < beginWithSpecialStrArr.length; i++) {
            if (keyword.startsWith(beginWithSpecialStrArr[i])) {
                return null;
            }
        }

        return keyword;
    }

    /**
     * s5
     */
    public static String repetitionReplace(String keyword) {
        if (keyword == null || keyword.length() == 0) {
            return null;
        }

        if (keyword.trim().split(" ").length == 1) {
            int cnt = 1;
            boolean change = false;
            char ch = keyword.charAt(0);
            for (int i = 1; i < keyword.length(); i++) {
                char chi = keyword.charAt(i);
                if (ch == chi) {
                    cnt++;
                } else {
                    change = true;
                }
            }

            if (cnt >= 3 && !change) {
                return null;
            }
        }else {
            StringBuffer sbf = new StringBuffer();
            String[] words = keyword.split(" ");
            for (int i = 0; i < words.length; i++) {
                String word = words[i];
                int chCnt = 0;
                boolean sing = false;
                if (word.equalsIgnoreCase("")) {
                    continue;
                }
                char cha = word.charAt(0);
                for (int j = 1; j < word.length(); j++) {
                    char chi = word.charAt(j);
                    if (cha == chi) {
                        chCnt++;
                        sing = true;
                    } else {
                        sing = false;
                    }
                }

                if (sing && chCnt >= 3) {
                    sbf.append(cha);
                }else {
                    sbf.append(word);
                }
                sbf.append(space);
            }
            return sbf.toString();
        }
        return keyword.trim();
    }

    /**
     * s6
     */
    public static String replaceSpecialCharWithSpace(String keyword) {
        if (keyword == null || keyword.length() == 0) {
            return null;
        }

        for (int i = 0; i < replaceWithSpaceStrArr.length; i++) {
            String str = replaceWithSpaceStrArr[i];
            if (keyword.contains(str)) {
                keyword = keyword.replace(str, " ");
            }
        }

        /*if (keyword.contains(parenthesisLeft) && keyword.contains(parenthesisRight)) {
            keyword = keyword.replace(parenthesisLeft, " ")
                    .replace(parenthesisRight, " ");
        }

        if (keyword.contains(bracketLeft) && keyword.contains(bracketRight)) {
            keyword = keyword.replace(bracketLeft, " ")
                    .replace(bracketRight, " ");
        }

        if (keyword.contains(bracesLeft) && keyword.contains(bracesRight)) {
            keyword = keyword.replace(bracesLeft, " ")
                    .replace(bracesRight, " ");
        }

        if (keyword.contains(lessThan) && keyword.contains(moreThan)) {
            keyword = keyword.replace(lessThan, " ")
                    .replace(moreThan, " ");
        }*/

        return keyword.trim();
    }

    /**
     * s7
     */
    public static String replaceCommas(String keyword) {
        if (keyword == null || keyword.length() == 0) {
            return null;
        }
        StringBuffer sbf = new StringBuffer();

        char chAfter;
        char chBefore;
        for (int i = 1; i < keyword.length() - 1; i++) {
            char ch = keyword.charAt(i);
            if (ch == commas) {
                chBefore = keyword.charAt(i - 1);
                chAfter = keyword.charAt(i + 1);
                if (Character.isDigit(chBefore) && Character.isDigit(chAfter)) {
                    //logger.info("");
                } else {
                    sbf.append(" ");
                }
            } else {
                sbf.append(ch);
            }
        }

        return sbf.toString();

    }

    /**
     * s8
     */
    public static String replaceSpacialQuotationMarks(String keyword) {
        if (keyword == null || keyword.length() == 0) {
            return null;
        }
        StringBuffer sbf = new StringBuffer();
        char chAfter;
        for (int i = 0; i < keyword.length() - 1; i++) {
            char ch = keyword.charAt(i);
            if (ch == single_quotation_marks || ch == double_quotation_marks) {
                chAfter = keyword.charAt(i + 1);
                if (chAfter == space) {
                    //todo
                    //logger.info("");
                } else {
                    sbf.append(ch);
                }
            } else {
                sbf.append(ch);
            }
        }

        return sbf.toString();
    }

    /**
     * s9
     */
    public static String removeIncorrectWords(String keyword) {
        if (keyword == null || keyword.length() == 0) {
            return null;
        }

        if (keyword.length() < 7) {
            //a b c d    len = 7
            return keyword;
        }

        if (keyword.charAt(1) == space
                && keyword.charAt(3) == space
                && keyword.charAt(5) == space) {
            return null;
        }

        if (keyword.charAt(keyword.length() - 2) == space
                && keyword.charAt(keyword.length() - 4) == space
                && keyword.charAt(keyword.length() - 6) == space) {
            return null;
        }

        return keyword;
    }

    /**
     * s10
     */
    public static String cleaningSpecialLengthAndWidthKeywords(String keyword) {
        if (keyword == null || keyword.length() == 0) {
            return null;
        }
        return null;
    }

    /**
     * s11
     */
    public static String removeExtraSpaces(String keyword) {
        if (keyword == null || keyword.length() == 0) {
            return null;
        }

        StringBuffer sbf = new StringBuffer();
        char chLast = ' ';
        for (int i = 0; i < keyword.length(); i++) {
            char ch = keyword.charAt(i);
            if (ch == space && chLast == space) {
                //todo
                //logger.info("");
            } else {
                chLast = ch;
                sbf.append(ch);
            }
        }
        keyword = sbf.toString();

        if (keyword.charAt(keyword.length() - 1) == space) {
            keyword = keyword.substring(0, keyword.length() - 1);
        }
        return keyword;
    }

    /**
     * s12 Unique Keywords list
     */
    public static List<String> keywordListDistinct(List<String> keywords) {
        return keywords.stream().distinct().collect(Collectors.toList());
    }

    /**
     * s13 if a keyword start/end with " , we should remove "
     */
    public static String removeEnclosingQuotationMarks(String keyword) {
    	if (keyword == null || keyword.length() == 0) {
            return null;
        }
    	
    	Set<Character> quotationMarkSet = new HashSet<Character>();
    	quotationMarkSet.add(single_quotation_marks);
    	quotationMarkSet.add(double_quotation_marks);
    	return removeEnclosingChar(keyword, quotationMarkSet);
    }  
    
    private static String removeEnclosingChar(String keyword, Set<Character> strToRemoveSet) {
    	String result = keyword;
    	while (true) {
    		String tmpStr = null;
    		boolean replaced = false;
    		if (StringUtils.isEmpty(result)) {
    			return null;
    		}
    		char leadingCh = result.charAt(0);
    		if (strToRemoveSet.contains(leadingCh)) {
    			tmpStr = result.substring(1);
    			result = tmpStr;
    			replaced = true;
    		}
    		
    		if (StringUtils.isEmpty(result)) {
    			return null;
    		}
    		char trailingCh = result.charAt(result.length() - 1);
    		if (strToRemoveSet.contains(trailingCh)) {
    			tmpStr = result.substring(0, result.length() - 1);
    			result = tmpStr;
    			replaced = true;
    		}	
    		if (replaced == false) {
    			return result;
    		}
    	}
    }

    // https://www.wrike.com/open.htm?id=1703299289
    public static String removeInvalidChar(String keyword) {
        if (StringUtils.isEmpty(keyword)) {
            return null;
        }
        Matcher matcher = INVALID_PATTERN.matcher(keyword);
        if (matcher.find()) {
            return null;
        }
        return keyword;
    }
}