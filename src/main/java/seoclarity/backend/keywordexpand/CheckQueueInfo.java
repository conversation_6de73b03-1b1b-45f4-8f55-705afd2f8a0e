package seoclarity.backend.keywordexpand;

import com.amazonaws.services.sqs.AmazonSQS;
import com.amazonaws.services.sqs.model.GetQueueUrlRequest;
import com.amazonaws.services.sqs.model.GetQueueUrlResult;
import com.google.gson.Gson;
import com.google.gson.JsonSyntaxException;
import lombok.extern.apachecommons.CommonsLog;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateUtils;
import org.springframework.format.datetime.DateFormatter;
import seoclarity.backend.dao.actonia.ApiCacheBodyDao;
import seoclarity.backend.entity.actonia.ApiCacheBodyEntity;
import seoclarity.backend.entity.rankcheck.RgRealtimeExpansionSeedKeywordEntity;
import seoclarity.backend.keywordexpand.entity.DataSeoData;
import seoclarity.backend.keywordexpand.entity.DataSeoResponse;
import seoclarity.backend.keywordexpand.entity.DataSeoResult;
import seoclarity.backend.keywordexpand.entity.DataSeoTask;
import seoclarity.backend.keywordexpand.utils.KeywordCleanUpUtils;
import seoclarity.backend.service.CommonDataService;
import seoclarity.backend.summary.ClarityDBBotDailyUploadThreadMain;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.SpringBeanFactory;
import seoclarity.backend.utils.WorkerUtils;
import seoclarity.backend.utils.amazon.SQSUtils;

import java.io.File;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.text.ParseException;
import java.util.*;

@Slf4j
public class CheckQueueInfo {

    private static final String QUEUE_NAME_DESKTOP = "SQS_IN_RG_REALTIME_EXPANSION_DESKTOP";
    private static final String QUEUE_NAME_MOBILE = "SQS_IN_RG_REALTIME_EXPANSION_MOBILE";
    private static final Gson gson = new Gson();

    private AmazonSQS amazonSQS;
    private ApiCacheBodyDao apiCacheBodyDao;

    public CheckQueueInfo() {
        amazonSQS = SQSUtils.getAmazonSQS();
        /*apiCacheBodyDao = SpringBeanFactory.getBean("apiCacheBodyDao");*/
    }

    public static void main(String[] args) throws Exception {
        new CheckQueueInfo().startProcess(args);
    }

    private void startProcess(String[] args) {
        int processType = Integer.parseInt(args[0]);
        if (processType == 0) {
            int bodyId = Integer.parseInt(args[1]);
            try {
                checkIssue(bodyId);
            } catch (Exception e) {
                e.printStackTrace();
            }
        } else if (processType == 1) {
            clearQueue(false);
        } else if (processType == 2) {
            sendWorkerKey();
        } else if (processType == 3) {
            testSendEmail();
        } else if (processType == 4 ) {
            String startDate = args[1];
            String endDate = args[2];
            checkExpediaMissBotDate(startDate, endDate);
        } else if (processType == 5 ) { // https://www.wrike.com/open.htm?id=1535675532
            String startDate = args[1];
            String endDate = args[2];
            String filePath = args[3];
            genExpediaMissBotDateFile(startDate, endDate, filePath);
        }
    }

    private void genExpediaMissBotDateFile(String startDate, String endDate, String filePath) {
        Map<Integer, List<String>> datePContentMap = commonExpediaMissBotDate(startDate, endDate);
        if (datePContentMap != null && !datePContentMap.isEmpty()) {
            File resFile = new File(filePath);
            List<String> contentList = new ArrayList<>();
            contentList.add("DATE\tOID\tDomain");
            for (Integer date : datePContentMap.keySet()) {
                List<String> pContentList = datePContentMap.get(date);
                for (String pContent : pContentList) {
                    contentList.add(date + "\t" + pContent);
                }
            }
            if (!contentList.isEmpty()) {
                System.out.println("==missInfo missCnt:" + contentList.size());
                try {
                    FileUtils.writeLines(resFile, contentList);
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    private void checkExpediaMissBotDate(String startDate, String endDate) {
        Map<Integer, List<String>> datePContentMap = commonExpediaMissBotDate(startDate, endDate);
        if (datePContentMap != null && !datePContentMap.isEmpty()) {
            for (Integer date : datePContentMap.keySet()) {
                System.out.println("miss bot date: " + date);
                List<String> pContentList = datePContentMap.get(date);
                System.out.println("miss domains: ");
                for (String pContent : pContentList) {
                    System.out.println(pContent);
                }
                System.out.println("===========================================================================");
            }
        }
    }

    private Map<Integer, List<String>> commonExpediaMissBotDate(String startDate, String endDate) {
        Map<Integer, List<String>> datePContentMap = null;
        List<Date> logDates = new ArrayList<>();
        Date sDate = null;
        Date eDate = null;
        try {
            sDate = DateUtils.parseDate(startDate, new String[]{"yyyy-MM-dd", "yyyyMMdd", "yyyy/MM/dd"});
            eDate = DateUtils.parseDate(endDate, new String[]{"yyyy-MM-dd", "yyyyMMdd", "yyyy/MM/dd"});
        } catch (ParseException e) {
            e.printStackTrace();
        }
        if (sDate != null && eDate != null) {
            while (sDate.compareTo(eDate) <= 0) {
                logDates.add(sDate);
                sDate = DateUtils.addDays(sDate, 1);
            }
            ClarityDBBotDailyUploadThreadMain clarityDBBotDailyUploadThreadMain = new ClarityDBBotDailyUploadThreadMain();
            datePContentMap = clarityDBBotDailyUploadThreadMain.checkMissBotDateLocal(logDates);
        }
        return datePContentMap;
    }

    private void testSendEmail() {
        ClarityDBBotDailyUploadThreadMain clarityDBBotDailyUploadThreadMain = new ClarityDBBotDailyUploadThreadMain();
        Date checkStartDate = DateUtils.addDays(new Date(), -1);
        clarityDBBotDailyUploadThreadMain.checkMissBotDate(checkStartDate);
    }

    private void sendWorkerKey() {
        List<String> workList = new ArrayList<>();

        sentStartWorker(workList);
    }

    public void sentStartWorker(List<String> workerKeyList) {
        if (workerKeyList.isEmpty()) {
            return;
        }
        for (String key : workerKeyList) {
            WorkerUtils.syncEventKeyToWorkers(WorkerUtils.WORKER_BASE_URL, key, "");
        }
    }



    private void checkIssue(int bodyId) throws Exception {

//        String apiResponse = FileUtils.readLines(new File("/Users/<USER>/Desktop/test.txt")).get(0);
        ApiCacheBodyEntity apiCacheBody = apiCacheBodyDao.getById(bodyId);
        String apiResponse = apiCacheBody.getApiResponse();
        DataSeoResponse dataSeoTask = null;
        try {
            dataSeoTask = gson.fromJson(apiResponse, DataSeoResponse.class);
        } catch (JsonSyntaxException e) {
            e.printStackTrace();
        }
        Map<String, String> sourceKwPEncodeKwMap = new HashMap<>();
        Map<String, List<DataSeoResult>> kwInfoMap = genExpansionSeedKw(1, dataSeoTask, sourceKwPEncodeKwMap);
        Set<String> kwList = kwInfoMap.keySet();

        List<String> cleanUpSkipKwList = new ArrayList<>();
        List<String> cleanUpOkKwList = new ArrayList<>();
        cleanupKw(3, kwList, cleanUpSkipKwList, cleanUpOkKwList, sourceKwPEncodeKwMap);
        log.info("==cleanUpInfo kwListCnt:{} skipKwCnt:{} okKwCnt:{}", kwList.size(), cleanUpSkipKwList.size(), cleanUpOkKwList.size());
    }

    private void cleanupKw(int expansionId, Set<String> sourceKwList, List<String> cleanUpSkipKwList, List<String> cleanUpOkKwList, Map<String, String> sourceKwPEncodeKwMap) {
        for (String keywordName : sourceKwList) {
            String sourceKw = keywordName;
            if (keywordName.contains("...")) {
                cleanUpSkipKwList.add(keywordName);
                continue;
            }

            if (keywordName.contains("\t")) {
                cleanUpSkipKwList.add(keywordName);
                continue;
            }

            if (keywordName.contains("#NAME")) {
                cleanUpSkipKwList.add(keywordName);
                continue;
            }

            String cleanUpKw = null;
            try {
                cleanUpKw = KeywordCleanUpUtils.cleanUp(keywordName);
            } catch (Exception e) {
                cleanUpSkipKwList.add(keywordName);
                log.info("==cleanUpError expansionId:{} keywordName:{} cleanUpKw:{}", expansionId, keywordName, cleanUpKw);
                e.printStackTrace();
            }

            if (cleanUpKw != null) {
                if (StringUtils.containsAny(cleanUpKw, KeywordCleanUpUtils.ILLEGAL_CHAR_ARRAY)) {
                    log.info("SkipWithBadChar expansionId:{} keywordName:{} cleanUpKw:{}", expansionId, keywordName, cleanUpKw);
                    cleanUpSkipKwList.add(keywordName);
                    continue;
                }
                cleanUpOkKwList.add(cleanUpKw);
                if (!StringUtils.equalsIgnoreCase(cleanUpKw, sourceKw)) {
                    log.info("===processNotEqualOldKw expansionId:{} sourceKw:{} cleanUpKw:{} ", expansionId, sourceKw, cleanUpKw);
                    if (sourceKwPEncodeKwMap.containsKey(sourceKw)) {
                        log.info("==removeOldSkw expansionId:{} sourceKw:{} cleanUpKw:{}", expansionId, sourceKw, cleanUpKw);
                        sourceKwPEncodeKwMap.remove(sourceKw);
                    }
                    String encodeKeyword = "";
                    try {
                        encodeKeyword = CommonDataService.encodeQueueBaseKeyword(cleanUpKw.toLowerCase());
                    } catch (UnsupportedEncodingException e) {
                        log.info("==reEncodeError expansionId:{} sourceKw:{} cleanUpKw:{}", expansionId, sourceKw, cleanUpKw);
                        e.printStackTrace();
                        encodeKeyword = FormatUtils.encodeKeyword(cleanUpKw.toLowerCase());
                    }
                    sourceKwPEncodeKwMap.put(cleanUpKw, encodeKeyword);
                }
                if (StringUtils.containsIgnoreCase(keywordName, "lafosslopi")) {
                    log.info("==lafosslopi expansionId:{} keywordName:{} cleanUpKw:{} encodeBySkw:{} encodeByCleanup:{} ", expansionId, keywordName, cleanUpKw, sourceKwPEncodeKwMap.get(keywordName), sourceKwPEncodeKwMap.get(cleanUpKw));
                }
            } else {
                cleanUpSkipKwList.add(keywordName);
            }
        }
    }

    private Map<String, List<DataSeoResult>> genExpansionSeedKw(int expansionId, DataSeoResponse dataSeoTask, Map<String, String> sourceKwPEncodeKwMap) {
        List<DataSeoTask> tasks = dataSeoTask.getTasks();
        if (tasks == null || tasks.isEmpty()) {
            log.info("==sourceKwCntEmpty expansionId:{}", expansionId);
            return null;
        }
        String seedKw = "";
        Map<String, List<DataSeoResult>> resultMap = new HashMap<>();
        Date createDate = new Date();
        int sourceKwCnt = 0;

        for (DataSeoTask task : tasks) {
            List<DataSeoResult> result = task.getResult();
            if (result == null || result.isEmpty()) {
                continue;
            }
            if (StringUtils.isEmpty(seedKw)) {
                DataSeoData data = task.getData();
                if (data != null) {
                    List<String> keywords = data.getKeywords();
                    if (keywords != null && !keywords.isEmpty()) {
                        seedKw = keywords.get(0);
                    }
                }
            }
            for (DataSeoResult dataSeoResult : result) {
                sourceKwCnt++;
                String keyword = FormatUtils.decodeKeyword(dataSeoResult.getKeyword());
//                String keyword = dataSeoResult.getKeyword();
                String encodeKeyword = null;
                try {
                    encodeKeyword = CommonDataService.encodeQueueBaseKeyword(keyword.toLowerCase());
                } catch (UnsupportedEncodingException e) {
                    log.info("==encodeError keyword:{}", keyword);
                    e.printStackTrace();
                    encodeKeyword = FormatUtils.encodeKeyword(keyword.toLowerCase());
                }

                if (resultMap.containsKey(keyword)) {
                    resultMap.get(keyword).add(dataSeoResult);
                } else {
                    List<DataSeoResult> list = new ArrayList<>();
                    list.add(dataSeoResult);
                    resultMap.put(keyword, list);
                    sourceKwPEncodeKwMap.put(keyword, encodeKeyword);
                }
            }
        }

        if (StringUtils.isEmpty(seedKw)) {
            log.info("==seedKwEmpty expansionId:{}", expansionId);

        }

        log.info("==sourceKwCnt expansionId:{} sourceKwCnt:{} resultMapCnt:{} sourceKwPEncodeKwMapCnt:{}", expansionId, sourceKwCnt, resultMap.size(), sourceKwPEncodeKwMap.size());
        return resultMap;
    }



    private String getQueueInfo(String queueName) {
        String queueUrl = null;
        try {
            GetQueueUrlResult result = amazonSQS.getQueueUrl(new GetQueueUrlRequest(queueName));
            queueUrl = result.getQueueUrl();
        } catch (Exception e) {
            queueUrl = SQSUtils.createQueue(queueName, amazonSQS);
        }
        return queueUrl;
    }

    private void clearQueue(boolean ifClear) {
        String dUrl = getQueueInfo(QUEUE_NAME_DESKTOP);
        String mUrl = getQueueInfo(QUEUE_NAME_MOBILE);
        Map<String, Integer> messagesAvailableAndMessageInFilght = SQSUtils.getMessagesAvailableAndMessageInFilght(amazonSQS, dUrl);
        Integer totalD = messagesAvailableAndMessageInFilght.get("MESSAGES_AVAILABLE");
        Integer inFlightD = messagesAvailableAndMessageInFilght.get("MESSAGES_IN_FLIGHT");
        log.info("==desktop total: {}, inFlight: {}", totalD, inFlightD);



        Map<String, Integer> messagesAvailableAndMessageInFilghtM = SQSUtils.getMessagesAvailableAndMessageInFilght(amazonSQS, mUrl);
        Integer totalM = messagesAvailableAndMessageInFilghtM.get("MESSAGES_AVAILABLE");
        Integer inFlightM = messagesAvailableAndMessageInFilghtM.get("MESSAGES_IN_FLIGHT");
        log.info("==mobile total: {}, inFlight: {}", totalM, inFlightM);

        if (ifClear) {
            if (totalD > 0 || inFlightD > 0) {
                SQSUtils.purgeQueue(amazonSQS, dUrl);
            }
            if (totalM > 0 || inFlightM > 0) {
                SQSUtils.purgeQueue(amazonSQS, mUrl);
            }
        }
    }
}
