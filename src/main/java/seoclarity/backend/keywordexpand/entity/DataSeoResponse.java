package seoclarity.backend.keywordexpand.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@EqualsAndHashCode
@AllArgsConstructor
@NoArgsConstructor
public class DataSeoResponse {
    private String version;
    private Integer status_code;
    private String status_message;
    private String time;
    private Float cost;
    private Integer tasks_count;
    private Integer tasks_error;
    private List<DataSeoTask> tasks;
}
