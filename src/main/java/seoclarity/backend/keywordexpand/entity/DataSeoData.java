package seoclarity.backend.keywordexpand.entity;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@EqualsAndHashCode
@AllArgsConstructor
@NoArgsConstructor
public class DataSeoData {
    private String api;
    private String function;
    private String se;
    private List<String> keywords;
    private Integer location_code;
}
