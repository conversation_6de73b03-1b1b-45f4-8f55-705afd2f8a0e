package seoclarity.backend.keywordexpand.command;

import org.apache.commons.io.FileUtils;
import org.apache.commons.io.LineIterator;
import org.apache.commons.lang.StringUtils;
import seoclarity.backend.multithread.core.modlefactory.CacheModleFactory;
import seoclarity.backend.multithread.core.thread.command.BaseThreadCommand;

import java.io.File;
import java.util.*;

public class SplitFileCommand extends BaseThreadCommand {

    private int startLine;

    private int endLine;

    private LineIterator lineIterator;

    private int threadNo;

    private String ip;

    private String parentFilePath;

    private String parentDictPath;

    private int countryType;

    private static final String SPLIT_PATH = "/";

    private static final String SPLIT_KEYWORD = "!_!";

    private static final String FILE_TYPE_TXT = ".txt";

    private static final int FILE_BATCH_SIZE = 50000;

    public SplitFileCommand() {
    }

    public SplitFileCommand(int startLine, int endLine, LineIterator lineIterator, String ip, int threadNo, String parentFilePath, String parentDictPath, int countryType) {
        System.out.println("SplitFileCommand create success, ip: " + ip);
        this.startLine = startLine;
        this.endLine = endLine;
        this.lineIterator = lineIterator;
        this.ip = ip;
        this.threadNo = threadNo;
        this.parentFilePath = parentFilePath;
        this.parentDictPath = parentDictPath;
        this.countryType = countryType;
    }

    @Override
    protected void execute() {
        try {
            process();
        } catch (Exception e) {
            e.printStackTrace();
            System.out.println("threadSplitFileError -> threadNo: " + ip + " fileLineStart: " + startLine + " fileLineEnd: " + endLine + " countryType: " + countryType);
            CacheModleFactory.getInstance().setAliveIpAddress(ip);
            System.exit(-1);
        }
    }

    private void process() throws Exception {
        int riKeywordCount = 0;
        Set<String> relatedKeywordList = new HashSet<>();
        int currentLine = startLine;
        String dataStr;
        int engineId = 1;
        int languageId = 1;
        while (lineIterator.hasNext() && StringUtils.isNotBlank(dataStr = lineIterator.nextLine()) && currentLine <= endLine) {
            String relatedKeywordStr = "";
            if (countryType == 0) {
                relatedKeywordStr = dataStr;
            } else {
                String[] relatedInfoArr = dataStr.split("\t");
                engineId = Integer.parseInt(relatedInfoArr[0].trim());
                languageId = Integer.parseInt(relatedInfoArr[1]);
                relatedKeywordStr = relatedInfoArr[2].trim();
            }
            if (StringUtils.isBlank(relatedKeywordStr) || "-".equals(relatedKeywordStr)) {
                continue;
            }

            String[] relatedKeywordArr = relatedKeywordStr.split(SPLIT_KEYWORD);
            for (int i = 0; i < relatedKeywordArr.length; i++) {
                String relatedKeyword = relatedKeywordArr[i];
                if (StringUtils.isBlank(relatedKeyword)) {
                    continue;
                }
//                System.out.println("===getKw:" + relatedKeyword);
                relatedKeywordList.add(relatedKeyword);
                if (relatedKeywordList.size() >= FILE_BATCH_SIZE) {
                    riKeywordCount += relatedKeywordList.size();
//                    FileUtils.writeLines(new File(parentFilePath + SPLIT_PATH + parentDictPath + SPLIT_PATH + engineId + "_" + languageId + "_" + threadNo + FILE_TYPE_TXT), "UTF-8", relatedKeywordList, true);
                    FileUtils.writeLines(new File(parentFilePath + SPLIT_PATH + parentDictPath + SPLIT_PATH + countryType + "_" + engineId + "_" + languageId + SPLIT_PATH + engineId + "_" + languageId + "_" + threadNo + FILE_TYPE_TXT), "UTF-8", relatedKeywordList, true);
                    relatedKeywordList = new HashSet<>();
                }
            }
            if (relatedKeywordList.size() > 0) {
                riKeywordCount += relatedKeywordList.size();
//                FileUtils.writeLines(new File(parentFilePath + SPLIT_PATH + parentDictPath + SPLIT_PATH + engineId + "_" + languageId + "_" + threadNo + FILE_TYPE_TXT), "UTF-8", relatedKeywordList, true);
                FileUtils.writeLines(new File(parentFilePath + SPLIT_PATH + parentDictPath + SPLIT_PATH + countryType + "_" + engineId + "_" + languageId + SPLIT_PATH + engineId + "_" + languageId + "_" + threadNo + FILE_TYPE_TXT), "UTF-8", relatedKeywordList, true);
                relatedKeywordList = new HashSet<>();
            }
        }
//        FileUtils.writeLines(new File(parentFilePath + SPLIT_PATH + "riCountParentFile" + SPLIT_PATH +  "riCountFile_" + threadNo + FILE_TYPE_TXT), "UTF-8", Collections.singletonList(riKeywordCount), true);
        FileUtils.writeLines(new File(parentFilePath + SPLIT_PATH + "riCountParentFile" + SPLIT_PATH + countryType + "_" + engineId + "_" + languageId + SPLIT_PATH + "riCountFile_" + threadNo + FILE_TYPE_TXT), "UTF-8", Collections.singletonList(riKeywordCount), true);
        CacheModleFactory.getInstance().setAliveIpAddress(ip);
    }

    @Override
    protected void undo() throws Exception {

    }
}
