package seoclarity.backend.keywordexpand.command;

import org.apache.commons.io.FileUtils;
import org.apache.commons.io.LineIterator;
import org.apache.commons.lang.StringUtils;
import seoclarity.backend.dao.actonia.keywordexpand.RiRelatedKeywordMonthlyExpansionDao;
import seoclarity.backend.dao.rankcheck.KeywordMonthlyRecommendDAO;
import seoclarity.backend.dao.rankcheck.SeoClarityKeywordEntityDAO;
import seoclarity.backend.dao.rankcheck.SeoClarityKeywordMonthlySearchEngineRelationEntityDAO;
import seoclarity.backend.entity.actonia.keywordexpand.RiRelatedKeywordMonthlyExpansionEntity;
import seoclarity.backend.entity.rankcheck.SeoClarityKeywordEntity;
import seoclarity.backend.keywordexpand.StemRGKeyword;
import seoclarity.backend.keywordexpand.utils.KeywordCleanUpUtils;
import seoclarity.backend.multithread.core.modlefactory.CacheModleFactory;
import seoclarity.backend.multithread.core.thread.command.BaseThreadCommand;
import seoclarity.backend.service.CommonDataService;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

public class CleanupAndStemKwCommand extends BaseThreadCommand {

    private static char[] ILLEGAL_CHAR_ARRAY = new char[] {
            '{', '}', '?', '!', ',',  ':',  ';',  '~',  '\"',
            '！',  '，',  '。',  '【',  '】',  '—',  '“',  '”',
            '’',  '@',  '<',  '>',  '%',  '*',  '\\',  '^',
            '+',  '￼',  '`',  '.',  '*',  '¡',  '¢',  '£',
            '¤',  '¥',  '¦',  '§',  '¨',  '©',  'ª',  '«',
            '¬',  '®',  '¯',  '–',  '—',  '‘',  '’',  '‚',
            '“',  '”',  '„',  '†',  '‡',  '•',  '…',  '‰',
            '€',  '™',  '°',  '±',  '²',  '³',  '´',  'µ',
            '¶',  '·',  '¸',  '¹',  'º',  '»',  '¼',  '½',
            '¾',  '¿',  '×',  '�',
    };

    private static final String UTF8_BOM = "\uFEFF";

    private static final String SPLIT_PATH = "/";

    private static final String SPLIT_KEYWORD = "!_!";

    private static final String FILE_TYPE_TXT = ".txt";

    private static final String PARENT_FILE_PATH = "/home/<USER>/source/keywordExpandMonthly/tmp_file";
//    private static final String PARENT_FILE_PATH = "/home/<USER>/source/radeL/keyword_expand/tmp_file";

    private static final String AFTER_CLEANUP_FILE = "RG_NLKeywordAfterCleanup";

    private static final String KEYWORD_STEAM_FILE = "RG_NLKeywordStream";

    private static final int FILE_BATCH_SIZE = 2000;

    private static final int DB_CHECK_BATCH_SIZE = 2000;

    private File file;
    private String outPutPath;

    private SeoClarityKeywordEntityDAO keywordEntityDAO;

    private SeoClarityKeywordMonthlySearchEngineRelationEntityDAO keywordMonthlySearchEngineRelationEntityDAO;

    private KeywordMonthlyRecommendDAO keywordMonthlyRecommendDAO;

    private RiRelatedKeywordMonthlyExpansionDao riRelatedKeywordMonthlyExpansionDao;

    private int random;

    private String ip;

    private int expandSionId;

    private int nonRGKeywordCount;
    private int finalKeywordCount;

    private int countryType;
    private int engineId;
    private int languageId;
    private boolean isTest;

    public CleanupAndStemKwCommand() {
    }

    public CleanupAndStemKwCommand(File file, String outPutPath, SeoClarityKeywordEntityDAO keywordEntityDAO,
                                   SeoClarityKeywordMonthlySearchEngineRelationEntityDAO keywordMonthlySearchEngineRelationEntityDAO,
                                   KeywordMonthlyRecommendDAO keywordMonthlyRecommendDAO, String ip, int random, int expandSionId,
                                   RiRelatedKeywordMonthlyExpansionDao riRelatedKeywordMonthlyExpansionDao, int nonRGKeywordCount, int finalKeywordCount,
                                   int countryType, int engineId, int languageId, boolean isTest) {
        this.file = file;
        this.outPutPath = outPutPath;
        this.keywordEntityDAO = keywordEntityDAO;
        this.keywordMonthlySearchEngineRelationEntityDAO = keywordMonthlySearchEngineRelationEntityDAO;
        this.keywordMonthlyRecommendDAO = keywordMonthlyRecommendDAO;
        this.riRelatedKeywordMonthlyExpansionDao = riRelatedKeywordMonthlyExpansionDao;
        this.ip = ip;
        this.random = random;
        this.expandSionId = expandSionId;
        this.countryType = countryType;
        this.engineId = engineId;
        this.languageId = languageId;
        this.isTest = isTest;
    }

    @Override
    protected void execute() throws Exception {
        process();
        CacheModleFactory.getInstance().setAliveIpAddress(ip);
    }

    private void process() {
        long totalProcessStart = System.currentTimeMillis();
        cleanUpKw();
        stemKw();
        try {
            FileUtils.writeLines(new File(PARENT_FILE_PATH + SPLIT_PATH + "countPrentFile" + SPLIT_PATH + countryType + "_" + engineId + "_" + languageId + SPLIT_PATH + "nonRGKeywordCount" + "_" + ip + "_" + random + FILE_TYPE_TXT), "UTF-8", Collections.singletonList(finalKeywordCount), true);
        } catch (IOException e) {
            e.printStackTrace();
            System.out.println("==>createNonRGKeywordCountError nonRGKeywordCount: " + nonRGKeywordCount + " finalKeywordCount:" + finalKeywordCount + " ip:" + ip);
        }
        if (!isTest) {
            riRelatedKeywordMonthlyExpansionDao.updateStatus(RiRelatedKeywordMonthlyExpansionEntity.STATUS_CLEANUP, expandSionId);
        }
        long totalEnd = System.currentTimeMillis();
        System.out.println("==>cleanupAndStemWordUseTotalTime:" + (totalEnd - totalProcessStart) + " ms. ip: " + ip + "nonRGKeywordCount: " + nonRGKeywordCount + " finalKeywordCount:" + finalKeywordCount);
    }

    private void cleanUpKw() {
        long cleanupStart = System.currentTimeMillis();
        Map<String, String> encodeKwPKwCheckMap = new TreeMap<>();
        int fileTotalLine = 0;
        int currentFileKeywordCount = 0;
        String fileName = file.getName();
        System.out.println("==>processFileName: " + fileName);
        String[] idInfoArr = file.getName().split("\\.")[0].split("_");
        int engineId = Integer.parseInt(idInfoArr[0]);
        int languageId = Integer.parseInt(idInfoArr[1]);
        LineIterator lineIterator = null;
        try {
            lineIterator = new LineIterator(new BufferedReader(new FileReader(file)));
            while (lineIterator.hasNext()) {
                fileTotalLine++;
                String keywordName = lineIterator.nextLine();
                if (StringUtils.isEmpty(keywordName)) {
                    nonRGKeywordCount++;
//                    System.out.println("===skipEmptyKW");
                    continue;
                }

                // remove Emoji
                if (keywordName.contains("...")) {// TODO
//                    System.out.println("===skipBadEmojiKW: " + keywordName);
                    nonRGKeywordCount++;
                    continue;
                }

                // remove multi space
                String replacedKW = keywordName.replaceAll("  ", " ");
                if (!replacedKW.equals(keywordName)) {
//                    System.out.println("===removeMultiSpaceKW: " + keywordName + "->" + replacedKW);
                    keywordName = replacedKW;
                }

                // remove UTF8-BOM
                if (keywordName.startsWith(UTF8_BOM)) {
                    String strNew = keywordName.replace(UTF8_BOM, "");
//                    System.out.println("===removeUTF8Bom: " + keywordName + "->" + strNew);
                    keywordName = strNew;
                }

                // skip keyword when word numbers is greater than 7
                if (keywordName.trim().split(" ").length > 6) {
//                    System.out.println("===skipKwCount7: " + keywordName);
                    nonRGKeywordCount++;
                    continue;
                }

                String cleanUpKw = KeywordCleanUpUtils.cleanUp(keywordName);
                if (StringUtils.isEmpty(cleanUpKw)) {
//                    System.out.println("===skipCleanUpKwEmpty");
                    nonRGKeywordCount++;
                    continue;
                }

                if (StringUtils.containsAny(cleanUpKw, ILLEGAL_CHAR_ARRAY)) {
//                    System.out.println("===skipKWWithBadChar: " + cleanUpKw);
                    nonRGKeywordCount++;
                    continue;
                }

                if (cleanUpKw.contains("\t")) { // TODO
//                    System.out.println("===skipKWWithTab: " + keywordName);
                    nonRGKeywordCount++;
                    continue;
                }

                String encodedKWName = CommonDataService.encodeQueueBaseKeyword(cleanUpKw);
                if (encodeKwPKwCheckMap.containsKey(encodedKWName)) {
                    nonRGKeywordCount++;
//                    System.out.println("===skipRepeat -> key:" + encodedKWName + " value:" + encodeKwPKwCheckMap.get(encodedKWName));
                } else {
//                    System.out.println("===putInMap -> key:" + encodedKWName + " value:" + encodeKwPKwCheckMap.get(encodedKWName));
                    encodeKwPKwCheckMap.put(encodedKWName, cleanUpKw);
                    currentFileKeywordCount++;
                }

                if (encodeKwPKwCheckMap.size() >= DB_CHECK_BATCH_SIZE) {
                    List<String> finalKeywordList = checkKeywordEntity(encodeKwPKwCheckMap, engineId, languageId);
                    List<String> newFinalKeywordList = finalKeywordList.stream().filter(str -> str.trim().split(" ").length <= 6).collect(Collectors.toList());
                    if (!newFinalKeywordList.isEmpty()) {
//                        System.out.println("==>writerToFinalFileSize:" + finalKeywordList.size());
                        FileUtils.writeLines(new File(PARENT_FILE_PATH + SPLIT_PATH + AFTER_CLEANUP_FILE + SPLIT_PATH + countryType + "_" + engineId + "_" + languageId + SPLIT_PATH + AFTER_CLEANUP_FILE + "_" + ip + "_" + random + FILE_TYPE_TXT), "UTF-8", newFinalKeywordList, true);
                    }
                    encodeKwPKwCheckMap.clear();
                }
            }
            if (!encodeKwPKwCheckMap.isEmpty()) {
                List<String> finalKeywordList = checkKeywordEntity(encodeKwPKwCheckMap, engineId, languageId);
                List<String> newFinalKeywordList = finalKeywordList.stream().filter(str -> str.trim().split(" ").length < 7).collect(Collectors.toList());
                if (!newFinalKeywordList.isEmpty()) {
//                    System.out.println("==>writerToFinalFileSize:" + finalKeywordList.size());
                    FileUtils.writeLines(new File(PARENT_FILE_PATH + SPLIT_PATH + AFTER_CLEANUP_FILE + SPLIT_PATH + countryType + "_" + engineId + "_" + languageId + SPLIT_PATH + AFTER_CLEANUP_FILE + "_" + ip + "_" + random + FILE_TYPE_TXT), "UTF-8", newFinalKeywordList, true);
                }
            }
//            file.deleteOnExit();
        } catch (Exception e) {
            e.printStackTrace();
            System.out.println("==>processFileError -> fileName: " + fileName + " threadNo: " + ip);
            System.exit(-1);
        }
        long cleanupEnd = System.currentTimeMillis();
        System.out.println("==>fileNameKwCount: " + currentFileKeywordCount + " fileTotalLine: " + fileTotalLine + " fileName: " + fileName + " thread ip " + ip + " cleanup costTime " + (cleanupEnd - cleanupStart) + " ms.");
    }

    private void stemKw() {
        StemRGKeyword stemRGKeyword = new StemRGKeyword(PARENT_FILE_PATH + SPLIT_PATH + AFTER_CLEANUP_FILE + SPLIT_PATH + countryType + "_" + engineId + "_" + languageId + SPLIT_PATH + AFTER_CLEANUP_FILE + "_" + ip + "_" + random + FILE_TYPE_TXT, PARENT_FILE_PATH + SPLIT_PATH + KEYWORD_STEAM_FILE + SPLIT_PATH + countryType + "_" + engineId + "_" + languageId + SPLIT_PATH + KEYWORD_STEAM_FILE + "_" + ip + "_" + random + FILE_TYPE_TXT);
        try {
            stemRGKeyword.process();
            finalKeywordCount = stemRGKeyword.getFinalKeywordCount();
        } catch (Exception e) {
            e.printStackTrace();
            System.out.println("==>keywordStemError: " + e.getMessage() + " threadNo: " + ip);
            System.exit(-1);
        }
    }

    @Override
    protected void undo() throws Exception {

    }

    private List<String> checkKeywordEntity(Map<String, String> encodeKwPKwCheckMap, int engineId, int languageId) throws Exception {
        System.out.println("checkKeywordEntityCheckTotalSize: " + encodeKwPKwCheckMap.size());
        List<String> finalKeywordList = new ArrayList<>();
        List<SeoClarityKeywordEntity> exitsKeywordEntityList = keywordEntityDAO.getKeywordEntityByNames(new ArrayList<>(encodeKwPKwCheckMap.keySet()));
        List<Integer> keywordIdList = new ArrayList<>();
        Map<Integer, String> idPEncodeKwMap = new HashMap<>();
        if (null == exitsKeywordEntityList || exitsKeywordEntityList.size() <= 0) {
            for (String keyword : encodeKwPKwCheckMap.values()) {
//                System.out.println("===stayKw:" + keyword);
                finalKeywordList.add(engineId + SPLIT_KEYWORD + languageId + SPLIT_KEYWORD + keyword);
            }
            System.out.println("checkKeywordEntityExistSize: 0 stayKw: " + finalKeywordList.size() + " total: " + encodeKwPKwCheckMap.size());
        } else {
            List<String> exitsEncodeKwList = exitsKeywordEntityList.stream().map(SeoClarityKeywordEntity::getKeywordText).collect(Collectors.toList());
            for (String key : encodeKwPKwCheckMap.keySet()) {
                if (!exitsEncodeKwList.contains(key)) {
                    String stayKw = encodeKwPKwCheckMap.get(key);
                    finalKeywordList.add(engineId + SPLIT_KEYWORD + languageId + SPLIT_KEYWORD + stayKw);
//                    System.out.println("===stayKw:" + stayKw);
                }
            }
            System.out.println("checkKeywordEntityExistSize: " + exitsKeywordEntityList.size() + " stayKw: " + finalKeywordList.size() + " totalSize: " + encodeKwPKwCheckMap.size());
            keywordIdList = exitsKeywordEntityList.stream().map(SeoClarityKeywordEntity::getId).collect(Collectors.toList());
            idPEncodeKwMap = exitsKeywordEntityList.stream().collect(Collectors.toMap(SeoClarityKeywordEntity::getId, SeoClarityKeywordEntity::getKeywordText, (var1, var2) -> var2));
            List<String> finalKeywordNewList = checkKeywordSeRel(keywordIdList, idPEncodeKwMap, engineId, languageId, encodeKwPKwCheckMap);
            finalKeywordList.addAll(finalKeywordNewList);
        }
        return finalKeywordList;
    }

    private List<String> checkKeywordSeRel(List<Integer> checkEncodeKwIdList, Map<Integer, String> idPKeywordMap, int engineId, int languageId, Map<String, String> encodeKwPKwCheckMap) throws Exception {
        int totalSize = checkEncodeKwIdList.size();
        System.out.println("checkKeywordSeRelCheckTotalSize: " + totalSize);
        List<String> finalKeywordList = new ArrayList<>();
        List<Integer> existsKeywordIdList = keywordMonthlySearchEngineRelationEntityDAO.checkExistsInDirtyKeywords(engineId, languageId, checkEncodeKwIdList);
        if (null == existsKeywordIdList || existsKeywordIdList.size() <= 0) {
            System.out.println("checkKeywordSeRelExistSize: 0");
            finalKeywordList = checkKeywordRecommend(checkEncodeKwIdList, idPKeywordMap, engineId, languageId, encodeKwPKwCheckMap);
        } else {
            /*for (Integer existKeywordId : existsKeywordIdList) {
                nonRGKeywordCount++;
                String existKeyword = idPKeywordMap.get(existKeywordId);
                System.out.println("===skipExistWord:" + existKeyword);
            }*/
            nonRGKeywordCount+= existsKeywordIdList.size();
            checkEncodeKwIdList.removeAll(existsKeywordIdList);
            System.out.println("checkKeywordSeRelExistSize: " + existsKeywordIdList.size() + " removeExistSize: " + checkEncodeKwIdList.size() + " toalSize: " + totalSize);
            if (checkEncodeKwIdList.size() > 0) {
                finalKeywordList = checkKeywordRecommend(checkEncodeKwIdList, idPKeywordMap, engineId, languageId, encodeKwPKwCheckMap);
            }
        }
        return finalKeywordList;
    }

    private List<String> checkKeywordRecommend(List<Integer> checkKeywordList, Map<Integer, String> idPKeywordMap, int engineId, int languageId, Map<String, String> encodeKwPKwCheckMap) throws Exception{
        int totalSize = checkKeywordList.size();
        System.out.println("checkKeywordRecommendCheckTotalSize: " + totalSize);
        List<String> finalKeywordList = new ArrayList<>();
        List<Integer> existsKeywordIdList = keywordMonthlyRecommendDAO.checkExists(checkKeywordList, engineId, languageId);
        if (existsKeywordIdList != null && existsKeywordIdList.size() > 0) {
            /*for (Integer existKeywordId : existsKeywordIdList) {
                nonRGKeywordCount++;
                String existKeyword = idPKeywordMap.get(existKeywordId);
                System.out.println("===skipExistsWord:" + existKeyword);
            }*/
            nonRGKeywordCount += existsKeywordIdList.size();
            checkKeywordList.removeAll(existsKeywordIdList);
            System.out.println("checkKeywordRecommendExistSize: " + existsKeywordIdList.size() + " removeExistSize: " + checkKeywordList.size() + " toalSize: " + totalSize);
        }
        if (checkKeywordList.size() > 0) {
            for (Integer keywordId : checkKeywordList) {
                String encodeKw = idPKeywordMap.get(keywordId);
//                System.out.println("===stayKw:" + encodeKw);
                finalKeywordList.add(engineId + SPLIT_KEYWORD + languageId + SPLIT_KEYWORD + encodeKwPKwCheckMap.get(encodeKw));
            }
        }
        return finalKeywordList;
    }
}
