<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE log4j:configuration SYSTEM "log4j.dtd">
<log4j:configuration xmlns:log4j="http://jakarta.apache.org/log4j/" debug="false">
    
    <!-- Console Appender -->
    <appender name="CONSOLE" class="org.apache.log4j.ConsoleAppender">
        <param name="Target" value="System.out"/>
        <layout class="org.apache.log4j.PatternLayout">
            <param name="ConversionPattern" value="%d{yyyy-MM-dd HH:mm:ss.SSS} %-5p[%t] %c{2} - %m%n"/>
        </layout>
    </appender>

    <!-- Rolling File Appender -->
    <appender name="FILE" class="org.apache.log4j.DailyRollingFileAppender">
        <param name="File" value="logs/app.log"/>
        <param name="DatePattern" value="'.'yyyyMMdd"/>
        <param name="Append" value="true"/>
        <layout class="org.apache.log4j.PatternLayout">
            <param name="ConversionPattern" value="%d{HH:mm:ss.SSS} %-5p[%t] %c{2} (%F:%L) - %m%n"/>
        </layout>
    </appender>

    <!-- Root Logger -->
    <root>
        <priority value="INFO"/>
        <appender-ref ref="CONSOLE"/>
        <appender-ref ref="FILE"/>
    </root>

</log4j:configuration>
