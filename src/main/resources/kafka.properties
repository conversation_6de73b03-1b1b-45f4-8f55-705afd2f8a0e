siteclarity.consumer.kafka_bootstrap_servers=173.236.58.194:9092,173.236.41.250:9092
siteclarity.consumer.kafka_topic=scrapyprod-v2
siteclarity.consumer.kafka_group_id=crawl_es_claritydb_newprod_group_cnbackend_g1
siteclarity.consumer.max_poll_records=500
siteclarity.consumer.max_partition_fetch_bytes=104857600
siteclarity.consumer.fetch_max_bytes=23000000
siteclarity.consumer.auto_commit=false;

tiktok.bootstrap.servers=69.175.4.114:9093,69.175.4.150:9093,69.175.4.158:9093
tiktok.topic=ritiktok_7_intl_d_nat


## RI Consumer Config
ri.consumer.kafka.bootstrap.servers=69.175.4.114:9093,69.175.4.150:9093,69.175.4.158:9093
ri.consumer.kafka.bootstrap.ssl.servers=69.175.4.114:9094,69.175.4.150:9094,69.175.4.158:9094
ri.consumer.kafka.bootstrap.internal.servers=10.5.33.200:9092,10.5.33.199:9092,10.5.33.198:9092
#AvgSize: 60KB; Percentile50: 46KB; Percentile75: 84KB
ri.consumer.max.poll.records=300 
# AvgSize: 486.31
# (AvgSize + 10KB) * (max.poll.records / 10) * 1024
ri.consumer.fetch.min.bytes=15246820
# (AvgSize + 10KB) * max.poll.records * 1024
ri.consumer.fetch.max.bytes=152468203
# (AvgSize + 10KB) * max.poll.records / 4 * 1024
ri.consumer.max.partition.fetch.bytes=38117050
# max.wait.ms > Max IO / fetch.max.bytes
ri.consumer.fetch.max.wait.ms=3000
ri.consumer.session.timeout.ms=20000
ri.consumer.heartbeat.interval.ms=1000
ri.consumer.auto.commit=false
ri.consumer.auto.offset.reset=earliest

## RI regular consumer group
ri.daily.consumer.kafka.group.id=ri_consumer_group_regular_daily
ri.weekly.consumer.kafka.group.id=ri_consumer_group_regular_weekly
ri.biweekly.consumer.kafka.group.id=ri_consumer_group_regular_biweekly

## RI Producer Config
ri.producer.kafka.bootstrap.servers=69.175.4.114:9093,69.175.4.150:9093,69.175.4.158:9093
ri.producer.kafka.bootstrap.ssl.servers=69.175.4.114:9094,69.175.4.150:9094,69.175.4.158:9094
ri.producer.kafka.bootstrap.internal.servers=10.5.33.200:9092,10.5.33.199:9092,10.5.33.198:9092
ri.producer.kafka.client.id=ri_producer_client_r1
ri.producer.kafka.acks=1
ri.producer.kafka.compression.type=none
ri.producer.kafka.buffer.memory=268435456
ri.producer.kafka.retries=2
ri.producer.kafka.retry.backoff.ms=100
ri.producer.kafka.batch.size=1048576
ri.producer.kafka.max.block.ms=100000
ri.producer.kafka.max.request.size=5242880
ri.producer.kafka.linger.ms=100
ri.producer.kafka.request.timeout.ms=1000000

## RI Backup Producer Config
ri.backup.producer.kafka.bootstrap.servers=backup-kfk-data-301:9093,backup-kfk-data-302:9093,backup-kfk-data-303:9093
ri.backup.producer.kafka.bootstrap.internal.servers=backup-kfk-data-301_internal:9092,backup-kfk-data-302_internal:9092,backup-kfk-data-303_internal:9092
#ri.backup.producer.kafka.bootstrap.servers=63.251.114.6:9093,63.251.114.11:9093,63.251.114.10:9093
#ri.backup.producer.kafka.bootstrap.internal.servers=172.27.2.7:9092,172.27.2.11:9092,172.27.2.10:9092
ri.backup.producer.kafka.client.id=backup_rank_producer_client
ri.backup.producer.kafka.acks=all
ri.backup.producer.kafka.compression.type=none
ri.backup.producer.kafka.buffer.memory=10485760
ri.backup.producer.kafka.retries=2
ri.backup.producer.kafka.retry.backoff.ms=100
ri.backup.producer.kafka.batch.size=1048576
ri.backup.producer.kafka.max.block.ms=600000
ri.backup.producer.kafka.max.request.size=5242880
ri.backup.producer.kafka.linger.ms=200
ri.backup.producer.kafka.request.timeout.ms=100000

## Special Rank Consumer Config
specRank.consumer.kafka.bootstrap.servers=69.175.4.114:9092,69.175.4.150:9092,69.175.4.158:9092
specRank.consumer.kafka.bootstrap.internal.servers=10.5.33.200:9092,10.5.33.199:9092,10.5.33.198:9092
specRank.consumer.max.poll.records=50
specRank.consumer.fetch.min.bytes=512000
specRank.consumer.fetch.max.bytes=3072000
specRank.consumer.max.partition.fetch.bytes=1536000
specRank.consumer.fetch.max.wait.ms=1000
specRank.consumer.session.timeout.ms=20000
specRank.consumer.heartbeat.interval.ms=1000
specRank.consumer.auto.commit=false
specRank.consumer.auto.offset.reset=earliest


## Rank Kafka Admin Config
rank.admin.kafka.bootstrap.servers=69.175.4.114:9093,69.175.4.150:9093,69.175.4.158:9093
rank.admin.kafka.bootstrap.internal.servers=10.5.33.200:9092,10.5.33.199:9092,10.5.33.198:9092
rank.admin.kafka.group.id=daily-ranking-consumer-03
rank.admin.session.timeout.ms=30000
rank.admin.heartbeat.interval.ms=5000
rank.admin.auto.commit=false
rank.admin.auto.offset.reset=earliest

## SiteHealth Producer Config
siteHealth.producer.kafka.bootstrap.servers=173.236.57.146:9093, 65.60.14.78:9093, 65.60.14.134:9093
siteHealth.producer.kafka.client.id=siteHealth_producer_client
siteHealth.producer.kafka.acks=1
siteHealth.producer.kafka.compression.type=none
siteHealth.producer.kafka.buffer.memory=268435456
siteHealth.producer.kafka.retries=2
siteHealth.producer.kafka.retry.backoff.ms=100
siteHealth.producer.kafka.batch.size=1048576
siteHealth.producer.kafka.max.block.ms=600000
siteHealth.producer.kafka.max.request.size=5242880
siteHealth.producer.kafka.linger.ms=100
siteHealth.producer.kafka.request.timeout.ms=100000


## Rank Kafka JMX
kafka.jmx.service.url=service:jmx:rmi:///jndi/rmi://10.5.33.200:9999/jmxrmi

## Rank Custom Consumer Config
custom.consumer.kafka.bootstrap.servers=69.175.4.114:9093,69.175.4.150:9093,69.175.4.158:9093
custom.consumer.kafka.bootstrap.ssl.servers=69.175.4.114:9094,69.175.4.150:9094,69.175.4.158:9094
custom.consumer.kafka.bootstrap.internal.servers=10.5.33.200:9092,10.5.33.199:9092,10.5.33.198:9092
#AvgSize: 60KB; Percentile50: 46KB; Percentile75: 84KB
custom.consumer.max.poll.records=300 
# AvgSize: 486.31
# (AvgSize + 10KB) * (max.poll.records / 10) * 1024
custom.consumer.fetch.min.bytes=15246820
# (AvgSize + 10KB) * max.poll.records * 1024
custom.consumer.fetch.max.bytes=152468203
# (AvgSize + 10KB) * max.poll.records / 4 * 1024
custom.consumer.max.partition.fetch.bytes=38117050
# max.wait.ms > Max IO / fetch.max.bytes
custom.consumer.fetch.max.wait.ms=3000
custom.consumer.session.timeout.ms=20000
custom.consumer.heartbeat.interval.ms=1000
custom.consumer.auto.commit=false
custom.consumer.auto.offset.reset=earliest

custom.consumer.kafka.group.id = custom_consumer_group_v1