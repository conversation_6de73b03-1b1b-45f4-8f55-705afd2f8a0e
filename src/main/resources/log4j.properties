#config root logger  
log4j.rootLogger = INFO,R,system.out  
log4j.appender.system.out=org.apache.log4j.ConsoleAppender  
log4j.appender.system.out.layout=org.apache.log4j.PatternLayout  
#log4j.appender.system.out.layout.ConversionPattern=Logger-->%5p{%F:%L}-%m%n
log4j.appender.system.out.layout.ConversionPattern=%m%n  
  
log4j.appender.R=org.apache.log4j.DailyRollingFileAppender    
log4j.appender.R.File=dailylog
log4j.appender.R.layout=org.apache.log4j.PatternLayout  
log4j.appender.R.layout.ConversionPattern=Logger-->%5p{%F:%L}-%m%n 