{"completed_count": {"type": "Int32"}, "completed_count_breakdown_amphtml": {"type": "UInt32"}, "completed_count_breakdown_canonical": {"type": "UInt32"}, "completed_count_breakdown_doc": {"type": "UInt32"}, "completed_count_breakdown_external": {"type": "UInt32"}, "completed_count_breakdown_hreflang": {"type": "UInt32"}, "completed_count_breakdown_pagination": {"type": "UInt32"}, "completed_count_breakdown_relational": {"type": "UInt32"}, "conv_crawl_date": {"type": "Int32"}, "crawl_request_id": {"type": "UInt32", "copyFrom": "crawl_request_log_id_i"}, "crawler_state": {"type": "String"}, "crawler_status_code": {"type": "Int32"}, "crawler_status_message": {"type": "String"}, "domain_id_i": {"type": "String"}, "in_progress": {"type": "UInt32"}, "jobId": {"type": "String"}, "kafka_error_summary": {"type": "String"}, "public_ip": {"type": "String"}, "remaining_count": {"type": "Int32"}, "scrapy_info": {"type": "String"}, "speed": {"type": "Float64"}, "crawl_request_date": {"type": "Date", "sourceFormat": "yyyy-MM-dd HH:mm:ss.S", "createFrom": "start_time"}, "starting_url": {"type": "String"}, "starting_url_crawl_status": {"type": "String"}, "title": {"type": "String"}, "total_time": {"type": "String"}, "url_blocked_summary": {"type": "String"}}