{"alt_img_detail": {"type": "String"}, "alt_img_list": {"type": "String"}, "alternate_links": {"type": "String"}, "amphtml_flag": {"type": "Int8"}, "amphtml_href": {"type": "String"}, "analyzed_url_flg_s": {"type": "String"}, "analyzed_url_s": {"type": "String"}, "archive_flg": {"type": "String"}, "archive_flg_x_tag": {"type": "String"}, "blocked_by_robots": {"type": "String"}, "canonical": {"type": "String"}, "canonical_flg": {"type": "String"}, "canonical_header_flag": {"type": "Int8"}, "canonical_header_type": {"type": "String"}, "canonical_type": {"type": "String"}, "canonical_url_is_consistent": {"type": "String"}, "change_tracking_hash_cd_json": {"type": "String"}, "child_link_lang": {"type": "String"}, "child_link_type": {"type": "String"}, "content_extracted_count": {"type": "Int32"}, "content_extracted_flg": {"type": "Int32"}, "content_flg": {"type": "String"}, "content_md5": {"type": "String"}, "content_type": {"type": "String"}, "content_word_count": {"type": "Int32"}, "content_words": {"type": "String"}, "conv_crawl_date": {"type": "Int32"}, "count_of_objects": {"type": "Int32"}, "crawl_date_long": {"type": "Int32"}, "crawl_depth": {"type": "UInt32", "copyFrom": "popularity"}, "crawl_request_date": {"type": "Date", "createFrom": "crawl_date_long"}, "crawl_request_id": {"type": "UInt32", "copyFrom": "crawl_request_log_id_i"}, "custom_data": {"type": "String"}, "description": {"type": "String"}, "description_flg": {"type": "String"}, "description_length": {"type": "Int32"}, "description_simhash": {"type": "String"}, "document_size": {"type": "Int32"}, "domain": {"type": "String"}, "domain_name": {"type": "String"}, "download_latency": {"type": "Float64"}, "download_time": {"type": "Float64"}, "error_message": {"type": "String"}, "final_response_code": {"type": "Int32"}, "folder_level_1": {"type": "String"}, "folder_level_2": {"type": "String"}, "folder_level_3": {"type": "String"}, "folder_level_count": {"type": "Int32"}, "follow_flg": {"type": "String"}, "follow_flg_x_tag": {"type": "String"}, "h1": {"type": "<PERSON><PERSON><PERSON>(String)"}, "h1_count": {"type": "Int32"}, "h1_flg": {"type": "String"}, "h1_length": {"type": "Int32"}, "h1_md5": {"type": "String"}, "h1_simhash": {"type": "String"}, "h2": {"type": "<PERSON><PERSON><PERSON>(String)"}, "h2_simhash": {"type": "String"}, "header_noarchive": {"type": "Int8"}, "header_nofollow": {"type": "Int8"}, "header_noindex": {"type": "Int8"}, "header_noodp": {"type": "Int8"}, "header_nosnippet": {"type": "Int8"}, "header_noydir": {"type": "Int8"}, "hreflang_errors": {"type": "String"}, "hreflang_links": {"type": "String"}, "hreflang_links_href": {"type": "<PERSON><PERSON><PERSON>(String)"}, "hreflang_links_lang": {"type": "<PERSON><PERSON><PERSON>(String)"}, "hreflang_links_out_count": {"type": "Int32"}, "hreflang_links_type": {"type": "<PERSON><PERSON><PERSON>(String)"}, "hreflang_url_count": {"type": "Int32"}, "hsts": {"type": "String"}, "index_flg": {"type": "String"}, "index_flg_x_tag": {"type": "String"}, "indexable": {"type": "Int8"}, "insecure_resources": {"type": "String"}, "insecure_resources_flag": {"type": "Int8"}, "long_redirect": {"type": "Int8", "actualType": "bool"}, "meta_charset": {"type": "String"}, "meta_content_type": {"type": "String"}, "meta_disabled_sitelinks": {"type": "Int8"}, "meta_noodp": {"type": "Int8"}, "meta_nosnippet": {"type": "Int8"}, "meta_noydir": {"type": "Int8"}, "meta_redirect": {"type": "Int8"}, "mixed_redirects": {"type": "Int8"}, "mobile_rel_alternate_url_is_consistent": {"type": "Int8"}, "noodp": {"type": "Int8"}, "nosnippet": {"type": "Int8"}, "noydir": {"type": "Int8"}, "og_markup": {"type": "String"}, "og_markup_flag": {"type": "Int8"}, "og_markup_length": {"type": "Int32"}, "outlink_count": {"type": "Int32"}, "page_1": {"type": "Int8"}, "page_analysis_issue_count": {"type": "Int8"}, "page_analysis_results": {"type": "String"}, "page_link": {"type": "String"}, "page_link_destination_url": {"type": "<PERSON><PERSON><PERSON>(String)"}, "page_link_destination_url_hash": {"type": "A<PERSON>y(UInt64)"}, "page_timeout_flag": {"type": "Int8", "actualType": "bool"}, "paginated": {"type": "Int8", "actualType": "bool"}, "pagination_links": {"type": "String"}, "parent_page": {"type": "String"}, "post_processing_analysed": {"type": "String"}, "post_processing_hreflang_issue_count": {"type": "Int32"}, "post_processing_issue_count": {"type": "Int32"}, "post_processing_issues": {"type": "String"}, "post_processing_rule_json": {"type": "String"}, "protocol": {"type": "String"}, "redirect_blocked": {"type": "Int8"}, "redirect_blocked_reason": {"type": "String"}, "redirect_chain": {"type": "String"}, "redirect_final_url": {"type": "String"}, "redirect_flg": {"type": "Int8"}, "redirect_times": {"type": "Int32"}, "rel_next_html_url": {"type": "String"}, "rel_next_url_is_consistent": {"type": "Int8"}, "rel_prev_url_is_consistent": {"type": "Int8"}, "request_headers": {"type": "String"}, "request_time": {"type": "String"}, "response_code": {"type": "String"}, "response_headers": {"type": "String"}, "retry_attempted": {"type": "Int8"}, "robots": {"type": "String"}, "robots_contents": {"type": "String"}, "robots_contents_x_tag": {"type": "Int8", "actualType": "bool"}, "robots_flg": {"type": "String"}, "robots_flg_x_tag": {"type": "String"}, "server_response_time": {"type": "String"}, "source_url": {"type": "String"}, "splash_took": {"type": "String"}, "status": {"type": "Int32"}, "structured_data": {"type": "String"}, "title": {"type": "String"}, "title_flg": {"type": "String"}, "title_length": {"type": "Int32"}, "title_md5": {"type": "String"}, "title_simhash": {"type": "String"}, "twitter_description_length": {"type": "Int32"}, "twitter_markup": {"type": "String"}, "twitter_markup_flag": {"type": "Int8"}, "twitter_markup_length": {"type": "Int32"}, "url": {"type": "String"}, "url_hash": {"type": "UInt64"}, "url_length": {"type": "Int32"}, "url_murmur_hash": {"type": "UInt32"}, "valid_twitter_card": {"type": "String"}, "viewport_content": {"type": "String"}, "viewport_flag": {"type": "Int8"}, "post_processing_issues_array": {"type": "<PERSON><PERSON><PERSON>(String)"}, "og_markup_property": {"type": "<PERSON><PERSON><PERSON>(String)"}, "og_markup_content": {"type": "<PERSON><PERSON><PERSON>(String)"}, "twitter_markup_property": {"type": "<PERSON><PERSON><PERSON>(String)"}, "twitter_markup_content": {"type": "<PERSON><PERSON><PERSON>(String)"}, "pagination_links_direction": {"type": "<PERSON><PERSON><PERSON>(String)"}, "lang": {"type": "String"}}