{"docAdditionalFields": ["content_words_exclude.count", "content_words_exclude.source", "content_words_exclude.word", "content_words_include.count", "content_words_include.source", "content_words_include.word", "content_words_search.count", "content_words_search.source", "content_words_search.word", "custom_data.content", "custom_data.index", "custom_data.links", "custom_data.match_found", "custom_data.selector", "custom_data.selector_type", "custom_data.word_count", "h1", "h2", "hreflang_links_href", "hreflang_links_lang", "hreflang_links_type", "og_markup_content", "og_markup_property", "page_link_destination_url", "pagination_links_direction", "twitter_markup_content", "twitter_markup_property", "redirect_chain", "response_headers", "twitter_markup_property", "redirect_chain", "response_headers", "external_link", "canonical_hash", "url_hash", "hreflang_links_href_hash_array", "page_analysis_issues_array", "title_stemmed", "description_stemmed", "h1_stemmed", "h2_stemmed", "title_murmurhash", "description_murmurhash", "h1_murmurhash", "h2_murmurhash", "page_link_destination_url_hash", "structure_schema_warnings.schema_type", "structure_schema_warnings.encoding", "structure_schema_warnings.message", "structure_schema_warnings.path", "structure_schema_warnings.warning_type", "structure_schema_errors.schema_type", "structure_schema_errors.encoding", "structure_schema_errors.message", "structure_schema_errors.path", "structure_schema_errors.error_type", "structured_schema.type", "structured_schema.encoding", "page_analysis_issues", "resources.url", "resources.status_code", "resources.is_blocked", "resources.request_type", "resources.is_from_memory_cache", "resources.types", "structure_schema_errors.info", "structure_schema_warnings.info", "page_link_destination_url_murmur_hash"], "canonicalAdditionalFields": ["h1", "h2", "hreflang_links_href", "hreflang_links_lang", "hreflang_links_type", "og_markup_content", "og_markup_property", "page_link_destination_url", "pagination_links_direction", "post_processing_issues_array", "twitter_markup_content", "twitter_markup_property", "url_hash", "redirect_chain", "response_headers", "page_link_destination_url_hash"], "hreflangAdditionalFields": ["h1", "h2", "hreflang_links_href", "hreflang_links_lang", "hreflang_links_type", "og_markup_content", "og_markup_property", "page_link_destination_url", "pagination_links_direction", "post_processing_issues_array", "twitter_markup_content", "twitter_markup_property", "url_hash", "redirect_chain", "response_headers", "page_link_destination_url_hash"], "fieldMapping": {"hreflang_links.lang": "hreflang_links_lang", "hreflang_links.type": "hreflang_links_type", "hreflang_links.href": "hreflang_links_href", "popularity": "crawl_depth", "post_processing_issues": "post_processing_issue_count", "og_markup.property": "og_markup_property", "og_markup.content": "og_markup_content", "twitter_markup.property": "twitter_markup_property", "twitter_markup.content": "twitter_markup_content", "pagination_links.direction": "pagination_links_direction"}, "hreflang_page_analysis_rules": ["page_analysis_rule_76_b", "page_analysis_rule_77_b", "page_analysis_rule_78_b", "page_analysis_rule_79_b", "page_analysis_rule_80_b", "page_analysis_rule_82_b", "page_analysis_rule_83_b", "page_analysis_rule_84_b", "page_analysis_rule_96_b", "page_analysis_rule_97_b", "page_analysis_rule_98_b", "page_analysis_rule_99_b", "page_analysis_rule_100_b", "page_analysis_rule_105_b"], "canonical_page_analysis_rules": ["page_analysis_rule_57_b", "page_analysis_rule_59_b", "page_analysis_rule_61_b", "page_analysis_rule_62_b", "page_analysis_rule_64_b", "page_analysis_rule_65_b", "page_analysis_rule_73_b", "page_analysis_rule_74_b", "page_analysis_rule_75_b", "page_analysis_rule_95_b"], "docTable": "dis_site_crawl_doc", "canonicalTable": "dis_site_crawl_canonical", "hreflangTable": "dis_site_crawl_hreflang", "endOfCrawlDocTable": "dis_crawl_stats", "structuredDataSubTable": "dis_site_crawl_structured_schema", "customDataNestedProperties": ["content", "index", "links", "match_found", "selector", "selector_type", "word_count"], "contentWordsFirstLevelProperties": ["include", "exclude", "search"], "contentWordsSecondLevelProperties": ["count", "source", "word"], "docFieldsToBeParsed": ["h1", "h2", "hreflang_links", "page_link", "pagination_links", "twitter_markup", "content_words", "custom_data", "og_markup", "structured_data", "resources"], "stemmedFields": ["title", "description", "h1", "h2"], "murmur3HashFields": ["title", "description", "h1", "h2"], "endOfCrawlAdditionalFields": ["scrapy_info", "kafka_error_summary", "url_blocked_summary", "completed_count_breakdown_amphtml", "completed_count_breakdown_canonical", "completed_count_breakdown_doc", "completed_count_breakdown_external", "completed_count_breakdown_hreflang", "completed_count_breakdown_pagination", "completed_count_breakdown_relational"], "endOfCrawlStoreAsStringFields": ["scrapy_info", "kafka_error_summary", "url_blocked_summary"], "structuredSchemaAdditionalFields": ["encoding", "schema_type", "errors.path", "errors.type", "warning_count", "markup", "validation_errors", "warnings.message", "warnings.path", "warnings.type", "errors.message", "error_count"]}