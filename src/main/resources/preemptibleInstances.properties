endpoint=ecs.us-east-1.aliyuncs.com
accessKey=LTAI4GGYqGWn99UKbJsBgyQ9
secretKey=******************************

# create instance config info
destinationResource=InstanceType
#instanceType=ecs.t6-c1m1.large
instanceType=ecs.u1-c1m1.large
# east
regionId=us-east-1
zoneId=us-east-1a
systemDiskCategory=cloud_efficiency
systemDiskSize=40
networkCategory=vpc
networkType=vpc
chargeType=PostPaid
spotStrategy=SpotWithPriceLimit
spotPriceLimit=0.013
resourceType=instance
spotDuration=0
vSwitchId=vsw-0xixh6y34xuygpyqw0paq
securityGroupId=sg-0xie0hy06f6x3dxmdyjz
InternetMaxBandwidthIn=5
InternetMaxBandwidthOut=5
KeyPairName=render
networkInterfaceName=eth0
############ v3 ################
imageName=vs3-local-20231212
HostName=vs3-[1,5]-local
instanceName=vs3-[1,5]-local
mobileInstanceName=vs3M-[1,5]-local
desktopInstanceName=vs3D-[1,5]-local
############ v3 ################
############ v2 ################
imageName_v2=pixel-v5
HostName_v2=vs2-[1,5]-local
instanceName_v2=vs2-[1,5]-local
mobileInstanceName_v2=vs2M-[1,5]-local
desktopInstanceName_v2=vs2D-[1,5]-local
############ v2 ################
############ productClick ################
imageName_productClick=product_click
hostName_productClick=productClick-[1,5]-local
instanceName_productClick=productClick-[1,5]-local
############ productClick ################