<div style=" background: #f3f6f9; padding: 15px 0; border-radius: 5px;">

    <div style="margin:0 auto; width:700px; color:#333; font:12px/1.5 Verdana, Geneva, sans-serif; border-radius: 5px; background: #fff; $!{DEFAULT_ALERT_CSS_PARAM}">

        <!-- head -->
        <div style=" padding: 5px 15px;">
            <span style="float:right; padding-top:10px; font-weight:bold; color:#777;">$!etHeadTitle </span>

            <a target="_blank" href="$!{agencyInfo.websiteDomain}">
                <img style="border: none;" src="$!{agencyInfo.websiteDomain}/images/company/$!{agencyInfo.logoImage}/logo_email.png" title="$!{agencyInfo.companyTitle} Logo" alt="$!{agencyInfo.companyTitle} Logo" height="30" />
            </a>
        </div>

        <!-- body -->
        <div style="padding:25px 15px;">
            Hi ${domainName}, <br/> <br/>
            ${successMessage}. <br/>
            <!-- body cnt-->
            <div style="margin:15px 0;">


                    #if ($alertInfoList)
                        #foreach ($map in $alertInfoList)
                        #set($infoId = $map.get('infoId'))
                        #set($splitChar = "_")
                        #set($matchedStr = "Keywords")
                        #if(${map.get('typeId')} == "1003")
                        	#set($matchedStr = "SERP Feature types")
                        #end
                        #set($commonTextAlign = $map.get('commonTextAlign'))
                        <div style="margin-top:30px;">
                            <span style="font-size: 16px; font-weight: bold;color:#4e4e4e; ">$map.get('infoSubject')</span>
                            <span style="font-size: 12px; font-weight: normal; color: #8c8c8c;">(Matched ${matchedStr}: ${map.get('summary')})</span>
                            #if($map.get('infoUrl') != "")
                            <a target="_blank" href="$map.get('infoUrl')">View All</a><br/>
                            #end
                        </div>
                            #if ($map.get('tableRows'))
                            <table style="border-collapse: collapse; border-spacing: 0;  width: 100%; font-size: 13px; margin: 10px auto 20px; table-layout: fixed;">
                                <thead>
                                    #foreach ($cols in ${map.get('headers')})
                                    <tr>
                                    	#foreach ($col in $cols)
	                                    	#set($attributeKey = "$infoId$splitChar$col")
	                                    	#set($attribute = "$!HEADER_COL_ATTRIBUE_MAP.get($attributeKey)")
	                                    	#set($textAlign = "$commonTextAlign")
	                                    	#if(${attribute.indexOf("colspan")} != -1)
	                                    		#set($textAlign = "center")
	                                    	#end
                                    		<th style="text-align: ${textAlign}; border-bottom: 1px #f1f1f1 solid;  color: #AEB5BF; padding: 5px;" ${attribute}>$col</th>
                                    	#end
                                    </tr>
                                    #end
                                </thead>
                                <tbody>
                                #foreach ($item in ${map.get('tableRows')})
                                <tr>
                                    #foreach ($col in $item)
                                    <td style=" padding: 8px 5px; word-break: break-all; text-align: ${commonTextAlign};">$col</td>
                                    #end
                                </tr>
                                #end
                                </tbody>
                            </table>
                            #end
                            
                        #end
                    #end


            </div>

            Sincerely,<br/>
            $!{agencyInfo.companyTitle}

        </div>

    </div>

    <div style="text-align: center; padding-top:15px;">
        <!-- foot -->

        <a target="_blank" href="$!{agencyInfo.websiteDomain}">
            <img style="border: none; height:20px;" src="$!{agencyInfo.websiteDomain}/images/company/$!{agencyInfo.logoImage}/logo_email.png" title="$!{agencyInfo.companyTitle} Logo" alt="$!{agencyInfo.companyTitle} Logo" height="30" />
        </a>

        <div style=" padding-top:15px; text-align: center; line-height:1.5; color:#999;">

            $!{agencyInfo.emailFooter.address} <br/>
            <div >
                Support: <a href="mailto:$!{agencyInfo.emailReplyto}">$!{agencyInfo.emailReplyto}</a> <br/>
                Phone: $!{agencyInfo.emailFooter.phone} <br/>
                Fax: $!{agencyInfo.emailFooter.fax}
            </div>

            <input type="hidden" value="$!hostName"/>

        </div>

      
    </div>

</div>

<p style="text-align:center; margin-top: 20px; color:#999; font-size: x-small;font-family: 'Proxima Nova', 'Open Sans', 'Helvetica Neue', Calibri, Helvetica, sans-serif;">
    Do not wish to receive these emails? <br/> 
    Simply write to <a href="mailto:<EMAIL>"><EMAIL></a> to have these emails disabled for your account
</p>