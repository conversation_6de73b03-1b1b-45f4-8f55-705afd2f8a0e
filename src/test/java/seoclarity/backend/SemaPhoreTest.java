package seoclarity.backend;

import java.util.ServiceLoader;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Semaphore;

public class SemaPhoreTest {

	public static void main(String[] args) {
		ExecutorService eService = Executors.newFixedThreadPool(1);
		
//		ServiceLoader<S>
		
		final Semaphore semaphore = new Semaphore(4);
		
		for (int i = 0; i < 50; i++) {
			
			System.out.println("RUning ..: "+i);
			try {
				semaphore.acquire();
			} catch (InterruptedException e1) {
				e1.printStackTrace();
			}
			Runnable task = new Runnable() {
				
				@Override
				public void run() {
					try {
						

						System.out.println("Accessing: " );
                        Thread.sleep((long) (Math.random() * 6000));
						
						semaphore.release();
						
					} catch (InterruptedException e) {
						// TODO Auto-generated catch block
						e.printStackTrace();
					}
					
				}
			};
			
			eService.execute(task);
			
			
		}
		
		eService.shutdown();

	}

}
