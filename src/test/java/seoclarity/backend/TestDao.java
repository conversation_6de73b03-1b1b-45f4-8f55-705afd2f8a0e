package seoclarity.backend;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.io.IoUtil;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVPrinter;
import org.apache.commons.csv.CSVRecord;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateUtils;
import org.simmetrics.StringDistance;
import org.simmetrics.metrics.Levenshtein;
import seoclarity.backend.dao.actonia.KeywordEntityDAO;
import seoclarity.backend.utils.BotUtils;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.SpringBeanFactory;

import java.io.*;
import java.util.*;

/**
 * rm -f ./src/main/java/seoclarity/backend/onetime/TestDao.java && vi ./src/main/java/seoclarity/backend/onetime/TestDao.java
 *
 * mvn clean compile
 * nohup mvn exec:java -Dexec.mainClass="seoclarity.backend.onetime.TestDao" -Dexec.cleanupDaemonThreads=false > test.log &
 */
public class TestDao {
	public static void main(String[] args) throws IOException {

		KeywordEntityDAO keywordEntityDAO = SpringBeanFactory.getBean("keywordEntityDAO");
		List<String> kws = IOUtils.readLines(new FileInputStream(new File("/home/<USER>/09kx_sv_1_1_sv_v2/20180930_1_1.csv")));
		List<List<String>> errorKws = CollectionUtil.split(kws, 200);
		Set<Integer> domainId = new HashSet<>();
		for (List<String> errorKw : errorKws) {
			StringBuilder sql = new StringBuilder();
			for (String kw : errorKw) {
				sql.append("'"+FormatUtils.encodeKeyword(kw)+"',");
			}
			sql.setLength(sql.length()-1);
			List<Integer> domainIds = keywordEntityDAO.getKeywordDomainByKeywordName(sql.toString());
			domainId.addAll(domainIds);
			System.out.println("Finished one page : "+StringUtils.join(domainIds, ","));
		}

		System.out.println("Final : "+StringUtils.join(domainId, ","));
		System.exit(1);



		File inBad = new File("/home/<USER>/keywordEx/keywordEx_full_bad.csv_bak");
		CSVFormat csvFormat = CSVFormat.DEFAULT.withFirstRecordAsHeader();
		CSVParser csvParser = new CSVParser(new FileReader(inBad), csvFormat);
		List<CSVRecord> csvRecords = csvParser.getRecords();
		Map<String, Set<String>> setMap = new HashMap<>();
		for (CSVRecord csvRecord : csvRecords) {
			String kName = csvRecord.get("newKeyword");
			String eId = csvRecord.get("engineId");
			String lId = csvRecord.get("languageId");
			String key = eId+"!_!"+lId;
			Set<String> set = setMap.get(key);
			if (null == set) {
				set = new HashSet<>();
			}
			if (!set.contains(kName)) {
				set.add(kName);
			}
			setMap.put(key, set);
		}
		File inDir = new File("/home/<USER>/09kx_1_1_sv/");
		CSVFormat inCsv = CSVFormat.DEFAULT.withHeader("LocationId","LocationName","Keyword","Avg SearchVol","Aug/2018",
				"Jul/2018","Jun/2018","May/2018","Apr/2018","Mar/2018","Feb/2018","Jan/2018","Dec/2017","Nov/2017","Oct/2017","Sep/2017","CPC")
				.withSkipHeaderRecord();
		CSVFormat inCsv2 = CSVFormat.DEFAULT.withHeader("keyword")
				;
		for (File file : inDir.listFiles()) {
			String name = file.getName();
			if (!name.endsWith(".csv")) {
				continue;
			}
			String engine = StringUtils.split(name, "_")[1];
			String language = StringUtils.split(name, "_")[2].replaceAll(".csv", "");
			String key = engine+"!_!"+language;
			Set<String> set = setMap.get(key);
			CSVParser csvParser1 = new CSVParser(new FileReader(file), inCsv);
			CSVPrinter csvPrinter = new CSVPrinter(new FileWriter(file.getAbsolutePath().replaceAll("09kx_1_1_sv", "09kx_sv_1_1_sv_v2")), inCsv2);
			List<CSVRecord> csvRecords1 = csvParser1.getRecords();
			for (CSVRecord csvRecord : csvRecords1) {
				String kName = csvRecord.get("Keyword");
				if (null == set || !set.contains(kName)) {
					continue;
				}
				csvPrinter.printRecord(kName);
//				csvPrinter.printRecord(new Object[] {
//						csvRecord.get("LocationId"),
//						csvRecord.get("LocationName"),
//						csvRecord.get("Keyword"),
//						csvRecord.get("Avg SearchVol"),
//						csvRecord.get("Aug/2018"),
//						csvRecord.get("Jul/2018"),
//						csvRecord.get("Jun/2018"),
//						csvRecord.get("May/2018"),
//						csvRecord.get("Apr/2018"),
//						csvRecord.get("Mar/2018"),
//						csvRecord.get("Feb/2018"),
//						csvRecord.get("Jan/2018"),
//						csvRecord.get("Dec/2017"),
//						csvRecord.get("Nov/2017"),
//						csvRecord.get("Oct/2017"),
//						csvRecord.get("Sep/2017"),
//						csvRecord.get("CPC")
//				});
			}
			csvPrinter.flush();
			csvPrinter.close();
		}


//		File file = new File("/home/<USER>/keywordEx/keywordEx.csv");
//		File outFile = new File("/home/<USER>/keywordEx/keywordEx_full_bad.csv");
//		CSVPrinter csvPrinter = new CSVPrinter(new BufferedWriter(new FileWriter(outFile)), CSVFormat.DEFAULT.withHeader("keyword", "newKeyword", "engineId", "languageId"));
//		CSVFormat csvFormat = CSVFormat.DEFAULT;
//		CSVParser csvParser = new CSVParser(new FileReader(file), csvFormat);
//		List<CSVRecord> csvRecords = csvParser.getRecords();
//		for (CSVRecord csvRecord : csvRecords) {
//			if (StringUtils.equalsIgnoreCase(csvRecord.get(1), "-")) {
//				continue;
//			}
//			csvPrinter.printRecord(csvRecord.get(0), csvRecord.get(1), csvRecord.get(2), csvRecord.get(3));
//		}
//		file = new File("/home/<USER>/keywordEx/keywordEx_4countries.csv");
//		csvParser = new CSVParser(new FileReader(file), csvFormat);
//		csvRecords = csvParser.getRecords();
//		for (CSVRecord csvRecord : csvRecords) {
//			if(csvRecord.size() < 4) {
//				System.out.println("error line :"+csvRecord.toString());
//				continue;
//			}
//			if (StringUtils.equalsIgnoreCase(csvRecord.get(1), "-")) {
//				continue;
//			}
//			csvPrinter.printRecord(csvRecord.get(0), csvRecord.get(1), csvRecord.get(2), csvRecord.get(3));
//		}
//		csvPrinter.flush();
//		csvPrinter.close();

		//把SV正常的keywird换成google recommend的
//		Map<String, Map<String, String>> badKeywords = new HashMap<>();
//		Map<String, List<Object[]>> outPutMaps = new HashMap<>();
//		File outFile = new File("/home/<USER>/keywordEx/keywordEx_full_bad.csv");
//		CSVFormat csvFormat = CSVFormat.DEFAULT.withHeader("keyword","newKeyword","engineId","languageId").withSkipHeaderRecord();
//		CSVParser csvParserB = new CSVParser(new FileReader(outFile), csvFormat);
//		List<CSVRecord> csvRecordsB = csvParserB.getRecords();
//		for (CSVRecord strings : csvRecordsB) {
//			String key = strings.get("engineId")+"!_!"+strings.get("languageId");
//			Map<String, String> kws = badKeywords.get(key);
//			if (kws == null) {
//				kws = new HashMap<>();
//			}
//			kws.put(FormatUtils.decoderString(strings.get("keyword")), strings.get("newKeyword"));
//			badKeywords.put(key, kws);
//		}
//		System.out.println("badKeywords size : "+badKeywords.size());
//
//		File inputFile = new File("/home/<USER>/keywordEx/save/rankcheck_done/");
//		CSVFormat inCsv = CSVFormat.DEFAULT.withHeader("LocationId","LocationName","Keyword","Avg SearchVol","Aug/2018","Jul/2018","Jun/2018","May/2018","Apr/2018","Mar/2018","Feb/2018","Jan/2018","Dec/2017","Nov/2017","Oct/2017","Sep/2017","CPC","EngineId","LanguageId")
//				.withSkipHeaderRecord();
//		for (File file : inputFile.listFiles()) {
//			CSVParser csvParser = new CSVParser(new FileReader(file), inCsv);
//			List<CSVRecord> csvRecords = csvParser.getRecords();
//			for (CSVRecord csvRecord : csvRecords) {
//				String key = csvRecord.get("EngineId")+"!_!"+csvRecord.get("LanguageId");
//				List<Object[]> list = outPutMaps.get(key);
//				if (list == null) {
//					list = new ArrayList<>();
//				}
//				String kName = csvRecord.get("Keyword");
//				if (null != badKeywords.get(key) && badKeywords.get(key).containsKey(kName)) {
//					System.out.println("Update keyword from "+kName+" !to! "+badKeywords.get(key).get(kName));
//					kName = badKeywords.get(key).get(kName);
//				}
//				list.add(new Object[] {
//						csvRecord.get("LocationId"),
//						csvRecord.get("LocationName"),
//						kName,
//						csvRecord.get("Avg SearchVol"),
//						csvRecord.get("Aug/2018"),
//						csvRecord.get("Jul/2018"),
//						csvRecord.get("Jun/2018"),
//						csvRecord.get("May/2018"),
//						csvRecord.get("Apr/2018"),
//						csvRecord.get("Mar/2018"),
//						csvRecord.get("Feb/2018"),
//						csvRecord.get("Jan/2018"),
//						csvRecord.get("Dec/2017"),
//						csvRecord.get("Nov/2017"),
//						csvRecord.get("Oct/2017"),
//						csvRecord.get("Sep/2017"),
//						csvRecord.get("CPC"),
//				});
//				outPutMaps.put(key, list);
//			}
//		}
//
//		CSVFormat ouCsv = CSVFormat.DEFAULT.withHeader("LocationId","LocationName","Keyword","Avg SearchVol","Aug/2018","Jul/2018","Jun/2018","May/2018","Apr/2018","Mar/2018","Feb/2018","Jan/2018","Dec/2017","Nov/2017","Oct/2017","Sep/2017","CPC","EngineId","LanguageId");
//		for (Map.Entry<String, List<Object[]>> listEntry : outPutMaps.entrySet()) {
//			String k = listEntry.getKey();
//			String engine = k.split("!_!")[0];
//			String language = k.split("!_!")[1];
//
//			CSVPrinter csvPrinter = new CSVPrinter(new BufferedWriter(
//					new FileWriter("/home/<USER>/09kx_sv/"+20180930+"_"+engine+"_"+language+".csv")), ouCsv);
//			for (Object[] objects : listEntry.getValue()) {
//				csvPrinter.printRecord(objects);
//			}
//			csvPrinter.flush();
//			csvPrinter.close();
//		}

		//tTEMP
//		File dir = new File("/home/<USER>/20180830_intl");
//		CSVFormat csvFormat = CSVFormat.DEFAULT.withFirstRecordAsHeader().withSkipHeaderRecord();
//		Map<String, Set<String>> dailyKeywords = new HashMap<>();
//		Map<String, List<Object[]>> extractKeywords = new HashMap<>();
//		for (File file : dir.listFiles()) {
//			String name = file.getName();
//			String engine = StringUtils.split(name, "_")[1];
//			String language = StringUtils.split(name, "_")[2].replaceAll(".csv", "");
//			String key = engine+"!_!"+language;
//			Set<String> kws = dailyKeywords.get(key);
//			if (null == kws) {
//				kws = new HashSet<>();
//			}
//
//			CSVParser csvParser = new CSVParser(new FileReader(file), csvFormat);
//			List<CSVRecord> csvRecords = csvParser.getRecords();
//			for (CSVRecord csvRecord : csvRecords) {
//				String kName = csvRecord.get("Keyword");
//				if (!kws.contains(kName)) {
//					kws.add(kName);
//				}
//			}
//			dailyKeywords.put(key, kws);
//			System.out.println(engine+"#"+language+" total kws : "+kws.size());
//		}

//		File[] mFiles = new File[] {
//				, new File("/home/<USER>/monthly_intl/")
//		};
//		String[] header = new  String[] {"LocationId","LocationName","Keyword","Avg SearchVol","Aug/2018","Jul/2018","Jun/2018","May/2018","Apr/2018","Mar/2018","Feb/2018","Jan/2018","Dec/2017","Nov/2017","Oct/2017","Sep/2017","CPC"};
//		CSVFormat outCsvF = CSVFormat.DEFAULT.withHeader(header);
//		File mUsFile = new File("/home/<USER>/monthly_intl/temp/");
//		CSVPrinter csvPrinter = new CSVPrinter(new BufferedWriter(new FileWriter("/home/<USER>/monthly_daily_kws_1_1.csv")), outCsvF);
//		for (File file : mUsFile.listFiles()) {
//			String name = file.getName();
//			String engine = "1";//StringUtils.split(name, "_")[1];
//			String language = "1";//StringUtils.split(name, "_")[2].replaceAll(".csv", "");
//			CSVParser csvParser = new CSVParser(new FileReader(file), csvFormat);
//			List<CSVRecord> csvRecords = csvParser.getRecords();
////			Map<String, CSVRecord> csvRecordMap = new HashMap<>();
//			String key = engine+"!_!"+language;
//			Set<String> kws = dailyKeywords.get(key);
//			for (CSVRecord csvRecord : csvRecords) {
//				String kName = csvRecord.get("Keyword");
//				if (kws.contains(kName)) {
//					Object[] obj = new Object[header.length];
//					for (int i = 0; i < header.length; i++) {
//						obj[i] = csvRecord.get(header[i]);
//					}
//					csvPrinter.printRecord(obj);
//				}
////				csvRecordMap.put(kName, csvRecord);
//			}
//
////		CSVPrinter csvPrinter = new CSVPrinter(new BufferedWriter(new FileWriter("/home/<USER>/monthly_daily_kws_"+engine+"_"+language+".csv")), outCsvF);
////		String key = engine+"!_!"+language;
////		Set<String> kws = dailyKeywords.get(key);
////		for (String kw : kws) {
////			CSVRecord csvRecord = csvRecordMap.get(kw);
////			if (csvRecord != null) {
////				Object[] obj = new Object[header.length];
////				for (int i = 0; i < header.length; i++) {
////					obj[i] = csvRecord.get(header[i]);
////				}
////				csvPrinter.printRecord(obj);
////			}
////		}
////			if (null == kws) {
////				continue;
////			}
//
//
////			dailyKeywords.put(key, kws);
//			System.out.println(engine+"#"+language);
//		}
//		csvPrinter.flush();
//		csvPrinter.close();


		Date s = new Date();
		System.out.println(DateUtils.addDays(s, -6));
		System.exit(1);
//		StringDistance metric = new Levenshtein();

//		CSVFormat csvFormat = CSVFormat.DEFAULT.withHeader("keywordA","keywordB","Similarity score","Levenstein score").withSkipHeaderRecord();
//		File dirs = new File("/home/<USER>/overValueDir");
//		File[] files = dirs.listFiles();
//		for (File file : files) {
//			if (StringUtils.startsWithIgnoreCase(file.getName(), "overValueExtracts_")) {
//				CSVParser csvParser = new CSVParser(new FileReader(file), csvFormat);
//				CSVPrinter csvPrinter = new CSVPrinter(new BufferedWriter(new FileWriter(file.getAbsolutePath()+"_V2")), csvFormat);
//				List<CSVRecord> csvRecordList = csvParser.getRecords();
//				for (CSVRecord csvRecord : csvRecordList) {
//					String ka = csvRecord.get("keywordA");
//					String kb = csvRecord.get("keywordB");
//					csvPrinter.printRecord(ka, kb, csvRecord.get("Similarity score"), metric.distance(ka, kb));
//				}
//				csvPrinter.flush();
//				csvPrinter.close();
//			}
//		}

//		File file = new File("/home/<USER>/expedia_bot_full/www.expedia.com-4765-09232018-BotClarity.txt");
//		CSVFormat csvFormat = CSVFormat.DEFAULT.withHeader("OID", "Domain", "Country", "Search Engine", "User Agent", "IP Address", "Spoofed", "URL", "Bot Requests", "Avg Requests", "Last Crawl Date", "2xx", "3xx", "4xx", "5xx").withSkipHeaderRecord();
//		CSVParser csvParser = new CSVParser(new FileReader(file), csvFormat);
//		CSVPrinter csvPrinter = new CSVPrinter(new BufferedWriter(new FileWriter(file.getAbsolutePath()+"_V2")), csvFormat);
//		List<CSVRecord> csvRecordList = csvParser.getRecords();
//		for (CSVRecord csvRecord : csvRecordList) {
//			String se = csvRecord.get("Search Engine");
//			String userAgent = csvRecord.get("User Agent");
//			if (se.equalsIgnoreCase("UNKNOWN")) {
//				csvPrinter.printRecord(
//						csvRecord.get("OID"),
//						csvRecord.get("Domain"),
//						csvRecord.get("Country"),
//						getBotType(userAgent),
//						userAgent,
//						csvRecord.get("IP Address"),
//						csvRecord.get("Spoofed"),
//						csvRecord.get("URL"),
//						csvRecord.get("Bot Requests"),
//						csvRecord.get("Avg Requests"),
//						csvRecord.get("Last Crawl Date"),
//						csvRecord.get("2xx"),
//						csvRecord.get("3xx"),
//						csvRecord.get("4xx"),
//						csvRecord.get("5xx")
//				);
//			} else {
//				csvPrinter.printRecord(
//						csvRecord.get("OID"),
//						csvRecord.get("Domain"),
//						csvRecord.get("Country"),
//						se,
//						userAgent,
//						csvRecord.get("IP Address"),
//						csvRecord.get("Spoofed"),
//						csvRecord.get("URL"),
//						csvRecord.get("Bot Requests"),
//						csvRecord.get("Avg Requests"),
//						csvRecord.get("Last Crawl Date"),
//						csvRecord.get("2xx"),
//						csvRecord.get("3xx"),
//						csvRecord.get("4xx"),
//						csvRecord.get("5xx")
//				);
//			}
//		}
//		csvPrinter.flush();
//		csvPrinter.close();

		//TODO Step 1, create sv retrieve file
		Map<String, Integer[]> processFile = new HashMap<>();
		processFile.put("INTL-GSC-2018-09-19_000", new Integer[] {6, 8, 2826});
//		processFile.put("2036_Australia-GSC-2018-09-19", new Integer[] {2,5,2036});
//		processFile.put("2076_Brazil-GSC-2018-09-19", new Integer[] {15, 16, 2076});
//		processFile.put("2124_Canada-GSC-2018-09-19", new Integer[] {3,3, 2124});
//		processFile.put("2276_Germany-GSC-2018-09-19", new Integer[] {14, 15, 2276});
//		processFile.put("2356_India-GSC-2018-09-19", new Integer[] {24,25, 2356});
//		processFile.put("2360_Indonesia-GSC-2018-09-19", new Integer[] {31, 31, 2360});
//		processFile.put("2392_Japan-GSC-2018-09-19", new Integer[] {18,19, 2392});
//		processFile.put("2764_Thailand-GSC-2018-09-19", new Integer[] {35, 35, 2764});
//		processFile.put("2840_US-GSC-2018-09-19", new Integer[] {1,1, 2840});
//		processFile.put("2826_UK-GSC-2018-09-19", new Integer[] {6, 8, 2826});
//		CSVFormat csvFormat = CSVFormat.DEFAULT.withHeader("keyword", "locationId");
//		File dir = new File("/home/<USER>/keywordEx/save/");
//		for (File file : dir.listFiles()) {
//			String fileName = file.getName();
//			System.out.println(fileName);
//			String sign = fileName.split("\\.")[0];
//			if (!processFile.containsKey(sign)) {
//				System.out.println(fileName);
//				continue;
//			}
//			Integer[] engines = processFile.get(sign);
//			int engine = engines[0];
//			int language = engines[1];
//			int locationId = engines[2];
//			CSVPrinter csvPrinter = new CSVPrinter(new FileWriter("/home/<USER>/keywordEx/save/"+locationId+"_"+sign+".csv"), csvFormat);
//			List<String> lines = new ArrayList<>();
//			lines = IoUtil.readLines(new FileReader(file), lines);
//			for (String line : lines) {
//				csvPrinter.printRecord(line, locationId);
//			}
//			csvPrinter.flush();
//			csvPrinter.close();
//		}

//		processFile.put("INTL-GSC-2018-09-19_000", new Integer[] {35, 35, 2764});
//		processFile.put("INTL-GSC-2018-09-19_001", new Integer[] {1,1, 2840});
//		processFile.put("INTL-GSC-2018-09-19_002", new Integer[] {6, 8, 2826});
//		CSVFormat csvFormat = CSVFormat.DEFAULT.withHeader("keyword", "engineId", "languageId");
//		CSVFormat csvFormat1 = CSVFormat.DEFAULT.withDelimiter('	').withSkipHeaderRecord(false);
//		File dir = new File("/home/<USER>/keywordEx/save2/");
//		for (File file : dir.listFiles()) {
//			String fileName = file.getName();
//			System.out.println(fileName);
//			String sign = fileName.split("\\.")[0];
//			if (!processFile.containsKey(sign)) {
//				System.out.println(fileName);
//				continue;
//			}
//			CSVPrinter csvPrinter = new CSVPrinter(new FileWriter("/home/<USER>/keywordEx/save2/"+sign+".csv"), csvFormat);
//			CSVParser csvParser = new CSVParser(new FileReader(file), csvFormat1);
//			List<CSVRecord> csvRecords = csvParser.getRecords();
//			for (CSVRecord csvRecord : csvRecords) {
//				csvPrinter.printRecord(csvRecord.get(0), csvRecord.get(1), csvRecord.get(2));
//			}
//			csvPrinter.flush();
//			csvPrinter.close();
//		}
//
//		//TODO step 2 only keep sv > 0
//		File dir = new File("/home/<USER>/keywordEx/save2/");
//		CSVFormat csvFormat = CSVFormat.DEFAULT.withFirstRecordAsHeader().withSkipHeaderRecord();
//		List<String> header = Arrays.asList("LocationId", "LocationName", "Keyword", "Avg SearchVol", "Aug/2018", "Jul/2018", "Jun/2018", "May/2018", "Apr/2018", "Mar/2018", "Feb/2018", "Jan/2018", "Dec/2017", "Nov/2017", "Oct/2017", "Sep/2017", "CPC", "EngineId", "LanguageId");
//		CSVFormat csvFormatP = CSVFormat.DEFAULT.withHeader(header.toArray(new String[header.size()]));
//		for (File file : dir.listFiles()) {
//			String fileName = file.getName();
//			System.out.println(fileName);
//			String sign = fileName.split("\\.")[0];
//			if (!processFile.containsKey(sign) || !fileName.endsWith(".csv_result")) {
//				System.out.println(fileName+" skip");
//				continue;
//			}
//			Integer[] engines = processFile.get(sign);
//			int engine = engines[0];
//			int language = engines[1];
//			CSVParser csvParser = new CSVParser(new FileReader(file), csvFormat);
//			CSVPrinter csvPrinter = new CSVPrinter(new BufferedWriter(new FileWriter(file.getAbsolutePath()+"_ranksv")), csvFormatP);
//			List<CSVRecord> csvRecords = csvParser.getRecords();
//			int skipCount = 0;
//			for (CSVRecord csvRecord : csvRecords) {
//				String sv = csvRecord.get("Avg SearchVol");
//				if (sv.equalsIgnoreCase("-") || sv.equalsIgnoreCase("0")) {
//					skipCount++;
//					continue;
//				}
//				Object[] obj = new Object[header.size()];
//				for (int i = 0; i < header.size()-2; i++) {
//					obj[i] = csvRecord.get(header.get(i));
//				}
//				obj[header.size()-2] = engine;
//				obj[header.size()-1] = language;
//
//				csvPrinter.printRecord(obj);
//			}
//			csvPrinter.flush();
//			csvPrinter.close();
//			System.out.println("Total :"+csvRecords.size()+" skip :"+skipCount);
//		}



//		File baseDir = new File("E:\\TEMP\\common-crawler\\0722");
//		for (File file : baseDir.listFiles()) {
//			File[] zipFiles = file.listFiles();
//			List<String> jsons = null;
//			for (File zipFile : zipFiles) {
//				if (zipFile.getName().endsWith(".txt")) {
//					jsons = IOUtils.readLines(new FileInputStream(zipFile));
//				}
//			}
//			for (File zipFile : zipFiles) {
//				if (zipFile.getName().endsWith(".zip")) {
//					String zipPath = StringUtils.removeEndIgnoreCase(zipFile.getAbsolutePath(), ".zip");
//					System.out.println(zipPath);
//					FileUtil.mkdir(zipPath);
//					File tmpFile = ZipUtil.unzip(zipFile, new File(zipPath));
//					System.out.println("Unzip File : "+tmpFile.getAbsolutePath());
//
//					File[] htmlFiles = tmpFile.listFiles();
//					String keywordName = StringUtils.removeEndIgnoreCase(zipFile.getName(), ".zip");
//					htmlFiles[0].renameTo(new File(tmpFile.getAbsolutePath()+"/"+keywordName+".html"));
//					File txtFile = FileUtil.touch(new File(tmpFile.getAbsolutePath()+"/rank.txt"));
//					for (String json : jsons) {
//						if (StringUtils.containsIgnoreCase(json, keywordName)){
//							FileUtil.writeString(json, txtFile, "UTF-8");
//						}
//					}
//				}
//			}
//		}
//		GscBaseDao gscBaseDao = SpringBeanFactory.getBean("gscBaseDao");
//		gscBaseDao.testSpeed();
//		String a = "audible.custhelp.com/app/answers/detail/a_id/6738/~/how-can-i-cancel-my-membership?,dictionary.cambridge.org/dictionary/english/cancel,dictionary.cambridge.org/dictionary/english/cancel,en.oxforddictionaries.com/definition/cancel,en.oxforddictionaries.com/definition/cancel,en.wikipedia.org/wiki/Cancel,en.wikipedia.org/wiki/Cancel,en.wiktionary.org/wiki/cancel,en.wiktionary.org/wiki/cancel,support.microsoft.com/en-us/help/4027815/microsoft-account-cancel-a-microsoft-subscription,support.microsoft.com/en-us/help/4027815/microsoft-account-cancel-a-microsoft-subscription,support.spotify.com/article/how-to-cancel-your-subscription/,www.dictionary.com/browse/cancel,www.dictionary.com/browse/cancel,www.grammarly.com/blog/canceled-vs-cancelled/,www.grammarly.com/blog/canceled-vs-cancelled/,www.merriam-webster.com/dictionary/cancel,www.merriam-webster.com/dictionary/cancel,www.thesaurus.com/browse/cancel,www.thesaurus.com/browse/cancel";
//		String b = "classifieds.kansas.com/ks/homes-for-rent/search,hotpads.com/wichita-ks/houses-for-rent,lease.invitationhomes.com/markets/seattle-wa,wichita.craigslist.org/search/apa,www.americanhomes4rent.com/,www.apartments.com/houses/,www.apartments.com/houses/,www.har.com/rentals,www.homes.com/homes-for-rent/,www.homes.com/wichita-ks/homes-for-rent/,www.keymgmt.com/properties/house-rentals,www.rent.com/homes-for-rent,www.rent.com/kansas/wichita-houses,www.trulia.com/for_rent/Wichita,KS/SINGLE-FAMILY_HOME_type/,www.trulia.com/rent/,www.wichitaleasing.com/properties/house-rentals,www.zillow.com/chicago-il/rent-houses/,www.zillow.com/fl/rent-houses/,www.zillow.com/san-diego-ca/rent-houses/,www.zillow.com/wichita-ks/rent-houses/";
//		StringDistance metric = new Levenshtein();
//		info.debatty.java.stringsimilarity.Levenshtein levenshtein = new info.debatty.java.stringsimilarity.Levenshtein();
//		System.out.println(metric.distance(a, b));
//		System.out.println(levenshtein.distance(a, b));
//
//		a = "Chilpéric II son of Childeric II";
//		b = "chilperic ii son of childeric ii";
//		System.out.println(metric.distance(a, b));
//		System.out.println(levenshtein.distance(a, b));

	}

	private static String getBotType(String userAgent) {
		if (BotUtils.isGoogleBot(userAgent)) {
			return "Google";
		} else if (BotUtils.isBingBot(userAgent)) {
			return "Bing";
		} else if (BotUtils.isYahooBot(userAgent)) {
			return "Yahoo";
		} else if (BotUtils.isYandexBot(userAgent)) {
			return "Yandex";
		} else if (BotUtils.isBaiduBot(userAgent)) {
			return "Baidu";
		}
		return "UNKNOWN";
	}
}
