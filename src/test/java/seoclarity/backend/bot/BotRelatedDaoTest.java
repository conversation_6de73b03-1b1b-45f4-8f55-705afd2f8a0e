package seoclarity.backend.bot;

import seoclarity.backend.dao.actonia.UploadFileDetailDao;
import seoclarity.backend.entity.UploadFileDetailEntity;
import seoclarity.backend.utils.FormatUtils;
import seoclarity.backend.utils.SpringBeanFactory;

import java.util.Date;

public class BotRelatedDaoTest {

    private UploadFileDetailDao uploadFileDetailDao;

    public BotRelatedDaoTest() {
        uploadFileDetailDao = SpringBeanFactory.getBean("uploadFileDetailDao");
    }

    public static void main(String[] args) {
        BotRelatedDaoTest test = new BotRelatedDaoTest();
        test.testInsert();
        test.testUpdate();
    }

    public void testInsert(){
        UploadFileDetailEntity fileDetail = new UploadFileDetailEntity();
        fileDetail.setOwnDomainId(1234);
        fileDetail.setUploadType(UploadFileDetailEntity.UPLOAD_TYPE_BOT);
        fileDetail.setFileName("fileName");
        fileDetail.setDataCount(0);
        fileDetail.setLoadedCount(0);
        UploadFileDetailEntity uploadFileDetail = uploadFileDetailDao.getUploadFileDetailByUniqueKey(fileDetail);
        if (null == uploadFileDetail) {
            fileDetail.setFileNameDay(FormatUtils.formatDateToYyyyMmDd(new Date()));
            fileDetail.setZipFileSize(10000L);
            fileDetail.setUnzippedFileSize(0L);
            uploadFileDetailDao.insert(fileDetail);
        }else {
            System.out.println("uploadFileDetail has exists");
        }
    }

    public void testUpdate(){
        UploadFileDetailEntity fileDetail = new UploadFileDetailEntity();
        fileDetail.setUploadType(UploadFileDetailEntity.UPLOAD_TYPE_BOT);
        fileDetail.setOwnDomainId(1234);
        fileDetail.setFileName("fileName");
        fileDetail.setUnzippedFileSize(20000L);
        fileDetail.setDataCount(2000);
        fileDetail.setLoadedCount(1950);
        UploadFileDetailEntity uploadFileDetailQuery = uploadFileDetailDao.getUploadFileDetailByUniqueKey(fileDetail);
        if (null != uploadFileDetailQuery){
            uploadFileDetailDao.updateUnzippedFileSize(uploadFileDetailQuery.getId(), fileDetail);
            uploadFileDetailDao.updateDataCount(uploadFileDetailQuery.getId(), fileDetail);
            uploadFileDetailDao.updateLoadedCount(uploadFileDetailQuery.getId(), fileDetail);
        }
    }
}
