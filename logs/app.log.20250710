13:48:08.170 INFO [main] producer.ProducerConfig (ProducerConfig.java:526) - Idempotence will be disabled because acks is set to 1, not set to 'all'.
13:48:08.174 INFO [main] producer.ProducerConfig (AbstractConfig.java:376) - ProducerConfig values: 
	acks = 1
	batch.size = 1048576
	bootstrap.servers = [**************:9093, ***********:9093, ************:9093]
	buffer.memory = 268435456
	client.dns.lookup = use_all_dns_ips
	client.id = siteHealth_producer_client
	compression.type = none
	connections.max.idle.ms = 540000
	delivery.timeout.ms = 120000
	enable.idempotence = false
	interceptor.classes = []
	key.serializer = class org.apache.kafka.common.serialization.StringSerializer
	linger.ms = 100
	max.block.ms = 600000
	max.in.flight.requests.per.connection = 5
	max.request.size = 5242880
	metadata.max.age.ms = 300000
	metadata.max.idle.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partitioner.adaptive.partitioning.enable = true
	partitioner.availability.timeout.ms = 0
	partitioner.class = null
	partitioner.ignore.keys = false
	receive.buffer.bytes = 32768
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 100000
	retries = 2
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = [hidden]
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.connect.timeout.ms = null
	sasl.login.read.timeout.ms = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.login.retry.backoff.max.ms = 10000
	sasl.login.retry.backoff.ms = 100
	sasl.mechanism = PLAIN
	sasl.oauthbearer.clock.skew.seconds = 30
	sasl.oauthbearer.expected.audience = null
	sasl.oauthbearer.expected.issuer = null
	sasl.oauthbearer.jwks.endpoint.refresh.ms = 3600000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.max.ms = 10000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.ms = 100
	sasl.oauthbearer.jwks.endpoint.url = null
	sasl.oauthbearer.scope.claim.name = scope
	sasl.oauthbearer.sub.claim.name = sub
	sasl.oauthbearer.token.endpoint.url = null
	security.protocol = SASL_PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.3]
	ssl.endpoint.identification.algorithm = 
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.3
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	transaction.timeout.ms = 60000
	transactional.id = null
	value.serializer = class org.apache.kafka.common.serialization.ByteArraySerializer

13:48:08.300 INFO [main] authenticator.AbstractLogin (AbstractLogin.java:61) - Successfully logged in.
13:48:08.319 WARN [main] producer.ProducerConfig (AbstractConfig.java:385) - These configurations '[ssl.endpoint.identification.algorithm]' were supplied but are not used yet.
13:48:08.320 INFO [main] utils.AppInfoParser (AppInfoParser.java:119) - Kafka version: 3.3.1
13:48:08.320 INFO [main] utils.AppInfoParser (AppInfoParser.java:120) - Kafka commitId: e23c59d00e687ff5
13:48:08.321 INFO [main] utils.AppInfoParser (AppInfoParser.java:121) - Kafka startTimeMs: 1752126488319
13:48:08.322 INFO [main] producer.ProducerConfig (ProducerConfig.java:526) - Idempotence will be disabled because acks is set to 1, not set to 'all'.
13:48:08.322 INFO [main] producer.ProducerConfig (AbstractConfig.java:376) - ProducerConfig values: 
	acks = 1
	batch.size = 1048576
	bootstrap.servers = [**************:9093, ***********:9093, ************:9093]
	buffer.memory = 268435456
	client.dns.lookup = use_all_dns_ips
	client.id = siteHealth_producer_client
	compression.type = none
	connections.max.idle.ms = 540000
	delivery.timeout.ms = 120000
	enable.idempotence = false
	interceptor.classes = []
	key.serializer = class org.apache.kafka.common.serialization.StringSerializer
	linger.ms = 100
	max.block.ms = 600000
	max.in.flight.requests.per.connection = 5
	max.request.size = 5242880
	metadata.max.age.ms = 300000
	metadata.max.idle.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partitioner.adaptive.partitioning.enable = true
	partitioner.availability.timeout.ms = 0
	partitioner.class = null
	partitioner.ignore.keys = false
	receive.buffer.bytes = 32768
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 100000
	retries = 2
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = [hidden]
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.connect.timeout.ms = null
	sasl.login.read.timeout.ms = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.login.retry.backoff.max.ms = 10000
	sasl.login.retry.backoff.ms = 100
	sasl.mechanism = PLAIN
	sasl.oauthbearer.clock.skew.seconds = 30
	sasl.oauthbearer.expected.audience = null
	sasl.oauthbearer.expected.issuer = null
	sasl.oauthbearer.jwks.endpoint.refresh.ms = 3600000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.max.ms = 10000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.ms = 100
	sasl.oauthbearer.jwks.endpoint.url = null
	sasl.oauthbearer.scope.claim.name = scope
	sasl.oauthbearer.sub.claim.name = sub
	sasl.oauthbearer.token.endpoint.url = null
	security.protocol = SASL_PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.3]
	ssl.endpoint.identification.algorithm = 
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.3
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	transaction.timeout.ms = 60000
	transactional.id = null
	value.serializer = class org.apache.kafka.common.serialization.StringSerializer

13:48:08.326 WARN [main] producer.ProducerConfig (AbstractConfig.java:385) - These configurations '[ssl.endpoint.identification.algorithm]' were supplied but are not used yet.
13:48:08.326 INFO [main] utils.AppInfoParser (AppInfoParser.java:119) - Kafka version: 3.3.1
13:48:08.326 INFO [main] utils.AppInfoParser (AppInfoParser.java:120) - Kafka commitId: e23c59d00e687ff5
13:48:08.326 INFO [main] utils.AppInfoParser (AppInfoParser.java:121) - Kafka startTimeMs: 1752126488326
13:48:08.327 WARN [main] utils.AppInfoParser (AppInfoParser.java:68) - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.producer:type=app-info,id=siteHealth_producer_client
	at java.management/com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436)
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865)
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960)
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895)
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320)
	at java.management/com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523)
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64)
	at org.apache.kafka.clients.producer.KafkaProducer.<init>(KafkaProducer.java:462)
	at org.apache.kafka.clients.producer.KafkaProducer.<init>(KafkaProducer.java:291)
	at org.apache.kafka.clients.producer.KafkaProducer.<init>(KafkaProducer.java:318)
	at org.apache.kafka.clients.producer.KafkaProducer.<init>(KafkaProducer.java:303)
	at seoclarity.backend.kafka.clientconstructor.producer.siteHealth.SiteHealthProducerConstructor.createStringValueProducer(SiteHealthProducerConstructor.java:46)
	at seoclarity.backend.kafka.clientconstructor.producer.siteHealth.SiteHealthKafkaProducer.<init>(SiteHealthKafkaProducer.java:29)
	at seoclarity.backend.kafka.clientconstructor.producer.siteHealth.SiteHealthKafkaProducer.getInstance(SiteHealthKafkaProducer.java:19)
	at seoclarity.backend.upload.GoogleAIModeScraperUploader.<clinit>(GoogleAIModeScraperUploader.java:62)
	at seoclarity.backend.upload.GoogleAIModeScraperUploader$GoogleAIModeParser$1.lastCharIsWhitespace(GoogleAIModeScraperUploader.java:622)
	at seoclarity.backend.upload.GoogleAIModeScraperUploader$GoogleAIModeParser$1.appendNormalisedText(GoogleAIModeScraperUploader.java:610)
	at seoclarity.backend.upload.GoogleAIModeScraperUploader$GoogleAIModeParser$1.head(GoogleAIModeScraperUploader.java:572)
	at org.jsoup.select.NodeTraversor.traverse(NodeTraversor.java:34)
	at seoclarity.backend.upload.GoogleAIModeScraperUploader$GoogleAIModeParser.getInnerText(GoogleAIModeScraperUploader.java:559)
	at seoclarity.backend.upload.GoogleAIModeScraperUploader$GoogleAIModeParser.extractResponse(GoogleAIModeScraperUploader.java:426)
	at seoclarity.backend.upload.GoogleAIModeScraperUploader$GoogleAIModeParser.main(GoogleAIModeScraperUploader.java:703)
13:48:24.701 INFO [main] producer.ProducerConfig (ProducerConfig.java:526) - Idempotence will be disabled because acks is set to 1, not set to 'all'.
13:48:24.704 INFO [main] producer.ProducerConfig (AbstractConfig.java:376) - ProducerConfig values: 
	acks = 1
	batch.size = 1048576
	bootstrap.servers = [**************:9093, ***********:9093, ************:9093]
	buffer.memory = 268435456
	client.dns.lookup = use_all_dns_ips
	client.id = siteHealth_producer_client
	compression.type = none
	connections.max.idle.ms = 540000
	delivery.timeout.ms = 120000
	enable.idempotence = false
	interceptor.classes = []
	key.serializer = class org.apache.kafka.common.serialization.StringSerializer
	linger.ms = 100
	max.block.ms = 600000
	max.in.flight.requests.per.connection = 5
	max.request.size = 5242880
	metadata.max.age.ms = 300000
	metadata.max.idle.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partitioner.adaptive.partitioning.enable = true
	partitioner.availability.timeout.ms = 0
	partitioner.class = null
	partitioner.ignore.keys = false
	receive.buffer.bytes = 32768
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 100000
	retries = 2
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = [hidden]
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.connect.timeout.ms = null
	sasl.login.read.timeout.ms = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.login.retry.backoff.max.ms = 10000
	sasl.login.retry.backoff.ms = 100
	sasl.mechanism = PLAIN
	sasl.oauthbearer.clock.skew.seconds = 30
	sasl.oauthbearer.expected.audience = null
	sasl.oauthbearer.expected.issuer = null
	sasl.oauthbearer.jwks.endpoint.refresh.ms = 3600000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.max.ms = 10000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.ms = 100
	sasl.oauthbearer.jwks.endpoint.url = null
	sasl.oauthbearer.scope.claim.name = scope
	sasl.oauthbearer.sub.claim.name = sub
	sasl.oauthbearer.token.endpoint.url = null
	security.protocol = SASL_PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.3]
	ssl.endpoint.identification.algorithm = 
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.3
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	transaction.timeout.ms = 60000
	transactional.id = null
	value.serializer = class org.apache.kafka.common.serialization.ByteArraySerializer

13:48:24.849 INFO [main] authenticator.AbstractLogin (AbstractLogin.java:61) - Successfully logged in.
13:48:24.875 WARN [main] producer.ProducerConfig (AbstractConfig.java:385) - These configurations '[ssl.endpoint.identification.algorithm]' were supplied but are not used yet.
13:48:24.877 INFO [main] utils.AppInfoParser (AppInfoParser.java:119) - Kafka version: 3.3.1
13:48:24.877 INFO [main] utils.AppInfoParser (AppInfoParser.java:120) - Kafka commitId: e23c59d00e687ff5
13:48:24.877 INFO [main] utils.AppInfoParser (AppInfoParser.java:121) - Kafka startTimeMs: 1752126504876
13:48:24.880 INFO [main] producer.ProducerConfig (ProducerConfig.java:526) - Idempotence will be disabled because acks is set to 1, not set to 'all'.
13:48:24.880 INFO [main] producer.ProducerConfig (AbstractConfig.java:376) - ProducerConfig values: 
	acks = 1
	batch.size = 1048576
	bootstrap.servers = [**************:9093, ***********:9093, ************:9093]
	buffer.memory = 268435456
	client.dns.lookup = use_all_dns_ips
	client.id = siteHealth_producer_client
	compression.type = none
	connections.max.idle.ms = 540000
	delivery.timeout.ms = 120000
	enable.idempotence = false
	interceptor.classes = []
	key.serializer = class org.apache.kafka.common.serialization.StringSerializer
	linger.ms = 100
	max.block.ms = 600000
	max.in.flight.requests.per.connection = 5
	max.request.size = 5242880
	metadata.max.age.ms = 300000
	metadata.max.idle.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partitioner.adaptive.partitioning.enable = true
	partitioner.availability.timeout.ms = 0
	partitioner.class = null
	partitioner.ignore.keys = false
	receive.buffer.bytes = 32768
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 100000
	retries = 2
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = [hidden]
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.connect.timeout.ms = null
	sasl.login.read.timeout.ms = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.login.retry.backoff.max.ms = 10000
	sasl.login.retry.backoff.ms = 100
	sasl.mechanism = PLAIN
	sasl.oauthbearer.clock.skew.seconds = 30
	sasl.oauthbearer.expected.audience = null
	sasl.oauthbearer.expected.issuer = null
	sasl.oauthbearer.jwks.endpoint.refresh.ms = 3600000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.max.ms = 10000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.ms = 100
	sasl.oauthbearer.jwks.endpoint.url = null
	sasl.oauthbearer.scope.claim.name = scope
	sasl.oauthbearer.sub.claim.name = sub
	sasl.oauthbearer.token.endpoint.url = null
	security.protocol = SASL_PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.3]
	ssl.endpoint.identification.algorithm = 
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.3
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	transaction.timeout.ms = 60000
	transactional.id = null
	value.serializer = class org.apache.kafka.common.serialization.StringSerializer

13:48:24.887 WARN [main] producer.ProducerConfig (AbstractConfig.java:385) - These configurations '[ssl.endpoint.identification.algorithm]' were supplied but are not used yet.
13:48:24.888 INFO [main] utils.AppInfoParser (AppInfoParser.java:119) - Kafka version: 3.3.1
13:48:24.888 INFO [main] utils.AppInfoParser (AppInfoParser.java:120) - Kafka commitId: e23c59d00e687ff5
13:48:24.888 INFO [main] utils.AppInfoParser (AppInfoParser.java:121) - Kafka startTimeMs: 1752126504888
13:48:24.888 WARN [main] utils.AppInfoParser (AppInfoParser.java:68) - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.producer:type=app-info,id=siteHealth_producer_client
	at java.management/com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436)
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865)
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960)
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895)
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320)
	at java.management/com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523)
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64)
	at org.apache.kafka.clients.producer.KafkaProducer.<init>(KafkaProducer.java:462)
	at org.apache.kafka.clients.producer.KafkaProducer.<init>(KafkaProducer.java:291)
	at org.apache.kafka.clients.producer.KafkaProducer.<init>(KafkaProducer.java:318)
	at org.apache.kafka.clients.producer.KafkaProducer.<init>(KafkaProducer.java:303)
	at seoclarity.backend.kafka.clientconstructor.producer.siteHealth.SiteHealthProducerConstructor.createStringValueProducer(SiteHealthProducerConstructor.java:46)
	at seoclarity.backend.kafka.clientconstructor.producer.siteHealth.SiteHealthKafkaProducer.<init>(SiteHealthKafkaProducer.java:29)
	at seoclarity.backend.kafka.clientconstructor.producer.siteHealth.SiteHealthKafkaProducer.getInstance(SiteHealthKafkaProducer.java:19)
	at seoclarity.backend.upload.GoogleAIModeScraperUploader.<clinit>(GoogleAIModeScraperUploader.java:62)
	at seoclarity.backend.upload.GoogleAIModeScraperUploader$GoogleAIModeParser$1.lastCharIsWhitespace(GoogleAIModeScraperUploader.java:622)
	at seoclarity.backend.upload.GoogleAIModeScraperUploader$GoogleAIModeParser$1.appendNormalisedText(GoogleAIModeScraperUploader.java:610)
	at seoclarity.backend.upload.GoogleAIModeScraperUploader$GoogleAIModeParser$1.head(GoogleAIModeScraperUploader.java:572)
	at org.jsoup.select.NodeTraversor.traverse(NodeTraversor.java:34)
	at seoclarity.backend.upload.GoogleAIModeScraperUploader$GoogleAIModeParser.getInnerText(GoogleAIModeScraperUploader.java:559)
	at seoclarity.backend.upload.GoogleAIModeScraperUploader$GoogleAIModeParser.extractResponse(GoogleAIModeScraperUploader.java:426)
	at seoclarity.backend.upload.GoogleAIModeScraperUploader$GoogleAIModeParser.main(GoogleAIModeScraperUploader.java:703)
13:48:34.460 INFO [main] producer.ProducerConfig (ProducerConfig.java:526) - Idempotence will be disabled because acks is set to 1, not set to 'all'.
13:48:34.463 INFO [main] producer.ProducerConfig (AbstractConfig.java:376) - ProducerConfig values: 
	acks = 1
	batch.size = 1048576
	bootstrap.servers = [**************:9093, ***********:9093, ************:9093]
	buffer.memory = 268435456
	client.dns.lookup = use_all_dns_ips
	client.id = siteHealth_producer_client
	compression.type = none
	connections.max.idle.ms = 540000
	delivery.timeout.ms = 120000
	enable.idempotence = false
	interceptor.classes = []
	key.serializer = class org.apache.kafka.common.serialization.StringSerializer
	linger.ms = 100
	max.block.ms = 600000
	max.in.flight.requests.per.connection = 5
	max.request.size = 5242880
	metadata.max.age.ms = 300000
	metadata.max.idle.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partitioner.adaptive.partitioning.enable = true
	partitioner.availability.timeout.ms = 0
	partitioner.class = null
	partitioner.ignore.keys = false
	receive.buffer.bytes = 32768
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 100000
	retries = 2
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = [hidden]
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.connect.timeout.ms = null
	sasl.login.read.timeout.ms = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.login.retry.backoff.max.ms = 10000
	sasl.login.retry.backoff.ms = 100
	sasl.mechanism = PLAIN
	sasl.oauthbearer.clock.skew.seconds = 30
	sasl.oauthbearer.expected.audience = null
	sasl.oauthbearer.expected.issuer = null
	sasl.oauthbearer.jwks.endpoint.refresh.ms = 3600000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.max.ms = 10000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.ms = 100
	sasl.oauthbearer.jwks.endpoint.url = null
	sasl.oauthbearer.scope.claim.name = scope
	sasl.oauthbearer.sub.claim.name = sub
	sasl.oauthbearer.token.endpoint.url = null
	security.protocol = SASL_PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.3]
	ssl.endpoint.identification.algorithm = 
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.3
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	transaction.timeout.ms = 60000
	transactional.id = null
	value.serializer = class org.apache.kafka.common.serialization.ByteArraySerializer

13:48:34.570 INFO [main] authenticator.AbstractLogin (AbstractLogin.java:61) - Successfully logged in.
13:48:34.587 WARN [main] producer.ProducerConfig (AbstractConfig.java:385) - These configurations '[ssl.endpoint.identification.algorithm]' were supplied but are not used yet.
13:48:34.588 INFO [main] utils.AppInfoParser (AppInfoParser.java:119) - Kafka version: 3.3.1
13:48:34.588 INFO [main] utils.AppInfoParser (AppInfoParser.java:120) - Kafka commitId: e23c59d00e687ff5
13:48:34.588 INFO [main] utils.AppInfoParser (AppInfoParser.java:121) - Kafka startTimeMs: 1752126514587
13:48:34.590 INFO [main] producer.ProducerConfig (ProducerConfig.java:526) - Idempotence will be disabled because acks is set to 1, not set to 'all'.
13:48:34.590 INFO [main] producer.ProducerConfig (AbstractConfig.java:376) - ProducerConfig values: 
	acks = 1
	batch.size = 1048576
	bootstrap.servers = [**************:9093, ***********:9093, ************:9093]
	buffer.memory = 268435456
	client.dns.lookup = use_all_dns_ips
	client.id = siteHealth_producer_client
	compression.type = none
	connections.max.idle.ms = 540000
	delivery.timeout.ms = 120000
	enable.idempotence = false
	interceptor.classes = []
	key.serializer = class org.apache.kafka.common.serialization.StringSerializer
	linger.ms = 100
	max.block.ms = 600000
	max.in.flight.requests.per.connection = 5
	max.request.size = 5242880
	metadata.max.age.ms = 300000
	metadata.max.idle.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partitioner.adaptive.partitioning.enable = true
	partitioner.availability.timeout.ms = 0
	partitioner.class = null
	partitioner.ignore.keys = false
	receive.buffer.bytes = 32768
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 100000
	retries = 2
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = [hidden]
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.connect.timeout.ms = null
	sasl.login.read.timeout.ms = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.login.retry.backoff.max.ms = 10000
	sasl.login.retry.backoff.ms = 100
	sasl.mechanism = PLAIN
	sasl.oauthbearer.clock.skew.seconds = 30
	sasl.oauthbearer.expected.audience = null
	sasl.oauthbearer.expected.issuer = null
	sasl.oauthbearer.jwks.endpoint.refresh.ms = 3600000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.max.ms = 10000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.ms = 100
	sasl.oauthbearer.jwks.endpoint.url = null
	sasl.oauthbearer.scope.claim.name = scope
	sasl.oauthbearer.sub.claim.name = sub
	sasl.oauthbearer.token.endpoint.url = null
	security.protocol = SASL_PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.3]
	ssl.endpoint.identification.algorithm = 
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.3
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	transaction.timeout.ms = 60000
	transactional.id = null
	value.serializer = class org.apache.kafka.common.serialization.StringSerializer

13:48:34.597 WARN [main] producer.ProducerConfig (AbstractConfig.java:385) - These configurations '[ssl.endpoint.identification.algorithm]' were supplied but are not used yet.
13:48:34.598 INFO [main] utils.AppInfoParser (AppInfoParser.java:119) - Kafka version: 3.3.1
13:48:34.598 INFO [main] utils.AppInfoParser (AppInfoParser.java:120) - Kafka commitId: e23c59d00e687ff5
13:48:34.598 INFO [main] utils.AppInfoParser (AppInfoParser.java:121) - Kafka startTimeMs: 1752126514598
13:48:34.598 WARN [main] utils.AppInfoParser (AppInfoParser.java:68) - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.producer:type=app-info,id=siteHealth_producer_client
	at java.management/com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436)
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865)
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960)
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895)
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320)
	at java.management/com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523)
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64)
	at org.apache.kafka.clients.producer.KafkaProducer.<init>(KafkaProducer.java:462)
	at org.apache.kafka.clients.producer.KafkaProducer.<init>(KafkaProducer.java:291)
	at org.apache.kafka.clients.producer.KafkaProducer.<init>(KafkaProducer.java:318)
	at org.apache.kafka.clients.producer.KafkaProducer.<init>(KafkaProducer.java:303)
	at seoclarity.backend.kafka.clientconstructor.producer.siteHealth.SiteHealthProducerConstructor.createStringValueProducer(SiteHealthProducerConstructor.java:46)
	at seoclarity.backend.kafka.clientconstructor.producer.siteHealth.SiteHealthKafkaProducer.<init>(SiteHealthKafkaProducer.java:29)
	at seoclarity.backend.kafka.clientconstructor.producer.siteHealth.SiteHealthKafkaProducer.getInstance(SiteHealthKafkaProducer.java:19)
	at seoclarity.backend.upload.GoogleAIModeScraperUploader.<clinit>(GoogleAIModeScraperUploader.java:62)
	at seoclarity.backend.upload.GoogleAIModeScraperUploader$GoogleAIModeParser$1.lastCharIsWhitespace(GoogleAIModeScraperUploader.java:622)
	at seoclarity.backend.upload.GoogleAIModeScraperUploader$GoogleAIModeParser$1.appendNormalisedText(GoogleAIModeScraperUploader.java:610)
	at seoclarity.backend.upload.GoogleAIModeScraperUploader$GoogleAIModeParser$1.head(GoogleAIModeScraperUploader.java:572)
	at org.jsoup.select.NodeTraversor.traverse(NodeTraversor.java:34)
	at seoclarity.backend.upload.GoogleAIModeScraperUploader$GoogleAIModeParser.getInnerText(GoogleAIModeScraperUploader.java:559)
	at seoclarity.backend.upload.GoogleAIModeScraperUploader$GoogleAIModeParser.extractResponse(GoogleAIModeScraperUploader.java:426)
	at seoclarity.backend.upload.GoogleAIModeScraperUploader$GoogleAIModeParser.main(GoogleAIModeScraperUploader.java:703)
