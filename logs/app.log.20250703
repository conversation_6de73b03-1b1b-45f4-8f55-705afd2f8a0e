09:58:31.825 INFO [main] producer.ProducerConfig (ProducerConfig.java:526) - Idempotence will be disabled because acks is set to 1, not set to 'all'.
09:58:31.836 INFO [main] producer.ProducerConfig (AbstractConfig.java:376) - ProducerConfig values: 
	acks = 1
	batch.size = 1048576
	bootstrap.servers = [**************:9093, ***********:9093, ************:9093]
	buffer.memory = 268435456
	client.dns.lookup = use_all_dns_ips
	client.id = siteHealth_producer_client
	compression.type = none
	connections.max.idle.ms = 540000
	delivery.timeout.ms = 120000
	enable.idempotence = false
	interceptor.classes = []
	key.serializer = class org.apache.kafka.common.serialization.StringSerializer
	linger.ms = 100
	max.block.ms = 600000
	max.in.flight.requests.per.connection = 5
	max.request.size = 5242880
	metadata.max.age.ms = 300000
	metadata.max.idle.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partitioner.adaptive.partitioning.enable = true
	partitioner.availability.timeout.ms = 0
	partitioner.class = null
	partitioner.ignore.keys = false
	receive.buffer.bytes = 32768
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 100000
	retries = 2
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = [hidden]
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.connect.timeout.ms = null
	sasl.login.read.timeout.ms = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.login.retry.backoff.max.ms = 10000
	sasl.login.retry.backoff.ms = 100
	sasl.mechanism = PLAIN
	sasl.oauthbearer.clock.skew.seconds = 30
	sasl.oauthbearer.expected.audience = null
	sasl.oauthbearer.expected.issuer = null
	sasl.oauthbearer.jwks.endpoint.refresh.ms = 3600000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.max.ms = 10000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.ms = 100
	sasl.oauthbearer.jwks.endpoint.url = null
	sasl.oauthbearer.scope.claim.name = scope
	sasl.oauthbearer.sub.claim.name = sub
	sasl.oauthbearer.token.endpoint.url = null
	security.protocol = SASL_PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.3]
	ssl.endpoint.identification.algorithm = 
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.3
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	transaction.timeout.ms = 60000
	transactional.id = null
	value.serializer = class org.apache.kafka.common.serialization.ByteArraySerializer

09:58:32.081 INFO [main] authenticator.AbstractLogin (AbstractLogin.java:61) - Successfully logged in.
09:58:32.112 WARN [main] producer.ProducerConfig (AbstractConfig.java:385) - These configurations '[ssl.endpoint.identification.algorithm]' were supplied but are not used yet.
09:58:32.114 INFO [main] utils.AppInfoParser (AppInfoParser.java:119) - Kafka version: 3.3.1
09:58:32.115 INFO [main] utils.AppInfoParser (AppInfoParser.java:120) - Kafka commitId: e23c59d00e687ff5
09:58:32.115 INFO [main] utils.AppInfoParser (AppInfoParser.java:121) - Kafka startTimeMs: 1751507912112
09:58:32.116 INFO [main] producer.ProducerConfig (ProducerConfig.java:526) - Idempotence will be disabled because acks is set to 1, not set to 'all'.
09:58:32.116 INFO [main] producer.ProducerConfig (AbstractConfig.java:376) - ProducerConfig values: 
	acks = 1
	batch.size = 1048576
	bootstrap.servers = [**************:9093, ***********:9093, ************:9093]
	buffer.memory = 268435456
	client.dns.lookup = use_all_dns_ips
	client.id = siteHealth_producer_client
	compression.type = none
	connections.max.idle.ms = 540000
	delivery.timeout.ms = 120000
	enable.idempotence = false
	interceptor.classes = []
	key.serializer = class org.apache.kafka.common.serialization.StringSerializer
	linger.ms = 100
	max.block.ms = 600000
	max.in.flight.requests.per.connection = 5
	max.request.size = 5242880
	metadata.max.age.ms = 300000
	metadata.max.idle.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partitioner.adaptive.partitioning.enable = true
	partitioner.availability.timeout.ms = 0
	partitioner.class = null
	partitioner.ignore.keys = false
	receive.buffer.bytes = 32768
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 100000
	retries = 2
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = [hidden]
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.connect.timeout.ms = null
	sasl.login.read.timeout.ms = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.login.retry.backoff.max.ms = 10000
	sasl.login.retry.backoff.ms = 100
	sasl.mechanism = PLAIN
	sasl.oauthbearer.clock.skew.seconds = 30
	sasl.oauthbearer.expected.audience = null
	sasl.oauthbearer.expected.issuer = null
	sasl.oauthbearer.jwks.endpoint.refresh.ms = 3600000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.max.ms = 10000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.ms = 100
	sasl.oauthbearer.jwks.endpoint.url = null
	sasl.oauthbearer.scope.claim.name = scope
	sasl.oauthbearer.sub.claim.name = sub
	sasl.oauthbearer.token.endpoint.url = null
	security.protocol = SASL_PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.3]
	ssl.endpoint.identification.algorithm = 
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.3
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	transaction.timeout.ms = 60000
	transactional.id = null
	value.serializer = class org.apache.kafka.common.serialization.StringSerializer

09:58:32.121 WARN [main] producer.ProducerConfig (AbstractConfig.java:385) - These configurations '[ssl.endpoint.identification.algorithm]' were supplied but are not used yet.
09:58:32.121 INFO [main] utils.AppInfoParser (AppInfoParser.java:119) - Kafka version: 3.3.1
09:58:32.121 INFO [main] utils.AppInfoParser (AppInfoParser.java:120) - Kafka commitId: e23c59d00e687ff5
09:58:32.121 INFO [main] utils.AppInfoParser (AppInfoParser.java:121) - Kafka startTimeMs: 1751507912121
09:58:32.122 WARN [main] utils.AppInfoParser (AppInfoParser.java:68) - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.producer:type=app-info,id=siteHealth_producer_client
	at java.management/com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436)
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865)
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960)
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895)
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320)
	at java.management/com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523)
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64)
	at org.apache.kafka.clients.producer.KafkaProducer.<init>(KafkaProducer.java:462)
	at org.apache.kafka.clients.producer.KafkaProducer.<init>(KafkaProducer.java:291)
	at org.apache.kafka.clients.producer.KafkaProducer.<init>(KafkaProducer.java:318)
	at org.apache.kafka.clients.producer.KafkaProducer.<init>(KafkaProducer.java:303)
	at seoclarity.backend.kafka.clientconstructor.producer.siteHealth.SiteHealthProducerConstructor.createStringValueProducer(SiteHealthProducerConstructor.java:46)
	at seoclarity.backend.kafka.clientconstructor.producer.siteHealth.SiteHealthKafkaProducer.<init>(SiteHealthKafkaProducer.java:29)
	at seoclarity.backend.kafka.clientconstructor.producer.siteHealth.SiteHealthKafkaProducer.getInstance(SiteHealthKafkaProducer.java:19)
	at seoclarity.backend.upload.GoogleAIModeScraperUploader.<clinit>(GoogleAIModeScraperUploader.java:47)
10:00:02.993 INFO [main] producer.ProducerConfig (ProducerConfig.java:526) - Idempotence will be disabled because acks is set to 1, not set to 'all'.
10:00:02.996 INFO [main] producer.ProducerConfig (AbstractConfig.java:376) - ProducerConfig values: 
	acks = 1
	batch.size = 1048576
	bootstrap.servers = [**************:9093, ***********:9093, ************:9093]
	buffer.memory = 268435456
	client.dns.lookup = use_all_dns_ips
	client.id = siteHealth_producer_client
	compression.type = none
	connections.max.idle.ms = 540000
	delivery.timeout.ms = 120000
	enable.idempotence = false
	interceptor.classes = []
	key.serializer = class org.apache.kafka.common.serialization.StringSerializer
	linger.ms = 100
	max.block.ms = 600000
	max.in.flight.requests.per.connection = 5
	max.request.size = 5242880
	metadata.max.age.ms = 300000
	metadata.max.idle.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partitioner.adaptive.partitioning.enable = true
	partitioner.availability.timeout.ms = 0
	partitioner.class = null
	partitioner.ignore.keys = false
	receive.buffer.bytes = 32768
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 100000
	retries = 2
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = [hidden]
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.connect.timeout.ms = null
	sasl.login.read.timeout.ms = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.login.retry.backoff.max.ms = 10000
	sasl.login.retry.backoff.ms = 100
	sasl.mechanism = PLAIN
	sasl.oauthbearer.clock.skew.seconds = 30
	sasl.oauthbearer.expected.audience = null
	sasl.oauthbearer.expected.issuer = null
	sasl.oauthbearer.jwks.endpoint.refresh.ms = 3600000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.max.ms = 10000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.ms = 100
	sasl.oauthbearer.jwks.endpoint.url = null
	sasl.oauthbearer.scope.claim.name = scope
	sasl.oauthbearer.sub.claim.name = sub
	sasl.oauthbearer.token.endpoint.url = null
	security.protocol = SASL_PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.3]
	ssl.endpoint.identification.algorithm = 
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.3
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	transaction.timeout.ms = 60000
	transactional.id = null
	value.serializer = class org.apache.kafka.common.serialization.ByteArraySerializer

10:00:03.097 INFO [main] authenticator.AbstractLogin (AbstractLogin.java:61) - Successfully logged in.
10:00:03.116 WARN [main] producer.ProducerConfig (AbstractConfig.java:385) - These configurations '[ssl.endpoint.identification.algorithm]' were supplied but are not used yet.
10:00:03.117 INFO [main] utils.AppInfoParser (AppInfoParser.java:119) - Kafka version: 3.3.1
10:00:03.117 INFO [main] utils.AppInfoParser (AppInfoParser.java:120) - Kafka commitId: e23c59d00e687ff5
10:00:03.117 INFO [main] utils.AppInfoParser (AppInfoParser.java:121) - Kafka startTimeMs: 1751508003116
10:00:03.119 INFO [main] producer.ProducerConfig (ProducerConfig.java:526) - Idempotence will be disabled because acks is set to 1, not set to 'all'.
10:00:03.119 INFO [main] producer.ProducerConfig (AbstractConfig.java:376) - ProducerConfig values: 
	acks = 1
	batch.size = 1048576
	bootstrap.servers = [**************:9093, ***********:9093, ************:9093]
	buffer.memory = 268435456
	client.dns.lookup = use_all_dns_ips
	client.id = siteHealth_producer_client
	compression.type = none
	connections.max.idle.ms = 540000
	delivery.timeout.ms = 120000
	enable.idempotence = false
	interceptor.classes = []
	key.serializer = class org.apache.kafka.common.serialization.StringSerializer
	linger.ms = 100
	max.block.ms = 600000
	max.in.flight.requests.per.connection = 5
	max.request.size = 5242880
	metadata.max.age.ms = 300000
	metadata.max.idle.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partitioner.adaptive.partitioning.enable = true
	partitioner.availability.timeout.ms = 0
	partitioner.class = null
	partitioner.ignore.keys = false
	receive.buffer.bytes = 32768
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 100000
	retries = 2
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = [hidden]
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.connect.timeout.ms = null
	sasl.login.read.timeout.ms = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.login.retry.backoff.max.ms = 10000
	sasl.login.retry.backoff.ms = 100
	sasl.mechanism = PLAIN
	sasl.oauthbearer.clock.skew.seconds = 30
	sasl.oauthbearer.expected.audience = null
	sasl.oauthbearer.expected.issuer = null
	sasl.oauthbearer.jwks.endpoint.refresh.ms = 3600000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.max.ms = 10000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.ms = 100
	sasl.oauthbearer.jwks.endpoint.url = null
	sasl.oauthbearer.scope.claim.name = scope
	sasl.oauthbearer.sub.claim.name = sub
	sasl.oauthbearer.token.endpoint.url = null
	security.protocol = SASL_PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.3]
	ssl.endpoint.identification.algorithm = 
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.3
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	transaction.timeout.ms = 60000
	transactional.id = null
	value.serializer = class org.apache.kafka.common.serialization.StringSerializer

10:00:03.123 WARN [main] producer.ProducerConfig (AbstractConfig.java:385) - These configurations '[ssl.endpoint.identification.algorithm]' were supplied but are not used yet.
10:00:03.123 INFO [main] utils.AppInfoParser (AppInfoParser.java:119) - Kafka version: 3.3.1
10:00:03.123 INFO [main] utils.AppInfoParser (AppInfoParser.java:120) - Kafka commitId: e23c59d00e687ff5
10:00:03.123 INFO [main] utils.AppInfoParser (AppInfoParser.java:121) - Kafka startTimeMs: 1751508003123
10:00:03.123 WARN [main] utils.AppInfoParser (AppInfoParser.java:68) - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.producer:type=app-info,id=siteHealth_producer_client
	at java.management/com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436)
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865)
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960)
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895)
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320)
	at java.management/com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523)
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64)
	at org.apache.kafka.clients.producer.KafkaProducer.<init>(KafkaProducer.java:462)
	at org.apache.kafka.clients.producer.KafkaProducer.<init>(KafkaProducer.java:291)
	at org.apache.kafka.clients.producer.KafkaProducer.<init>(KafkaProducer.java:318)
	at org.apache.kafka.clients.producer.KafkaProducer.<init>(KafkaProducer.java:303)
	at seoclarity.backend.kafka.clientconstructor.producer.siteHealth.SiteHealthProducerConstructor.createStringValueProducer(SiteHealthProducerConstructor.java:46)
	at seoclarity.backend.kafka.clientconstructor.producer.siteHealth.SiteHealthKafkaProducer.<init>(SiteHealthKafkaProducer.java:29)
	at seoclarity.backend.kafka.clientconstructor.producer.siteHealth.SiteHealthKafkaProducer.getInstance(SiteHealthKafkaProducer.java:19)
	at seoclarity.backend.upload.GoogleAIModeScraperUploader.<clinit>(GoogleAIModeScraperUploader.java:47)
10:00:03.129 INFO [main] utils.ScStringEncryptor (ScStringEncryptor.java:22) - Starting stringEncryptor configuration.
10:00:03.134 INFO [main] utils.ScStringEncryptor (ScStringEncryptor.java:33) - Finished stringEncryptor initialization.
10:00:03.490 INFO [main] http.AmazonHttpClient (ApacheHttpClientFactory.java:90) - Configuring Proxy. Proxy Host: 127.0.0.1 Proxy Port: 7890
10:00:06.379 INFO [kafka-producer-network-thread | siteHealth_producer_client] clients.Metadata (Metadata.java:287) - [Producer clientId=siteHealth_producer_client] Cluster ID: y9Z9s3O6SZe4iw1T2rXZ8g
10:00:06.484 INFO [kafka-producer-network-thread | siteHealth_producer_client] clients.Metadata (Metadata.java:287) - [Producer clientId=siteHealth_producer_client] Cluster ID: y9Z9s3O6SZe4iw1T2rXZ8g
10:00:06.661 INFO [main] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:103) - Starting Google AI mode scraper uploader with 10 threads
10:00:07.362 INFO [main] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:108) - Received 10 messages from SQS
10:00:07.365 INFO [pool-2-thread-2] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:145) - Processing message: b145f9ea-653d-4376-aa8a-1aeaaf39f281
10:00:07.365 INFO [pool-2-thread-4] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:145) - Processing message: 55bc3eb3-6173-4fed-a839-7b6b6ea3c140
10:00:07.365 INFO [pool-2-thread-3] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:145) - Processing message: 1d7f8877-caa5-4159-b6d5-10cf9459ad84
10:00:07.365 INFO [pool-2-thread-1] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:145) - Processing message: 8f2fc06e-2ca0-4ff7-a4b6-ece2dd858164
10:00:07.366 INFO [pool-2-thread-6] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:145) - Processing message: e514ab3c-17ef-47c8-bc13-467181b3fb16
10:00:07.366 INFO [pool-2-thread-5] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:145) - Processing message: dc7ed7ae-f80d-4b0f-a3c3-ca71fe382996
10:00:07.366 INFO [pool-2-thread-10] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:145) - Processing message: 715caeb0-84e0-4ac2-880c-e1208c02a103
10:00:07.366 INFO [main] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:118) - Sleeping for 20 seconds...
10:00:07.366 INFO [pool-2-thread-8] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:145) - Processing message: 875b8f48-b2f9-42f9-8eaf-f3538162a7f5
10:00:07.366 INFO [pool-2-thread-9] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:145) - Processing message: 8c8a3cde-457b-4291-88c3-cca1ca50309b
10:00:07.366 INFO [pool-2-thread-7] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:145) - Processing message: 7abee476-4c89-41ff-802a-139b9dc71664
10:01:38.463 INFO [main] producer.ProducerConfig (ProducerConfig.java:526) - Idempotence will be disabled because acks is set to 1, not set to 'all'.
10:01:38.465 INFO [main] producer.ProducerConfig (AbstractConfig.java:376) - ProducerConfig values: 
	acks = 1
	batch.size = 1048576
	bootstrap.servers = [**************:9093, ***********:9093, ************:9093]
	buffer.memory = 268435456
	client.dns.lookup = use_all_dns_ips
	client.id = siteHealth_producer_client
	compression.type = none
	connections.max.idle.ms = 540000
	delivery.timeout.ms = 120000
	enable.idempotence = false
	interceptor.classes = []
	key.serializer = class org.apache.kafka.common.serialization.StringSerializer
	linger.ms = 100
	max.block.ms = 600000
	max.in.flight.requests.per.connection = 5
	max.request.size = 5242880
	metadata.max.age.ms = 300000
	metadata.max.idle.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partitioner.adaptive.partitioning.enable = true
	partitioner.availability.timeout.ms = 0
	partitioner.class = null
	partitioner.ignore.keys = false
	receive.buffer.bytes = 32768
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 100000
	retries = 2
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = [hidden]
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.connect.timeout.ms = null
	sasl.login.read.timeout.ms = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.login.retry.backoff.max.ms = 10000
	sasl.login.retry.backoff.ms = 100
	sasl.mechanism = PLAIN
	sasl.oauthbearer.clock.skew.seconds = 30
	sasl.oauthbearer.expected.audience = null
	sasl.oauthbearer.expected.issuer = null
	sasl.oauthbearer.jwks.endpoint.refresh.ms = 3600000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.max.ms = 10000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.ms = 100
	sasl.oauthbearer.jwks.endpoint.url = null
	sasl.oauthbearer.scope.claim.name = scope
	sasl.oauthbearer.sub.claim.name = sub
	sasl.oauthbearer.token.endpoint.url = null
	security.protocol = SASL_PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.3]
	ssl.endpoint.identification.algorithm = 
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.3
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	transaction.timeout.ms = 60000
	transactional.id = null
	value.serializer = class org.apache.kafka.common.serialization.ByteArraySerializer

10:01:38.574 INFO [main] authenticator.AbstractLogin (AbstractLogin.java:61) - Successfully logged in.
10:01:38.593 WARN [main] producer.ProducerConfig (AbstractConfig.java:385) - These configurations '[ssl.endpoint.identification.algorithm]' were supplied but are not used yet.
10:01:38.594 INFO [main] utils.AppInfoParser (AppInfoParser.java:119) - Kafka version: 3.3.1
10:01:38.594 INFO [main] utils.AppInfoParser (AppInfoParser.java:120) - Kafka commitId: e23c59d00e687ff5
10:01:38.594 INFO [main] utils.AppInfoParser (AppInfoParser.java:121) - Kafka startTimeMs: 1751508098593
10:01:38.595 INFO [main] producer.ProducerConfig (ProducerConfig.java:526) - Idempotence will be disabled because acks is set to 1, not set to 'all'.
10:01:38.596 INFO [main] producer.ProducerConfig (AbstractConfig.java:376) - ProducerConfig values: 
	acks = 1
	batch.size = 1048576
	bootstrap.servers = [**************:9093, ***********:9093, ************:9093]
	buffer.memory = 268435456
	client.dns.lookup = use_all_dns_ips
	client.id = siteHealth_producer_client
	compression.type = none
	connections.max.idle.ms = 540000
	delivery.timeout.ms = 120000
	enable.idempotence = false
	interceptor.classes = []
	key.serializer = class org.apache.kafka.common.serialization.StringSerializer
	linger.ms = 100
	max.block.ms = 600000
	max.in.flight.requests.per.connection = 5
	max.request.size = 5242880
	metadata.max.age.ms = 300000
	metadata.max.idle.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partitioner.adaptive.partitioning.enable = true
	partitioner.availability.timeout.ms = 0
	partitioner.class = null
	partitioner.ignore.keys = false
	receive.buffer.bytes = 32768
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 100000
	retries = 2
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = [hidden]
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.connect.timeout.ms = null
	sasl.login.read.timeout.ms = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.login.retry.backoff.max.ms = 10000
	sasl.login.retry.backoff.ms = 100
	sasl.mechanism = PLAIN
	sasl.oauthbearer.clock.skew.seconds = 30
	sasl.oauthbearer.expected.audience = null
	sasl.oauthbearer.expected.issuer = null
	sasl.oauthbearer.jwks.endpoint.refresh.ms = 3600000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.max.ms = 10000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.ms = 100
	sasl.oauthbearer.jwks.endpoint.url = null
	sasl.oauthbearer.scope.claim.name = scope
	sasl.oauthbearer.sub.claim.name = sub
	sasl.oauthbearer.token.endpoint.url = null
	security.protocol = SASL_PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.3]
	ssl.endpoint.identification.algorithm = 
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.3
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	transaction.timeout.ms = 60000
	transactional.id = null
	value.serializer = class org.apache.kafka.common.serialization.StringSerializer

10:01:38.599 WARN [main] producer.ProducerConfig (AbstractConfig.java:385) - These configurations '[ssl.endpoint.identification.algorithm]' were supplied but are not used yet.
10:01:38.599 INFO [main] utils.AppInfoParser (AppInfoParser.java:119) - Kafka version: 3.3.1
10:01:38.600 INFO [main] utils.AppInfoParser (AppInfoParser.java:120) - Kafka commitId: e23c59d00e687ff5
10:01:38.600 INFO [main] utils.AppInfoParser (AppInfoParser.java:121) - Kafka startTimeMs: 1751508098599
10:01:38.600 WARN [main] utils.AppInfoParser (AppInfoParser.java:68) - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.producer:type=app-info,id=siteHealth_producer_client
	at java.management/com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436)
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865)
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960)
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895)
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320)
	at java.management/com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523)
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64)
	at org.apache.kafka.clients.producer.KafkaProducer.<init>(KafkaProducer.java:462)
	at org.apache.kafka.clients.producer.KafkaProducer.<init>(KafkaProducer.java:291)
	at org.apache.kafka.clients.producer.KafkaProducer.<init>(KafkaProducer.java:318)
	at org.apache.kafka.clients.producer.KafkaProducer.<init>(KafkaProducer.java:303)
	at seoclarity.backend.kafka.clientconstructor.producer.siteHealth.SiteHealthProducerConstructor.createStringValueProducer(SiteHealthProducerConstructor.java:46)
	at seoclarity.backend.kafka.clientconstructor.producer.siteHealth.SiteHealthKafkaProducer.<init>(SiteHealthKafkaProducer.java:29)
	at seoclarity.backend.kafka.clientconstructor.producer.siteHealth.SiteHealthKafkaProducer.getInstance(SiteHealthKafkaProducer.java:19)
	at seoclarity.backend.upload.GoogleAIModeScraperUploader.<clinit>(GoogleAIModeScraperUploader.java:47)
10:01:38.606 INFO [main] utils.ScStringEncryptor (ScStringEncryptor.java:22) - Starting stringEncryptor configuration.
10:01:38.611 INFO [main] utils.ScStringEncryptor (ScStringEncryptor.java:33) - Finished stringEncryptor initialization.
10:01:38.998 INFO [main] http.AmazonHttpClient (ApacheHttpClientFactory.java:90) - Configuring Proxy. Proxy Host: 127.0.0.1 Proxy Port: 7890
10:01:41.332 INFO [kafka-producer-network-thread | siteHealth_producer_client] clients.Metadata (Metadata.java:287) - [Producer clientId=siteHealth_producer_client] Cluster ID: y9Z9s3O6SZe4iw1T2rXZ8g
10:01:41.332 INFO [kafka-producer-network-thread | siteHealth_producer_client] clients.Metadata (Metadata.java:287) - [Producer clientId=siteHealth_producer_client] Cluster ID: y9Z9s3O6SZe4iw1T2rXZ8g
10:01:42.213 INFO [main] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:103) - Starting Google AI mode scraper uploader with 10 threads
10:01:42.904 INFO [main] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:108) - Received 10 messages from SQS
10:01:42.914 INFO [main] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:118) - Sleeping for 20 seconds...
10:01:42.914 INFO [pool-2-thread-9] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:145) - Processing message: d0898c64-6145-42fe-82b0-3d0e5775af0a
10:01:42.914 INFO [pool-2-thread-1] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:145) - Processing message: 74db7139-d220-4d8b-8c61-df5810e9802f
10:01:42.914 INFO [pool-2-thread-4] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:145) - Processing message: 4658f02f-ec96-49ed-8373-eadd17e09374
10:01:42.914 INFO [pool-2-thread-10] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:145) - Processing message: f6911e74-bead-4298-91d1-37f28ac8bedf
10:01:42.914 INFO [pool-2-thread-2] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:145) - Processing message: d5ba8473-a272-4deb-a6b0-5efb01044a65
10:01:42.914 INFO [pool-2-thread-8] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:145) - Processing message: 630e3174-ea60-4980-9934-5fdc1d1f5069
10:01:42.914 INFO [pool-2-thread-3] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:145) - Processing message: 8291a8c2-6e0f-4594-8a27-5b0bb43023d0
10:01:42.914 INFO [pool-2-thread-6] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:145) - Processing message: a5d5b862-914a-416a-8f5d-701611324329
10:01:42.914 INFO [pool-2-thread-5] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:145) - Processing message: afc80093-8238-4b48-98c7-f20d960a8476
10:01:42.914 INFO [pool-2-thread-7] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:145) - Processing message: 34536f16-4145-42df-ad24-bd4158a62d6d
10:07:02.612 INFO [main] producer.ProducerConfig (ProducerConfig.java:526) - Idempotence will be disabled because acks is set to 1, not set to 'all'.
10:07:02.615 INFO [main] producer.ProducerConfig (AbstractConfig.java:376) - ProducerConfig values: 
	acks = 1
	batch.size = 1048576
	bootstrap.servers = [**************:9093, ***********:9093, ************:9093]
	buffer.memory = 268435456
	client.dns.lookup = use_all_dns_ips
	client.id = siteHealth_producer_client
	compression.type = none
	connections.max.idle.ms = 540000
	delivery.timeout.ms = 120000
	enable.idempotence = false
	interceptor.classes = []
	key.serializer = class org.apache.kafka.common.serialization.StringSerializer
	linger.ms = 100
	max.block.ms = 600000
	max.in.flight.requests.per.connection = 5
	max.request.size = 5242880
	metadata.max.age.ms = 300000
	metadata.max.idle.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partitioner.adaptive.partitioning.enable = true
	partitioner.availability.timeout.ms = 0
	partitioner.class = null
	partitioner.ignore.keys = false
	receive.buffer.bytes = 32768
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 100000
	retries = 2
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = [hidden]
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.connect.timeout.ms = null
	sasl.login.read.timeout.ms = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.login.retry.backoff.max.ms = 10000
	sasl.login.retry.backoff.ms = 100
	sasl.mechanism = PLAIN
	sasl.oauthbearer.clock.skew.seconds = 30
	sasl.oauthbearer.expected.audience = null
	sasl.oauthbearer.expected.issuer = null
	sasl.oauthbearer.jwks.endpoint.refresh.ms = 3600000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.max.ms = 10000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.ms = 100
	sasl.oauthbearer.jwks.endpoint.url = null
	sasl.oauthbearer.scope.claim.name = scope
	sasl.oauthbearer.sub.claim.name = sub
	sasl.oauthbearer.token.endpoint.url = null
	security.protocol = SASL_PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.3]
	ssl.endpoint.identification.algorithm = 
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.3
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	transaction.timeout.ms = 60000
	transactional.id = null
	value.serializer = class org.apache.kafka.common.serialization.ByteArraySerializer

10:07:02.739 INFO [main] authenticator.AbstractLogin (AbstractLogin.java:61) - Successfully logged in.
10:07:02.759 WARN [main] producer.ProducerConfig (AbstractConfig.java:385) - These configurations '[ssl.endpoint.identification.algorithm]' were supplied but are not used yet.
10:07:02.760 INFO [main] utils.AppInfoParser (AppInfoParser.java:119) - Kafka version: 3.3.1
10:07:02.760 INFO [main] utils.AppInfoParser (AppInfoParser.java:120) - Kafka commitId: e23c59d00e687ff5
10:07:02.760 INFO [main] utils.AppInfoParser (AppInfoParser.java:121) - Kafka startTimeMs: 1751508422759
10:07:02.762 INFO [main] producer.ProducerConfig (ProducerConfig.java:526) - Idempotence will be disabled because acks is set to 1, not set to 'all'.
10:07:02.762 INFO [main] producer.ProducerConfig (AbstractConfig.java:376) - ProducerConfig values: 
	acks = 1
	batch.size = 1048576
	bootstrap.servers = [**************:9093, ***********:9093, ************:9093]
	buffer.memory = 268435456
	client.dns.lookup = use_all_dns_ips
	client.id = siteHealth_producer_client
	compression.type = none
	connections.max.idle.ms = 540000
	delivery.timeout.ms = 120000
	enable.idempotence = false
	interceptor.classes = []
	key.serializer = class org.apache.kafka.common.serialization.StringSerializer
	linger.ms = 100
	max.block.ms = 600000
	max.in.flight.requests.per.connection = 5
	max.request.size = 5242880
	metadata.max.age.ms = 300000
	metadata.max.idle.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partitioner.adaptive.partitioning.enable = true
	partitioner.availability.timeout.ms = 0
	partitioner.class = null
	partitioner.ignore.keys = false
	receive.buffer.bytes = 32768
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 100000
	retries = 2
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = [hidden]
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.connect.timeout.ms = null
	sasl.login.read.timeout.ms = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.login.retry.backoff.max.ms = 10000
	sasl.login.retry.backoff.ms = 100
	sasl.mechanism = PLAIN
	sasl.oauthbearer.clock.skew.seconds = 30
	sasl.oauthbearer.expected.audience = null
	sasl.oauthbearer.expected.issuer = null
	sasl.oauthbearer.jwks.endpoint.refresh.ms = 3600000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.max.ms = 10000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.ms = 100
	sasl.oauthbearer.jwks.endpoint.url = null
	sasl.oauthbearer.scope.claim.name = scope
	sasl.oauthbearer.sub.claim.name = sub
	sasl.oauthbearer.token.endpoint.url = null
	security.protocol = SASL_PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.3]
	ssl.endpoint.identification.algorithm = 
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.3
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	transaction.timeout.ms = 60000
	transactional.id = null
	value.serializer = class org.apache.kafka.common.serialization.StringSerializer

10:07:02.766 WARN [main] producer.ProducerConfig (AbstractConfig.java:385) - These configurations '[ssl.endpoint.identification.algorithm]' were supplied but are not used yet.
10:07:02.766 INFO [main] utils.AppInfoParser (AppInfoParser.java:119) - Kafka version: 3.3.1
10:07:02.766 INFO [main] utils.AppInfoParser (AppInfoParser.java:120) - Kafka commitId: e23c59d00e687ff5
10:07:02.766 INFO [main] utils.AppInfoParser (AppInfoParser.java:121) - Kafka startTimeMs: 1751508422766
10:07:02.766 WARN [main] utils.AppInfoParser (AppInfoParser.java:68) - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.producer:type=app-info,id=siteHealth_producer_client
	at java.management/com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436)
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865)
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960)
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895)
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320)
	at java.management/com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523)
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64)
	at org.apache.kafka.clients.producer.KafkaProducer.<init>(KafkaProducer.java:462)
	at org.apache.kafka.clients.producer.KafkaProducer.<init>(KafkaProducer.java:291)
	at org.apache.kafka.clients.producer.KafkaProducer.<init>(KafkaProducer.java:318)
	at org.apache.kafka.clients.producer.KafkaProducer.<init>(KafkaProducer.java:303)
	at seoclarity.backend.kafka.clientconstructor.producer.siteHealth.SiteHealthProducerConstructor.createStringValueProducer(SiteHealthProducerConstructor.java:46)
	at seoclarity.backend.kafka.clientconstructor.producer.siteHealth.SiteHealthKafkaProducer.<init>(SiteHealthKafkaProducer.java:29)
	at seoclarity.backend.kafka.clientconstructor.producer.siteHealth.SiteHealthKafkaProducer.getInstance(SiteHealthKafkaProducer.java:19)
	at seoclarity.backend.upload.GoogleAIModeScraperUploader.<clinit>(GoogleAIModeScraperUploader.java:47)
10:07:02.772 INFO [main] utils.ScStringEncryptor (ScStringEncryptor.java:22) - Starting stringEncryptor configuration.
10:07:02.776 INFO [main] utils.ScStringEncryptor (ScStringEncryptor.java:33) - Finished stringEncryptor initialization.
10:07:03.099 INFO [main] http.AmazonHttpClient (ApacheHttpClientFactory.java:90) - Configuring Proxy. Proxy Host: 127.0.0.1 Proxy Port: 7890
10:07:05.503 INFO [kafka-producer-network-thread | siteHealth_producer_client] clients.Metadata (Metadata.java:287) - [Producer clientId=siteHealth_producer_client] Cluster ID: y9Z9s3O6SZe4iw1T2rXZ8g
10:07:05.503 INFO [kafka-producer-network-thread | siteHealth_producer_client] clients.Metadata (Metadata.java:287) - [Producer clientId=siteHealth_producer_client] Cluster ID: y9Z9s3O6SZe4iw1T2rXZ8g
10:07:06.310 INFO [main] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:103) - Starting Google AI mode scraper uploader with 10 threads
10:07:07.060 INFO [main] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:108) - Received 10 messages from SQS
10:07:07.064 INFO [pool-2-thread-2] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:145) - Processing message: 46b74853-6d11-4c8c-ab05-bf3b158135c1
10:07:07.064 INFO [pool-2-thread-4] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:145) - Processing message: b34d341b-0699-4ea3-b7cc-56ef9025618d
10:07:07.064 INFO [pool-2-thread-6] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:145) - Processing message: 136361ef-4016-413a-83bf-bb63475c1c1e
10:07:07.064 INFO [pool-2-thread-1] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:145) - Processing message: 3f1ff923-2bf5-463d-a4bf-77a4bd7b5faf
10:07:07.064 INFO [pool-2-thread-3] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:145) - Processing message: b0b5a22f-1530-48da-9e2a-7cdd8115ebf2
10:07:07.064 INFO [pool-2-thread-5] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:145) - Processing message: e932a037-9214-46e6-8ea5-9c9c5fa1c7d1
10:07:07.069 INFO [pool-2-thread-7] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:145) - Processing message: f87f3633-79b3-41ec-975f-59bf3911af55
10:07:07.069 INFO [pool-2-thread-8] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:145) - Processing message: e4ce92f9-10b2-427e-9b29-221d84f740b0
10:07:07.069 INFO [pool-2-thread-9] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:145) - Processing message: f354c3ba-90dc-4235-a208-8e739f042144
10:07:07.070 INFO [pool-2-thread-10] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:145) - Processing message: 3fef1276-0a5d-4fda-bb07-273c73e0ba58
10:07:07.069 INFO [main] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:118) - Sleeping for 20 seconds...
10:07:27.961 INFO [main] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:108) - Received 10 messages from SQS
10:07:27.964 INFO [main] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:118) - Sleeping for 20 seconds...
10:07:33.644 INFO [kafka-producer-network-thread | siteHealth_producer_client] clients.Metadata (Metadata.java:402) - [Producer clientId=siteHealth_producer_client] Resetting the last seen epoch of partition ai_insights-1 to 2 since the associated topicId changed from null to XGY-dXXjQ6mqcYypl1P9gg
10:07:33.646 INFO [kafka-producer-network-thread | siteHealth_producer_client] clients.Metadata (Metadata.java:402) - [Producer clientId=siteHealth_producer_client] Resetting the last seen epoch of partition ai_insights-3 to 1 since the associated topicId changed from null to XGY-dXXjQ6mqcYypl1P9gg
10:07:33.648 INFO [kafka-producer-network-thread | siteHealth_producer_client] clients.Metadata (Metadata.java:402) - [Producer clientId=siteHealth_producer_client] Resetting the last seen epoch of partition ai_insights-0 to 1 since the associated topicId changed from null to XGY-dXXjQ6mqcYypl1P9gg
10:07:33.648 INFO [kafka-producer-network-thread | siteHealth_producer_client] clients.Metadata (Metadata.java:402) - [Producer clientId=siteHealth_producer_client] Resetting the last seen epoch of partition ai_insights-4 to 1 since the associated topicId changed from null to XGY-dXXjQ6mqcYypl1P9gg
10:07:33.648 INFO [kafka-producer-network-thread | siteHealth_producer_client] clients.Metadata (Metadata.java:402) - [Producer clientId=siteHealth_producer_client] Resetting the last seen epoch of partition ai_insights-2 to 1 since the associated topicId changed from null to XGY-dXXjQ6mqcYypl1P9gg
10:07:33.649 INFO [kafka-producer-network-thread | siteHealth_producer_client] clients.Metadata (Metadata.java:402) - [Producer clientId=siteHealth_producer_client] Resetting the last seen epoch of partition ai_insights-5 to 2 since the associated topicId changed from null to XGY-dXXjQ6mqcYypl1P9gg
10:07:34.271 INFO [pool-2-thread-3] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:152) - Successfully processed and sent message b0b5a22f-1530-48da-9e2a-7cdd8115ebf2 to Kafka
10:07:34.272 INFO [pool-2-thread-3] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:145) - Processing message: 55329d4e-fdf2-45a5-9f09-b923226b5e8c
10:07:34.641 INFO [kafka-producer-network-thread | siteHealth_producer_client] backup.BackupRankKafkaProducer (KafkaCommonSender.java:42) - send key id: e4ce92f9-10b2-427e-9b29-221d84f740b0, currtime: 2025-07-03 10:07:34
10:07:34.642 INFO [kafka-producer-network-thread | siteHealth_producer_client] backup.BackupRankKafkaProducer (KafkaCommonSender.java:42) - send key id: 3fef1276-0a5d-4fda-bb07-273c73e0ba58, currtime: 2025-07-03 10:07:34
10:07:34.642 INFO [kafka-producer-network-thread | siteHealth_producer_client] backup.BackupRankKafkaProducer (KafkaCommonSender.java:42) - send key id: e932a037-9214-46e6-8ea5-9c9c5fa1c7d1, currtime: 2025-07-03 10:07:34
10:07:34.642 INFO [kafka-producer-network-thread | siteHealth_producer_client] backup.BackupRankKafkaProducer (KafkaCommonSender.java:42) - send key id: b0b5a22f-1530-48da-9e2a-7cdd8115ebf2, currtime: 2025-07-03 10:07:34
10:07:34.642 INFO [kafka-producer-network-thread | siteHealth_producer_client] backup.BackupRankKafkaProducer (KafkaCommonSender.java:42) - send key id: 136361ef-4016-413a-83bf-bb63475c1c1e, currtime: 2025-07-03 10:07:34
10:07:35.195 INFO [pool-2-thread-7] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:152) - Successfully processed and sent message f87f3633-79b3-41ec-975f-59bf3911af55 to Kafka
10:07:35.198 INFO [pool-2-thread-7] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:145) - Processing message: 0bb09284-c5c6-44dc-9700-75fa2291caea
10:07:35.205 INFO [pool-2-thread-5] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:152) - Successfully processed and sent message e932a037-9214-46e6-8ea5-9c9c5fa1c7d1 to Kafka
10:07:35.205 INFO [pool-2-thread-5] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:145) - Processing message: 511bc076-120f-4c8e-abcc-da60767bc527
10:07:35.252 INFO [pool-2-thread-9] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:152) - Successfully processed and sent message f354c3ba-90dc-4235-a208-8e739f042144 to Kafka
10:07:35.252 INFO [pool-2-thread-9] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:145) - Processing message: 66bf96ca-2447-4208-b757-7232dc9831f9
10:07:35.270 INFO [pool-2-thread-8] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:152) - Successfully processed and sent message e4ce92f9-10b2-427e-9b29-221d84f740b0 to Kafka
10:07:35.270 INFO [pool-2-thread-8] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:145) - Processing message: 75e6c568-8de7-4ba4-8752-f91f6e6900b3
10:07:35.278 INFO [pool-2-thread-6] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:152) - Successfully processed and sent message 136361ef-4016-413a-83bf-bb63475c1c1e to Kafka
10:07:35.281 INFO [pool-2-thread-6] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:145) - Processing message: e1998fc2-0c6c-4eca-87e4-3dc7a60e37b9
10:07:35.282 INFO [pool-2-thread-10] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:152) - Successfully processed and sent message 3fef1276-0a5d-4fda-bb07-273c73e0ba58 to Kafka
10:07:35.292 INFO [pool-2-thread-10] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:145) - Processing message: cb90b7d2-0195-4156-8a26-9e2cc33f876a
10:07:35.400 INFO [pool-2-thread-4] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:152) - Successfully processed and sent message b34d341b-0699-4ea3-b7cc-56ef9025618d to Kafka
10:07:35.400 INFO [pool-2-thread-4] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:145) - Processing message: 5f7e4976-554b-43c2-9b38-f437d003feca
10:07:36.716 INFO [kafka-producer-network-thread | siteHealth_producer_client] backup.BackupRankKafkaProducer (KafkaCommonSender.java:42) - send key id: f87f3633-79b3-41ec-975f-59bf3911af55, currtime: 2025-07-03 10:07:36
10:07:36.717 INFO [kafka-producer-network-thread | siteHealth_producer_client] backup.BackupRankKafkaProducer (KafkaCommonSender.java:42) - send key id: f354c3ba-90dc-4235-a208-8e739f042144, currtime: 2025-07-03 10:07:36
10:07:36.717 INFO [kafka-producer-network-thread | siteHealth_producer_client] backup.BackupRankKafkaProducer (KafkaCommonSender.java:42) - send key id: b34d341b-0699-4ea3-b7cc-56ef9025618d, currtime: 2025-07-03 10:07:36
10:07:38.198 INFO [pool-2-thread-1] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:152) - Successfully processed and sent message 3f1ff923-2bf5-463d-a4bf-77a4bd7b5faf to Kafka
10:07:38.199 INFO [pool-2-thread-1] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:145) - Processing message: 082ccc77-6bce-4c2e-ac1e-5e94d1ec1816
10:07:39.814 INFO [pool-2-thread-2] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:152) - Successfully processed and sent message 46b74853-6d11-4c8c-ab05-bf3b158135c1 to Kafka
10:07:39.815 INFO [pool-2-thread-2] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:145) - Processing message: 5876c7ba-34b4-47cd-a493-5cbe5fd9d018
10:07:40.447 INFO [kafka-producer-network-thread | siteHealth_producer_client] backup.BackupRankKafkaProducer (KafkaCommonSender.java:42) - send key id: 46b74853-6d11-4c8c-ab05-bf3b158135c1, currtime: 2025-07-03 10:07:40
10:07:40.448 INFO [kafka-producer-network-thread | siteHealth_producer_client] backup.BackupRankKafkaProducer (KafkaCommonSender.java:42) - send key id: 3f1ff923-2bf5-463d-a4bf-77a4bd7b5faf, currtime: 2025-07-03 10:07:40
10:07:48.692 INFO [main] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:108) - Received 10 messages from SQS
10:07:48.695 INFO [main] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:118) - Sleeping for 20 seconds...
11:06:40.899 INFO [main] producer.ProducerConfig (ProducerConfig.java:526) - Idempotence will be disabled because acks is set to 1, not set to 'all'.
11:06:40.902 INFO [main] producer.ProducerConfig (AbstractConfig.java:376) - ProducerConfig values: 
	acks = 1
	batch.size = 1048576
	bootstrap.servers = [**************:9093, ***********:9093, ************:9093]
	buffer.memory = 268435456
	client.dns.lookup = use_all_dns_ips
	client.id = siteHealth_producer_client
	compression.type = none
	connections.max.idle.ms = 540000
	delivery.timeout.ms = 120000
	enable.idempotence = false
	interceptor.classes = []
	key.serializer = class org.apache.kafka.common.serialization.StringSerializer
	linger.ms = 100
	max.block.ms = 600000
	max.in.flight.requests.per.connection = 5
	max.request.size = 5242880
	metadata.max.age.ms = 300000
	metadata.max.idle.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partitioner.adaptive.partitioning.enable = true
	partitioner.availability.timeout.ms = 0
	partitioner.class = null
	partitioner.ignore.keys = false
	receive.buffer.bytes = 32768
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 100000
	retries = 2
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = [hidden]
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.connect.timeout.ms = null
	sasl.login.read.timeout.ms = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.login.retry.backoff.max.ms = 10000
	sasl.login.retry.backoff.ms = 100
	sasl.mechanism = PLAIN
	sasl.oauthbearer.clock.skew.seconds = 30
	sasl.oauthbearer.expected.audience = null
	sasl.oauthbearer.expected.issuer = null
	sasl.oauthbearer.jwks.endpoint.refresh.ms = 3600000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.max.ms = 10000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.ms = 100
	sasl.oauthbearer.jwks.endpoint.url = null
	sasl.oauthbearer.scope.claim.name = scope
	sasl.oauthbearer.sub.claim.name = sub
	sasl.oauthbearer.token.endpoint.url = null
	security.protocol = SASL_PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.3]
	ssl.endpoint.identification.algorithm = 
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.3
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	transaction.timeout.ms = 60000
	transactional.id = null
	value.serializer = class org.apache.kafka.common.serialization.ByteArraySerializer

11:06:41.029 INFO [main] authenticator.AbstractLogin (AbstractLogin.java:61) - Successfully logged in.
11:06:41.049 WARN [main] producer.ProducerConfig (AbstractConfig.java:385) - These configurations '[ssl.endpoint.identification.algorithm]' were supplied but are not used yet.
11:06:41.050 INFO [main] utils.AppInfoParser (AppInfoParser.java:119) - Kafka version: 3.3.1
11:06:41.050 INFO [main] utils.AppInfoParser (AppInfoParser.java:120) - Kafka commitId: e23c59d00e687ff5
11:06:41.050 INFO [main] utils.AppInfoParser (AppInfoParser.java:121) - Kafka startTimeMs: 1751512001049
11:06:41.051 INFO [main] producer.ProducerConfig (ProducerConfig.java:526) - Idempotence will be disabled because acks is set to 1, not set to 'all'.
11:06:41.052 INFO [main] producer.ProducerConfig (AbstractConfig.java:376) - ProducerConfig values: 
	acks = 1
	batch.size = 1048576
	bootstrap.servers = [**************:9093, ***********:9093, ************:9093]
	buffer.memory = 268435456
	client.dns.lookup = use_all_dns_ips
	client.id = siteHealth_producer_client
	compression.type = none
	connections.max.idle.ms = 540000
	delivery.timeout.ms = 120000
	enable.idempotence = false
	interceptor.classes = []
	key.serializer = class org.apache.kafka.common.serialization.StringSerializer
	linger.ms = 100
	max.block.ms = 600000
	max.in.flight.requests.per.connection = 5
	max.request.size = 5242880
	metadata.max.age.ms = 300000
	metadata.max.idle.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partitioner.adaptive.partitioning.enable = true
	partitioner.availability.timeout.ms = 0
	partitioner.class = null
	partitioner.ignore.keys = false
	receive.buffer.bytes = 32768
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 100000
	retries = 2
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = [hidden]
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.connect.timeout.ms = null
	sasl.login.read.timeout.ms = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.login.retry.backoff.max.ms = 10000
	sasl.login.retry.backoff.ms = 100
	sasl.mechanism = PLAIN
	sasl.oauthbearer.clock.skew.seconds = 30
	sasl.oauthbearer.expected.audience = null
	sasl.oauthbearer.expected.issuer = null
	sasl.oauthbearer.jwks.endpoint.refresh.ms = 3600000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.max.ms = 10000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.ms = 100
	sasl.oauthbearer.jwks.endpoint.url = null
	sasl.oauthbearer.scope.claim.name = scope
	sasl.oauthbearer.sub.claim.name = sub
	sasl.oauthbearer.token.endpoint.url = null
	security.protocol = SASL_PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.3]
	ssl.endpoint.identification.algorithm = 
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.3
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	transaction.timeout.ms = 60000
	transactional.id = null
	value.serializer = class org.apache.kafka.common.serialization.StringSerializer

11:06:41.055 WARN [main] producer.ProducerConfig (AbstractConfig.java:385) - These configurations '[ssl.endpoint.identification.algorithm]' were supplied but are not used yet.
11:06:41.055 INFO [main] utils.AppInfoParser (AppInfoParser.java:119) - Kafka version: 3.3.1
11:06:41.055 INFO [main] utils.AppInfoParser (AppInfoParser.java:120) - Kafka commitId: e23c59d00e687ff5
11:06:41.055 INFO [main] utils.AppInfoParser (AppInfoParser.java:121) - Kafka startTimeMs: 1751512001055
11:06:41.056 WARN [main] utils.AppInfoParser (AppInfoParser.java:68) - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.producer:type=app-info,id=siteHealth_producer_client
	at java.management/com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436)
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865)
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960)
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895)
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320)
	at java.management/com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523)
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64)
	at org.apache.kafka.clients.producer.KafkaProducer.<init>(KafkaProducer.java:462)
	at org.apache.kafka.clients.producer.KafkaProducer.<init>(KafkaProducer.java:291)
	at org.apache.kafka.clients.producer.KafkaProducer.<init>(KafkaProducer.java:318)
	at org.apache.kafka.clients.producer.KafkaProducer.<init>(KafkaProducer.java:303)
	at seoclarity.backend.kafka.clientconstructor.producer.siteHealth.SiteHealthProducerConstructor.createStringValueProducer(SiteHealthProducerConstructor.java:46)
	at seoclarity.backend.kafka.clientconstructor.producer.siteHealth.SiteHealthKafkaProducer.<init>(SiteHealthKafkaProducer.java:29)
	at seoclarity.backend.kafka.clientconstructor.producer.siteHealth.SiteHealthKafkaProducer.getInstance(SiteHealthKafkaProducer.java:19)
	at seoclarity.backend.upload.GoogleAIModeScraperUploader.<clinit>(GoogleAIModeScraperUploader.java:55)
11:06:41.062 INFO [main] utils.ScStringEncryptor (ScStringEncryptor.java:22) - Starting stringEncryptor configuration.
11:06:41.066 INFO [main] utils.ScStringEncryptor (ScStringEncryptor.java:33) - Finished stringEncryptor initialization.
11:06:41.408 INFO [main] http.AmazonHttpClient (ApacheHttpClientFactory.java:90) - Configuring Proxy. Proxy Host: 127.0.0.1 Proxy Port: 7890
11:06:44.118 INFO [kafka-producer-network-thread | siteHealth_producer_client] clients.Metadata (Metadata.java:287) - [Producer clientId=siteHealth_producer_client] Cluster ID: y9Z9s3O6SZe4iw1T2rXZ8g
11:06:44.926 INFO [kafka-producer-network-thread | siteHealth_producer_client] clients.Metadata (Metadata.java:287) - [Producer clientId=siteHealth_producer_client] Cluster ID: y9Z9s3O6SZe4iw1T2rXZ8g
11:06:47.033 INFO [main] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:112) - Starting Google AI mode scraper uploader with 10 threads
11:06:48.174 INFO [main] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:117) - Received 10 messages from SQS
11:06:48.183 INFO [pool-2-thread-2] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:154) - Processing message: 0d1277c5-add2-43fe-b0e4-5e01c7c458ac
11:06:48.183 INFO [pool-2-thread-10] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:154) - Processing message: 37bbb33f-118d-408f-8038-805df76a77a2
11:06:48.183 INFO [main] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:127) - Sleeping for 20 seconds...
11:06:48.183 INFO [pool-2-thread-3] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:154) - Processing message: e1ae9280-51fe-48ed-8cd3-1dd758828338
11:06:48.183 INFO [pool-2-thread-1] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:154) - Processing message: 8bf5f1de-938c-4d2d-a49c-ae307c4bdb01
11:06:48.183 INFO [pool-2-thread-8] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:154) - Processing message: 74ae5873-7226-4c21-8b5f-7ecdb042ba74
11:06:48.183 INFO [pool-2-thread-9] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:154) - Processing message: eaed4d75-2f2d-4eab-8b3b-47eef41af37a
11:06:48.183 INFO [pool-2-thread-4] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:154) - Processing message: c8c27a9c-4502-4f7c-bdcc-10c9a64d5bff
11:06:48.183 INFO [pool-2-thread-5] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:154) - Processing message: 0acea8f5-0f88-4dfc-a0ab-7342c71a87fa
11:06:48.183 INFO [pool-2-thread-7] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:154) - Processing message: fe7eb572-d707-4d38-a6f0-ffb1723804e3
11:06:48.183 INFO [pool-2-thread-6] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:154) - Processing message: 4346c696-e447-4a83-b887-170723828087
11:07:09.100 INFO [main] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:117) - Received 10 messages from SQS
11:07:09.100 INFO [main] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:127) - Sleeping for 20 seconds...
11:15:01.580 INFO [main] producer.ProducerConfig (ProducerConfig.java:526) - Idempotence will be disabled because acks is set to 1, not set to 'all'.
11:15:01.583 INFO [main] producer.ProducerConfig (AbstractConfig.java:376) - ProducerConfig values: 
	acks = 1
	batch.size = 1048576
	bootstrap.servers = [**************:9093, ***********:9093, ************:9093]
	buffer.memory = 268435456
	client.dns.lookup = use_all_dns_ips
	client.id = siteHealth_producer_client
	compression.type = none
	connections.max.idle.ms = 540000
	delivery.timeout.ms = 120000
	enable.idempotence = false
	interceptor.classes = []
	key.serializer = class org.apache.kafka.common.serialization.StringSerializer
	linger.ms = 100
	max.block.ms = 600000
	max.in.flight.requests.per.connection = 5
	max.request.size = 5242880
	metadata.max.age.ms = 300000
	metadata.max.idle.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partitioner.adaptive.partitioning.enable = true
	partitioner.availability.timeout.ms = 0
	partitioner.class = null
	partitioner.ignore.keys = false
	receive.buffer.bytes = 32768
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 100000
	retries = 2
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = [hidden]
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.connect.timeout.ms = null
	sasl.login.read.timeout.ms = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.login.retry.backoff.max.ms = 10000
	sasl.login.retry.backoff.ms = 100
	sasl.mechanism = PLAIN
	sasl.oauthbearer.clock.skew.seconds = 30
	sasl.oauthbearer.expected.audience = null
	sasl.oauthbearer.expected.issuer = null
	sasl.oauthbearer.jwks.endpoint.refresh.ms = 3600000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.max.ms = 10000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.ms = 100
	sasl.oauthbearer.jwks.endpoint.url = null
	sasl.oauthbearer.scope.claim.name = scope
	sasl.oauthbearer.sub.claim.name = sub
	sasl.oauthbearer.token.endpoint.url = null
	security.protocol = SASL_PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.3]
	ssl.endpoint.identification.algorithm = 
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.3
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	transaction.timeout.ms = 60000
	transactional.id = null
	value.serializer = class org.apache.kafka.common.serialization.ByteArraySerializer

11:15:01.710 INFO [main] authenticator.AbstractLogin (AbstractLogin.java:61) - Successfully logged in.
11:15:01.732 WARN [main] producer.ProducerConfig (AbstractConfig.java:385) - These configurations '[ssl.endpoint.identification.algorithm]' were supplied but are not used yet.
11:15:01.733 INFO [main] utils.AppInfoParser (AppInfoParser.java:119) - Kafka version: 3.3.1
11:15:01.734 INFO [main] utils.AppInfoParser (AppInfoParser.java:120) - Kafka commitId: e23c59d00e687ff5
11:15:01.734 INFO [main] utils.AppInfoParser (AppInfoParser.java:121) - Kafka startTimeMs: 1751512501732
11:15:01.735 INFO [main] producer.ProducerConfig (ProducerConfig.java:526) - Idempotence will be disabled because acks is set to 1, not set to 'all'.
11:15:01.735 INFO [main] producer.ProducerConfig (AbstractConfig.java:376) - ProducerConfig values: 
	acks = 1
	batch.size = 1048576
	bootstrap.servers = [**************:9093, ***********:9093, ************:9093]
	buffer.memory = 268435456
	client.dns.lookup = use_all_dns_ips
	client.id = siteHealth_producer_client
	compression.type = none
	connections.max.idle.ms = 540000
	delivery.timeout.ms = 120000
	enable.idempotence = false
	interceptor.classes = []
	key.serializer = class org.apache.kafka.common.serialization.StringSerializer
	linger.ms = 100
	max.block.ms = 600000
	max.in.flight.requests.per.connection = 5
	max.request.size = 5242880
	metadata.max.age.ms = 300000
	metadata.max.idle.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partitioner.adaptive.partitioning.enable = true
	partitioner.availability.timeout.ms = 0
	partitioner.class = null
	partitioner.ignore.keys = false
	receive.buffer.bytes = 32768
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 100000
	retries = 2
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = [hidden]
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.connect.timeout.ms = null
	sasl.login.read.timeout.ms = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.login.retry.backoff.max.ms = 10000
	sasl.login.retry.backoff.ms = 100
	sasl.mechanism = PLAIN
	sasl.oauthbearer.clock.skew.seconds = 30
	sasl.oauthbearer.expected.audience = null
	sasl.oauthbearer.expected.issuer = null
	sasl.oauthbearer.jwks.endpoint.refresh.ms = 3600000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.max.ms = 10000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.ms = 100
	sasl.oauthbearer.jwks.endpoint.url = null
	sasl.oauthbearer.scope.claim.name = scope
	sasl.oauthbearer.sub.claim.name = sub
	sasl.oauthbearer.token.endpoint.url = null
	security.protocol = SASL_PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.3]
	ssl.endpoint.identification.algorithm = 
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.3
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	transaction.timeout.ms = 60000
	transactional.id = null
	value.serializer = class org.apache.kafka.common.serialization.StringSerializer

11:15:01.739 WARN [main] producer.ProducerConfig (AbstractConfig.java:385) - These configurations '[ssl.endpoint.identification.algorithm]' were supplied but are not used yet.
11:15:01.739 INFO [main] utils.AppInfoParser (AppInfoParser.java:119) - Kafka version: 3.3.1
11:15:01.739 INFO [main] utils.AppInfoParser (AppInfoParser.java:120) - Kafka commitId: e23c59d00e687ff5
11:15:01.740 INFO [main] utils.AppInfoParser (AppInfoParser.java:121) - Kafka startTimeMs: 1751512501739
11:15:01.740 WARN [main] utils.AppInfoParser (AppInfoParser.java:68) - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.producer:type=app-info,id=siteHealth_producer_client
	at java.management/com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436)
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865)
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960)
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895)
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320)
	at java.management/com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523)
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64)
	at org.apache.kafka.clients.producer.KafkaProducer.<init>(KafkaProducer.java:462)
	at org.apache.kafka.clients.producer.KafkaProducer.<init>(KafkaProducer.java:291)
	at org.apache.kafka.clients.producer.KafkaProducer.<init>(KafkaProducer.java:318)
	at org.apache.kafka.clients.producer.KafkaProducer.<init>(KafkaProducer.java:303)
	at seoclarity.backend.kafka.clientconstructor.producer.siteHealth.SiteHealthProducerConstructor.createStringValueProducer(SiteHealthProducerConstructor.java:46)
	at seoclarity.backend.kafka.clientconstructor.producer.siteHealth.SiteHealthKafkaProducer.<init>(SiteHealthKafkaProducer.java:29)
	at seoclarity.backend.kafka.clientconstructor.producer.siteHealth.SiteHealthKafkaProducer.getInstance(SiteHealthKafkaProducer.java:19)
	at seoclarity.backend.upload.GoogleAIModeScraperUploader.<clinit>(GoogleAIModeScraperUploader.java:59)
11:15:01.745 INFO [main] utils.ScStringEncryptor (ScStringEncryptor.java:22) - Starting stringEncryptor configuration.
11:15:01.749 INFO [main] utils.ScStringEncryptor (ScStringEncryptor.java:33) - Finished stringEncryptor initialization.
11:15:02.133 INFO [main] http.AmazonHttpClient (ApacheHttpClientFactory.java:90) - Configuring Proxy. Proxy Host: 127.0.0.1 Proxy Port: 7890
11:15:04.625 INFO [kafka-producer-network-thread | siteHealth_producer_client] clients.Metadata (Metadata.java:287) - [Producer clientId=siteHealth_producer_client] Cluster ID: y9Z9s3O6SZe4iw1T2rXZ8g
11:15:04.798 INFO [kafka-producer-network-thread | siteHealth_producer_client] clients.Metadata (Metadata.java:287) - [Producer clientId=siteHealth_producer_client] Cluster ID: y9Z9s3O6SZe4iw1T2rXZ8g
11:15:06.251 INFO [main] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:122) - Starting Google AI mode scraper uploader with 10 threads
11:15:07.129 INFO [main] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:127) - Received 10 messages from SQS
11:15:07.133 INFO [pool-2-thread-1] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:164) - Processing message: 3689f3e9-b64c-4b0a-b7c4-291ee2fd462c
11:15:07.134 INFO [pool-2-thread-3] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:164) - Processing message: ad66cd70-17ce-49bc-b666-05d12faa6e80
11:15:07.134 INFO [pool-2-thread-7] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:164) - Processing message: 3b0a73f6-7c1f-48d2-aee8-1caa6d36849a
11:15:07.134 INFO [pool-2-thread-2] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:164) - Processing message: c29887f4-d372-462f-a7a5-0295ed40ae00
11:15:07.135 INFO [pool-2-thread-9] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:164) - Processing message: 4487c76f-8b53-47b1-ac86-5a9ae592788b
11:15:07.134 INFO [pool-2-thread-8] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:164) - Processing message: e4d7abaf-0592-478f-ae7a-6eaeac8260dc
11:15:07.134 INFO [pool-2-thread-6] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:164) - Processing message: 68a96092-550d-42e1-b1bb-60fcc170956d
11:15:07.134 INFO [pool-2-thread-4] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:164) - Processing message: c61cf19e-d221-4b49-8b7d-38f570516ccb
11:15:07.134 INFO [pool-2-thread-5] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:164) - Processing message: 5c591f8f-aa2a-430a-a75e-0f863354f252
11:15:07.135 INFO [pool-2-thread-10] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:164) - Processing message: 128e124e-7079-480c-8360-60da92c04b34
11:15:07.135 INFO [main] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:137) - Sleeping for 20 seconds...
11:15:28.019 INFO [main] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:127) - Received 10 messages from SQS
11:15:28.020 INFO [main] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:137) - Sleeping for 20 seconds...
11:17:08.968 INFO [pool-2-thread-5] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:303) - Sent to Loggly: {"response" : "ok"}
11:17:08.972 WARN [pool-2-thread-7] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:238) - Attempt 1/3 failed for message {"prompt":"What digital marketing courses teach techniques for creating viral content?","system_prompt":"You are a helpful assistant.\nRespond in the following language: English (English)\nResponses should be strictly in the context of the following country: United States","topic":"ai_insights","country_language":"US_en","date":"2025-07-01 02:32:50.530","ai_se_id":4,"ai_language_id":1,"id":319293,"promptId":1,"brandPromptId":2,"keywordId":42231,"ownDomainId":13735,"frequency":7,"sendToQDate":20250629}, url: https://run.operia.io/965c83c8924af44b/?url=https%3A%2F%2Fwww.google.com%2Fsearch%3Fq%3DWhat+digital+marketing+courses+teach+techniques+for+creating+viral+content%3F
org.apache.http.client.ClientProtocolException
	at org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:187)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:83)
	at org.apache.http.impl.client.CloseableHttpClient.execute(CloseableHttpClient.java:108)
	at seoclarity.backend.upload.GoogleAIModeScraperUploader.processMessageData(GoogleAIModeScraperUploader.java:198)
	at seoclarity.backend.upload.GoogleAIModeScraperUploader.processMessage(GoogleAIModeScraperUploader.java:166)
	at seoclarity.backend.upload.GoogleAIModeScraperUploader.lambda$start$0(GoogleAIModeScraperUploader.java:129)
	at java.base/java.util.concurrent.Executors$RunnableAdapter.call(Executors.java:539)
	at java.base/java.util.concurrent.FutureTask.run$$$capture(FutureTask.java:264)
	at java.base/java.util.concurrent.FutureTask.run(FutureTask.java)
	at --- Async.Stack.Trace --- (captured by IntelliJ IDEA debugger)
	at java.base/java.util.concurrent.FutureTask.<init>(FutureTask.java:151)
	at java.base/java.util.concurrent.AbstractExecutorService.newTaskFor(AbstractExecutorService.java:98)
	at java.base/java.util.concurrent.AbstractExecutorService.submit(AbstractExecutorService.java:122)
	at seoclarity.backend.upload.GoogleAIModeScraperUploader.start(GoogleAIModeScraperUploader.java:129)
	at seoclarity.backend.upload.GoogleAIModeScraperUploader.main(GoogleAIModeScraperUploader.java:118)
Caused by: org.apache.http.client.RedirectException: Maximum redirects (5) exceeded
	at org.apache.http.impl.execchain.RedirectExec.execute(RedirectExec.java:122)
	at org.apache.http.impl.client.InternalHttpClient.doExecute(InternalHttpClient.java:185)
	... 14 more
11:17:09.013 WARN [pool-2-thread-6] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:310) - Attempt 1/3 failed to send to Loggly, retrying...
11:17:09.016 WARN [pool-2-thread-3] aliyun.oss (LogUtils.java:70) - [Client]Unable to execute HTTP request: Broken pipe
[ErrorCode]: SocketException
[RequestId]: Unknown
11:17:10.575 INFO [main] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:127) - Received 10 messages from SQS
11:17:10.576 INFO [main] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:137) - Sleeping for 20 seconds...
11:22:13.296 INFO [main] producer.ProducerConfig (ProducerConfig.java:526) - Idempotence will be disabled because acks is set to 1, not set to 'all'.
11:22:13.299 INFO [main] producer.ProducerConfig (AbstractConfig.java:376) - ProducerConfig values: 
	acks = 1
	batch.size = 1048576
	bootstrap.servers = [**************:9093, ***********:9093, ************:9093]
	buffer.memory = 268435456
	client.dns.lookup = use_all_dns_ips
	client.id = siteHealth_producer_client
	compression.type = none
	connections.max.idle.ms = 540000
	delivery.timeout.ms = 120000
	enable.idempotence = false
	interceptor.classes = []
	key.serializer = class org.apache.kafka.common.serialization.StringSerializer
	linger.ms = 100
	max.block.ms = 600000
	max.in.flight.requests.per.connection = 5
	max.request.size = 5242880
	metadata.max.age.ms = 300000
	metadata.max.idle.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partitioner.adaptive.partitioning.enable = true
	partitioner.availability.timeout.ms = 0
	partitioner.class = null
	partitioner.ignore.keys = false
	receive.buffer.bytes = 32768
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 100000
	retries = 2
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = [hidden]
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.connect.timeout.ms = null
	sasl.login.read.timeout.ms = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.login.retry.backoff.max.ms = 10000
	sasl.login.retry.backoff.ms = 100
	sasl.mechanism = PLAIN
	sasl.oauthbearer.clock.skew.seconds = 30
	sasl.oauthbearer.expected.audience = null
	sasl.oauthbearer.expected.issuer = null
	sasl.oauthbearer.jwks.endpoint.refresh.ms = 3600000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.max.ms = 10000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.ms = 100
	sasl.oauthbearer.jwks.endpoint.url = null
	sasl.oauthbearer.scope.claim.name = scope
	sasl.oauthbearer.sub.claim.name = sub
	sasl.oauthbearer.token.endpoint.url = null
	security.protocol = SASL_PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.3]
	ssl.endpoint.identification.algorithm = 
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.3
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	transaction.timeout.ms = 60000
	transactional.id = null
	value.serializer = class org.apache.kafka.common.serialization.ByteArraySerializer

11:22:13.427 INFO [main] authenticator.AbstractLogin (AbstractLogin.java:61) - Successfully logged in.
11:22:13.653 WARN [main] producer.ProducerConfig (AbstractConfig.java:385) - These configurations '[ssl.endpoint.identification.algorithm]' were supplied but are not used yet.
11:22:13.666 INFO [main] utils.AppInfoParser (AppInfoParser.java:119) - Kafka version: 3.3.1
11:22:13.667 INFO [main] utils.AppInfoParser (AppInfoParser.java:120) - Kafka commitId: e23c59d00e687ff5
11:22:13.667 INFO [main] utils.AppInfoParser (AppInfoParser.java:121) - Kafka startTimeMs: 1751512933654
11:22:13.717 INFO [main] producer.ProducerConfig (ProducerConfig.java:526) - Idempotence will be disabled because acks is set to 1, not set to 'all'.
11:22:13.721 INFO [main] producer.ProducerConfig (AbstractConfig.java:376) - ProducerConfig values: 
	acks = 1
	batch.size = 1048576
	bootstrap.servers = [**************:9093, ***********:9093, ************:9093]
	buffer.memory = 268435456
	client.dns.lookup = use_all_dns_ips
	client.id = siteHealth_producer_client
	compression.type = none
	connections.max.idle.ms = 540000
	delivery.timeout.ms = 120000
	enable.idempotence = false
	interceptor.classes = []
	key.serializer = class org.apache.kafka.common.serialization.StringSerializer
	linger.ms = 100
	max.block.ms = 600000
	max.in.flight.requests.per.connection = 5
	max.request.size = 5242880
	metadata.max.age.ms = 300000
	metadata.max.idle.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partitioner.adaptive.partitioning.enable = true
	partitioner.availability.timeout.ms = 0
	partitioner.class = null
	partitioner.ignore.keys = false
	receive.buffer.bytes = 32768
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 100000
	retries = 2
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = [hidden]
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.connect.timeout.ms = null
	sasl.login.read.timeout.ms = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.login.retry.backoff.max.ms = 10000
	sasl.login.retry.backoff.ms = 100
	sasl.mechanism = PLAIN
	sasl.oauthbearer.clock.skew.seconds = 30
	sasl.oauthbearer.expected.audience = null
	sasl.oauthbearer.expected.issuer = null
	sasl.oauthbearer.jwks.endpoint.refresh.ms = 3600000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.max.ms = 10000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.ms = 100
	sasl.oauthbearer.jwks.endpoint.url = null
	sasl.oauthbearer.scope.claim.name = scope
	sasl.oauthbearer.sub.claim.name = sub
	sasl.oauthbearer.token.endpoint.url = null
	security.protocol = SASL_PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.3]
	ssl.endpoint.identification.algorithm = 
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.3
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	transaction.timeout.ms = 60000
	transactional.id = null
	value.serializer = class org.apache.kafka.common.serialization.StringSerializer

11:22:13.732 WARN [main] producer.ProducerConfig (AbstractConfig.java:385) - These configurations '[ssl.endpoint.identification.algorithm]' were supplied but are not used yet.
11:22:13.732 INFO [main] utils.AppInfoParser (AppInfoParser.java:119) - Kafka version: 3.3.1
11:22:13.740 INFO [main] utils.AppInfoParser (AppInfoParser.java:120) - Kafka commitId: e23c59d00e687ff5
11:22:13.741 INFO [main] utils.AppInfoParser (AppInfoParser.java:121) - Kafka startTimeMs: 1751512933732
11:22:13.742 WARN [main] utils.AppInfoParser (AppInfoParser.java:68) - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.producer:type=app-info,id=siteHealth_producer_client
	at java.management/com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436)
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865)
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960)
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895)
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320)
	at java.management/com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523)
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64)
	at org.apache.kafka.clients.producer.KafkaProducer.<init>(KafkaProducer.java:462)
	at org.apache.kafka.clients.producer.KafkaProducer.<init>(KafkaProducer.java:291)
	at org.apache.kafka.clients.producer.KafkaProducer.<init>(KafkaProducer.java:318)
	at org.apache.kafka.clients.producer.KafkaProducer.<init>(KafkaProducer.java:303)
	at seoclarity.backend.kafka.clientconstructor.producer.siteHealth.SiteHealthProducerConstructor.createStringValueProducer(SiteHealthProducerConstructor.java:46)
	at seoclarity.backend.kafka.clientconstructor.producer.siteHealth.SiteHealthKafkaProducer.<init>(SiteHealthKafkaProducer.java:29)
	at seoclarity.backend.kafka.clientconstructor.producer.siteHealth.SiteHealthKafkaProducer.getInstance(SiteHealthKafkaProducer.java:19)
	at seoclarity.backend.upload.GoogleAIModeScraperUploader.<clinit>(GoogleAIModeScraperUploader.java:59)
11:22:13.764 INFO [main] utils.ScStringEncryptor (ScStringEncryptor.java:22) - Starting stringEncryptor configuration.
11:22:13.777 INFO [main] utils.ScStringEncryptor (ScStringEncryptor.java:33) - Finished stringEncryptor initialization.
11:22:14.367 INFO [main] http.AmazonHttpClient (ApacheHttpClientFactory.java:90) - Configuring Proxy. Proxy Host: 127.0.0.1 Proxy Port: 7890
11:22:17.027 INFO [kafka-producer-network-thread | siteHealth_producer_client] clients.Metadata (Metadata.java:287) - [Producer clientId=siteHealth_producer_client] Cluster ID: y9Z9s3O6SZe4iw1T2rXZ8g
11:22:17.528 INFO [kafka-producer-network-thread | siteHealth_producer_client] clients.Metadata (Metadata.java:287) - [Producer clientId=siteHealth_producer_client] Cluster ID: y9Z9s3O6SZe4iw1T2rXZ8g
11:22:18.029 INFO [main] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:123) - Starting Google AI mode scraper uploader with 10 threads
11:22:18.867 INFO [main] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:128) - Received 10 messages from SQS
11:22:18.870 INFO [pool-2-thread-1] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:165) - Processing message: 6adc1a3e-3016-46cd-bf02-944e50c312ba
11:22:18.871 INFO [pool-2-thread-4] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:165) - Processing message: 7d9b55ae-97bc-4392-9642-ed503590db8a
11:22:18.870 INFO [pool-2-thread-2] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:165) - Processing message: e2a6836f-96f6-4202-b05e-a73f9778b2b1
11:22:18.870 INFO [pool-2-thread-3] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:165) - Processing message: 8b84817e-ef2a-4932-b6e3-08b7ae133b31
11:22:18.871 INFO [pool-2-thread-7] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:165) - Processing message: 6eae000a-4fcc-4948-b6d7-c67882c08d53
11:22:18.871 INFO [main] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:138) - Sleeping for 20 seconds...
11:22:18.871 INFO [pool-2-thread-6] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:165) - Processing message: 924c8d08-6f82-4f18-b0a6-6bfea977a083
11:22:18.871 INFO [pool-2-thread-5] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:165) - Processing message: 6f4e3d97-de73-49c2-8301-ecaefc4d5695
11:22:18.871 INFO [pool-2-thread-10] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:165) - Processing message: 810641b3-03e9-48df-aed5-26cb0f533e28
11:22:18.871 INFO [pool-2-thread-9] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:165) - Processing message: 0c9732ae-4209-42bb-a409-437507b7184c
11:22:18.871 INFO [pool-2-thread-8] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:165) - Processing message: ac6f384c-563c-4ff8-b29f-f930fdf651ce
11:22:39.709 INFO [main] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:128) - Received 10 messages from SQS
11:22:39.711 INFO [main] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:138) - Sleeping for 20 seconds...
11:23:54.529 INFO [main] producer.ProducerConfig (ProducerConfig.java:526) - Idempotence will be disabled because acks is set to 1, not set to 'all'.
11:23:54.532 INFO [main] producer.ProducerConfig (AbstractConfig.java:376) - ProducerConfig values: 
	acks = 1
	batch.size = 1048576
	bootstrap.servers = [**************:9093, ***********:9093, ************:9093]
	buffer.memory = 268435456
	client.dns.lookup = use_all_dns_ips
	client.id = siteHealth_producer_client
	compression.type = none
	connections.max.idle.ms = 540000
	delivery.timeout.ms = 120000
	enable.idempotence = false
	interceptor.classes = []
	key.serializer = class org.apache.kafka.common.serialization.StringSerializer
	linger.ms = 100
	max.block.ms = 600000
	max.in.flight.requests.per.connection = 5
	max.request.size = 5242880
	metadata.max.age.ms = 300000
	metadata.max.idle.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partitioner.adaptive.partitioning.enable = true
	partitioner.availability.timeout.ms = 0
	partitioner.class = null
	partitioner.ignore.keys = false
	receive.buffer.bytes = 32768
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 100000
	retries = 2
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = [hidden]
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.connect.timeout.ms = null
	sasl.login.read.timeout.ms = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.login.retry.backoff.max.ms = 10000
	sasl.login.retry.backoff.ms = 100
	sasl.mechanism = PLAIN
	sasl.oauthbearer.clock.skew.seconds = 30
	sasl.oauthbearer.expected.audience = null
	sasl.oauthbearer.expected.issuer = null
	sasl.oauthbearer.jwks.endpoint.refresh.ms = 3600000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.max.ms = 10000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.ms = 100
	sasl.oauthbearer.jwks.endpoint.url = null
	sasl.oauthbearer.scope.claim.name = scope
	sasl.oauthbearer.sub.claim.name = sub
	sasl.oauthbearer.token.endpoint.url = null
	security.protocol = SASL_PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.3]
	ssl.endpoint.identification.algorithm = 
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.3
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	transaction.timeout.ms = 60000
	transactional.id = null
	value.serializer = class org.apache.kafka.common.serialization.ByteArraySerializer

11:23:54.653 INFO [main] authenticator.AbstractLogin (AbstractLogin.java:61) - Successfully logged in.
11:23:54.671 WARN [main] producer.ProducerConfig (AbstractConfig.java:385) - These configurations '[ssl.endpoint.identification.algorithm]' were supplied but are not used yet.
11:23:54.673 INFO [main] utils.AppInfoParser (AppInfoParser.java:119) - Kafka version: 3.3.1
11:23:54.673 INFO [main] utils.AppInfoParser (AppInfoParser.java:120) - Kafka commitId: e23c59d00e687ff5
11:23:54.673 INFO [main] utils.AppInfoParser (AppInfoParser.java:121) - Kafka startTimeMs: 1751513034672
11:23:54.674 INFO [main] producer.ProducerConfig (ProducerConfig.java:526) - Idempotence will be disabled because acks is set to 1, not set to 'all'.
11:23:54.674 INFO [main] producer.ProducerConfig (AbstractConfig.java:376) - ProducerConfig values: 
	acks = 1
	batch.size = 1048576
	bootstrap.servers = [**************:9093, ***********:9093, ************:9093]
	buffer.memory = 268435456
	client.dns.lookup = use_all_dns_ips
	client.id = siteHealth_producer_client
	compression.type = none
	connections.max.idle.ms = 540000
	delivery.timeout.ms = 120000
	enable.idempotence = false
	interceptor.classes = []
	key.serializer = class org.apache.kafka.common.serialization.StringSerializer
	linger.ms = 100
	max.block.ms = 600000
	max.in.flight.requests.per.connection = 5
	max.request.size = 5242880
	metadata.max.age.ms = 300000
	metadata.max.idle.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partitioner.adaptive.partitioning.enable = true
	partitioner.availability.timeout.ms = 0
	partitioner.class = null
	partitioner.ignore.keys = false
	receive.buffer.bytes = 32768
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 100000
	retries = 2
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = [hidden]
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.connect.timeout.ms = null
	sasl.login.read.timeout.ms = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.login.retry.backoff.max.ms = 10000
	sasl.login.retry.backoff.ms = 100
	sasl.mechanism = PLAIN
	sasl.oauthbearer.clock.skew.seconds = 30
	sasl.oauthbearer.expected.audience = null
	sasl.oauthbearer.expected.issuer = null
	sasl.oauthbearer.jwks.endpoint.refresh.ms = 3600000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.max.ms = 10000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.ms = 100
	sasl.oauthbearer.jwks.endpoint.url = null
	sasl.oauthbearer.scope.claim.name = scope
	sasl.oauthbearer.sub.claim.name = sub
	sasl.oauthbearer.token.endpoint.url = null
	security.protocol = SASL_PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.3]
	ssl.endpoint.identification.algorithm = 
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.3
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	transaction.timeout.ms = 60000
	transactional.id = null
	value.serializer = class org.apache.kafka.common.serialization.StringSerializer

11:23:54.678 WARN [main] producer.ProducerConfig (AbstractConfig.java:385) - These configurations '[ssl.endpoint.identification.algorithm]' were supplied but are not used yet.
11:23:54.678 INFO [main] utils.AppInfoParser (AppInfoParser.java:119) - Kafka version: 3.3.1
11:23:54.678 INFO [main] utils.AppInfoParser (AppInfoParser.java:120) - Kafka commitId: e23c59d00e687ff5
11:23:54.678 INFO [main] utils.AppInfoParser (AppInfoParser.java:121) - Kafka startTimeMs: 1751513034678
11:23:54.678 WARN [main] utils.AppInfoParser (AppInfoParser.java:68) - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.producer:type=app-info,id=siteHealth_producer_client
	at java.management/com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436)
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865)
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960)
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895)
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320)
	at java.management/com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523)
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64)
	at org.apache.kafka.clients.producer.KafkaProducer.<init>(KafkaProducer.java:462)
	at org.apache.kafka.clients.producer.KafkaProducer.<init>(KafkaProducer.java:291)
	at org.apache.kafka.clients.producer.KafkaProducer.<init>(KafkaProducer.java:318)
	at org.apache.kafka.clients.producer.KafkaProducer.<init>(KafkaProducer.java:303)
	at seoclarity.backend.kafka.clientconstructor.producer.siteHealth.SiteHealthProducerConstructor.createStringValueProducer(SiteHealthProducerConstructor.java:46)
	at seoclarity.backend.kafka.clientconstructor.producer.siteHealth.SiteHealthKafkaProducer.<init>(SiteHealthKafkaProducer.java:29)
	at seoclarity.backend.kafka.clientconstructor.producer.siteHealth.SiteHealthKafkaProducer.getInstance(SiteHealthKafkaProducer.java:19)
	at seoclarity.backend.upload.GoogleAIModeScraperUploader.<clinit>(GoogleAIModeScraperUploader.java:59)
11:23:54.685 INFO [main] utils.ScStringEncryptor (ScStringEncryptor.java:22) - Starting stringEncryptor configuration.
11:23:54.690 INFO [main] utils.ScStringEncryptor (ScStringEncryptor.java:33) - Finished stringEncryptor initialization.
11:23:55.304 INFO [main] http.AmazonHttpClient (ApacheHttpClientFactory.java:90) - Configuring Proxy. Proxy Host: 127.0.0.1 Proxy Port: 7890
11:23:57.696 INFO [kafka-producer-network-thread | siteHealth_producer_client] clients.Metadata (Metadata.java:287) - [Producer clientId=siteHealth_producer_client] Cluster ID: y9Z9s3O6SZe4iw1T2rXZ8g
11:23:57.755 INFO [kafka-producer-network-thread | siteHealth_producer_client] clients.Metadata (Metadata.java:287) - [Producer clientId=siteHealth_producer_client] Cluster ID: y9Z9s3O6SZe4iw1T2rXZ8g
11:23:58.755 INFO [main] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:123) - Starting Google AI mode scraper uploader with 10 threads
11:23:59.561 INFO [main] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:128) - Received 10 messages from SQS
11:23:59.563 INFO [pool-2-thread-1] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:165) - Processing message: 69f50cd1-4331-43d9-9129-8a3ddb380a1c
11:23:59.563 INFO [pool-2-thread-6] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:165) - Processing message: 94043376-951b-44d8-9510-42bd732fedcf
11:23:59.563 INFO [pool-2-thread-5] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:165) - Processing message: 3f828332-e36b-4864-a1fb-730756cc6286
11:23:59.563 INFO [pool-2-thread-4] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:165) - Processing message: ef6ad9eb-9092-47ce-8cc0-5f7d9f2523fc
11:23:59.563 INFO [pool-2-thread-2] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:165) - Processing message: 499b011b-2744-475b-8e8e-3333fcf1ebc6
11:23:59.563 INFO [pool-2-thread-3] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:165) - Processing message: 648d01d3-baa9-49c0-ba3a-e7840ffa8bc2
11:23:59.564 INFO [pool-2-thread-10] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:165) - Processing message: a546ac56-fb26-4adb-9510-64b5a06fbef7
11:23:59.564 INFO [pool-2-thread-9] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:165) - Processing message: f4ad3476-16df-45e8-afdf-a7371ddbcc2e
11:23:59.564 INFO [main] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:138) - Sleeping for 20 seconds...
11:23:59.564 INFO [pool-2-thread-8] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:165) - Processing message: 48576d6d-3ab7-454a-bf60-7b0a58c17f8b
11:23:59.563 INFO [pool-2-thread-7] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:165) - Processing message: 9d061658-4309-4ed5-9784-975b46cd2cfd
11:24:20.339 INFO [main] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:128) - Received 10 messages from SQS
11:24:20.340 INFO [main] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:138) - Sleeping for 20 seconds...
11:24:43.400 INFO [pool-2-thread-7] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:311) - Sent to Loggly: {"response" : "ok"}
11:24:43.400 INFO [pool-2-thread-4] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:311) - Sent to Loggly: {"response" : "ok"}
11:24:53.921 INFO [pool-2-thread-10] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:311) - Sent to Loggly: {"response" : "ok"}
11:24:53.921 INFO [pool-2-thread-3] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:311) - Sent to Loggly: {"response" : "ok"}
11:24:53.927 INFO [main] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:128) - Received 10 messages from SQS
11:24:53.928 INFO [main] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:138) - Sleeping for 20 seconds...
