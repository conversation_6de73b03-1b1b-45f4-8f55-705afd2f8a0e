11:13:34.453 INFO [main] support.ClassPathXmlApplicationContext (AbstractApplicationContext.java:578) - Refreshing org.springframework.context.support.ClassPathXmlApplicationContext@71809907: startup date [Mon Jul 14 11:13:34 CST 2025]; root of context hierarchy
11:13:34.478 INFO [main] xml.XmlBeanDefinitionReader (XmlBeanDefinitionReader.java:317) - Loading XML bean definitions from class path resource [applicationContext.xml]
11:13:35.288 INFO [main] xml.XmlBeanDefinitionReader (XmlBeanDefinitionReader.java:317) - Loading XML bean definitions from class path resource [applicationContext-dao.xml]
11:13:35.334 INFO [main] xml.XmlBeanDefinitionReader (XmlBeanDefinitionReader.java:317) - Loading XML bean definitions from class path resource [applicationContext-email.xml]
11:13:35.489 INFO [main] support.PropertySourcesPlaceholderConfigurer (PropertiesLoaderSupport.java:172) - Loading properties file from class path resource [jdbc.properties]
11:13:35.502 INFO [main] support.PropertySourcesPlaceholderConfigurer (PropertiesLoaderSupport.java:172) - Loading properties file from class path resource [mailjet.properties]
11:13:35.504 INFO [main] support.PropertySourcesPlaceholderConfigurer (PropertiesLoaderSupport.java:172) - Loading properties file from class path resource [serverInfo.properties]
11:13:35.505 INFO [main] support.PropertySourcesPlaceholderConfigurer (PropertiesLoaderSupport.java:172) - Loading properties file from class path resource [kafka.properties]
11:13:35.509 INFO [main] annotation.AutowiredAnnotationBeanPostProcessor (AutowiredAnnotationBeanPostProcessor.java:153) - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
11:13:35.603 WARN [main] utils.GenericsUtils (GenericsUtils.java:54) - MonthlyRankingGRDao not set the actual class on superclass generic parameter
11:13:35.729 WARN [main] utils.GenericsUtils (GenericsUtils.java:54) - ClResearchgridManagedUrlDao not set the actual class on superclass generic parameter
11:13:35.730 WARN [main] utils.GenericsUtils (GenericsUtils.java:42) - RGRankingDetail202107DAO3's superclass not ParameterizedType
11:13:35.751 WARN [main] utils.GenericsUtils (GenericsUtils.java:42) - RGRankingDetail202107DAO1's superclass not ParameterizedType
11:13:35.833 WARN [main] utils.GenericsUtils (GenericsUtils.java:42) - RGRankingDetail202107DAO4's superclass not ParameterizedType
11:13:35.879 WARN [main] utils.GenericsUtils (GenericsUtils.java:42) - RGRankingDetail202107DAO2's superclass not ParameterizedType
11:13:36.060 INFO [main] support.ClassPathXmlApplicationContext (AbstractApplicationContext.java:578) - Refreshing org.springframework.context.support.ClassPathXmlApplicationContext@bb095: startup date [Mon Jul 14 11:13:36 CST 2025]; root of context hierarchy
11:13:36.061 INFO [main] xml.XmlBeanDefinitionReader (XmlBeanDefinitionReader.java:317) - Loading XML bean definitions from class path resource [applicationContext.xml]
11:13:36.213 INFO [main] xml.XmlBeanDefinitionReader (XmlBeanDefinitionReader.java:317) - Loading XML bean definitions from class path resource [applicationContext-dao.xml]
11:13:36.231 INFO [main] xml.XmlBeanDefinitionReader (XmlBeanDefinitionReader.java:317) - Loading XML bean definitions from class path resource [applicationContext-email.xml]
11:13:36.274 INFO [main] support.PropertySourcesPlaceholderConfigurer (PropertiesLoaderSupport.java:172) - Loading properties file from class path resource [jdbc.properties]
11:13:36.279 INFO [main] support.PropertySourcesPlaceholderConfigurer (PropertiesLoaderSupport.java:172) - Loading properties file from class path resource [mailjet.properties]
11:13:36.280 INFO [main] support.PropertySourcesPlaceholderConfigurer (PropertiesLoaderSupport.java:172) - Loading properties file from class path resource [serverInfo.properties]
11:13:36.281 INFO [main] support.PropertySourcesPlaceholderConfigurer (PropertiesLoaderSupport.java:172) - Loading properties file from class path resource [kafka.properties]
11:13:36.283 INFO [main] annotation.AutowiredAnnotationBeanPostProcessor (AutowiredAnnotationBeanPostProcessor.java:153) - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
11:13:36.290 WARN [main] utils.GenericsUtils (GenericsUtils.java:54) - MonthlyRankingGRDao not set the actual class on superclass generic parameter
11:13:36.313 WARN [main] utils.GenericsUtils (GenericsUtils.java:54) - ClResearchgridManagedUrlDao not set the actual class on superclass generic parameter
11:13:36.313 WARN [main] utils.GenericsUtils (GenericsUtils.java:42) - RGRankingDetail202107DAO3's superclass not ParameterizedType
11:13:36.317 WARN [main] utils.GenericsUtils (GenericsUtils.java:42) - RGRankingDetail202107DAO1's superclass not ParameterizedType
11:13:36.331 WARN [main] utils.GenericsUtils (GenericsUtils.java:42) - RGRankingDetail202107DAO4's superclass not ParameterizedType
11:13:36.340 WARN [main] utils.GenericsUtils (GenericsUtils.java:42) - RGRankingDetail202107DAO2's superclass not ParameterizedType
11:13:36.443 INFO [main] support.ClassPathXmlApplicationContext (AbstractApplicationContext.java:578) - Refreshing org.springframework.context.support.ClassPathXmlApplicationContext@4d178d55: startup date [Mon Jul 14 11:13:36 CST 2025]; root of context hierarchy
11:13:36.443 INFO [main] xml.XmlBeanDefinitionReader (XmlBeanDefinitionReader.java:317) - Loading XML bean definitions from class path resource [applicationContext.xml]
11:13:36.594 INFO [main] xml.XmlBeanDefinitionReader (XmlBeanDefinitionReader.java:317) - Loading XML bean definitions from class path resource [applicationContext-dao.xml]
11:13:36.606 INFO [main] xml.XmlBeanDefinitionReader (XmlBeanDefinitionReader.java:317) - Loading XML bean definitions from class path resource [applicationContext-email.xml]
11:13:36.653 INFO [main] support.PropertySourcesPlaceholderConfigurer (PropertiesLoaderSupport.java:172) - Loading properties file from class path resource [jdbc.properties]
11:13:36.658 INFO [main] support.PropertySourcesPlaceholderConfigurer (PropertiesLoaderSupport.java:172) - Loading properties file from class path resource [mailjet.properties]
11:13:36.660 INFO [main] support.PropertySourcesPlaceholderConfigurer (PropertiesLoaderSupport.java:172) - Loading properties file from class path resource [serverInfo.properties]
11:13:36.663 INFO [main] support.PropertySourcesPlaceholderConfigurer (PropertiesLoaderSupport.java:172) - Loading properties file from class path resource [kafka.properties]
11:13:36.666 INFO [main] annotation.AutowiredAnnotationBeanPostProcessor (AutowiredAnnotationBeanPostProcessor.java:153) - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
11:13:36.673 WARN [main] utils.GenericsUtils (GenericsUtils.java:54) - MonthlyRankingGRDao not set the actual class on superclass generic parameter
11:13:36.703 WARN [main] utils.GenericsUtils (GenericsUtils.java:54) - ClResearchgridManagedUrlDao not set the actual class on superclass generic parameter
11:13:36.704 WARN [main] utils.GenericsUtils (GenericsUtils.java:42) - RGRankingDetail202107DAO3's superclass not ParameterizedType
11:13:36.708 WARN [main] utils.GenericsUtils (GenericsUtils.java:42) - RGRankingDetail202107DAO1's superclass not ParameterizedType
11:13:36.722 WARN [main] utils.GenericsUtils (GenericsUtils.java:42) - RGRankingDetail202107DAO4's superclass not ParameterizedType
11:13:36.743 WARN [main] utils.GenericsUtils (GenericsUtils.java:42) - RGRankingDetail202107DAO2's superclass not ParameterizedType
11:13:36.794 INFO [main] support.ClassPathXmlApplicationContext (AbstractApplicationContext.java:578) - Refreshing org.springframework.context.support.ClassPathXmlApplicationContext@10bd9df0: startup date [Mon Jul 14 11:13:36 CST 2025]; root of context hierarchy
11:13:36.794 INFO [main] xml.XmlBeanDefinitionReader (XmlBeanDefinitionReader.java:317) - Loading XML bean definitions from class path resource [applicationContext.xml]
11:13:36.941 INFO [main] xml.XmlBeanDefinitionReader (XmlBeanDefinitionReader.java:317) - Loading XML bean definitions from class path resource [applicationContext-dao.xml]
11:13:36.955 INFO [main] xml.XmlBeanDefinitionReader (XmlBeanDefinitionReader.java:317) - Loading XML bean definitions from class path resource [applicationContext-email.xml]
11:13:36.996 INFO [main] support.PropertySourcesPlaceholderConfigurer (PropertiesLoaderSupport.java:172) - Loading properties file from class path resource [jdbc.properties]
11:13:36.999 INFO [main] support.PropertySourcesPlaceholderConfigurer (PropertiesLoaderSupport.java:172) - Loading properties file from class path resource [mailjet.properties]
11:13:37.000 INFO [main] support.PropertySourcesPlaceholderConfigurer (PropertiesLoaderSupport.java:172) - Loading properties file from class path resource [serverInfo.properties]
11:13:37.000 INFO [main] support.PropertySourcesPlaceholderConfigurer (PropertiesLoaderSupport.java:172) - Loading properties file from class path resource [kafka.properties]
11:13:37.002 INFO [main] annotation.AutowiredAnnotationBeanPostProcessor (AutowiredAnnotationBeanPostProcessor.java:153) - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
11:13:37.008 WARN [main] utils.GenericsUtils (GenericsUtils.java:54) - MonthlyRankingGRDao not set the actual class on superclass generic parameter
11:13:37.028 WARN [main] utils.GenericsUtils (GenericsUtils.java:54) - ClResearchgridManagedUrlDao not set the actual class on superclass generic parameter
11:13:37.028 WARN [main] utils.GenericsUtils (GenericsUtils.java:42) - RGRankingDetail202107DAO3's superclass not ParameterizedType
11:13:37.031 WARN [main] utils.GenericsUtils (GenericsUtils.java:42) - RGRankingDetail202107DAO1's superclass not ParameterizedType
11:13:37.040 WARN [main] utils.GenericsUtils (GenericsUtils.java:42) - RGRankingDetail202107DAO4's superclass not ParameterizedType
11:13:37.046 WARN [main] utils.GenericsUtils (GenericsUtils.java:42) - RGRankingDetail202107DAO2's superclass not ParameterizedType
11:13:37.064 INFO [main] support.ClassPathXmlApplicationContext (AbstractApplicationContext.java:578) - Refreshing org.springframework.context.support.ClassPathXmlApplicationContext@2de50ee4: startup date [Mon Jul 14 11:13:37 CST 2025]; root of context hierarchy
11:13:37.065 INFO [main] xml.XmlBeanDefinitionReader (XmlBeanDefinitionReader.java:317) - Loading XML bean definitions from class path resource [applicationContext.xml]
11:13:37.193 INFO [main] xml.XmlBeanDefinitionReader (XmlBeanDefinitionReader.java:317) - Loading XML bean definitions from class path resource [applicationContext-dao.xml]
11:13:37.205 INFO [main] xml.XmlBeanDefinitionReader (XmlBeanDefinitionReader.java:317) - Loading XML bean definitions from class path resource [applicationContext-email.xml]
11:13:37.253 INFO [main] support.PropertySourcesPlaceholderConfigurer (PropertiesLoaderSupport.java:172) - Loading properties file from class path resource [jdbc.properties]
11:13:37.255 INFO [main] support.PropertySourcesPlaceholderConfigurer (PropertiesLoaderSupport.java:172) - Loading properties file from class path resource [mailjet.properties]
11:13:37.256 INFO [main] support.PropertySourcesPlaceholderConfigurer (PropertiesLoaderSupport.java:172) - Loading properties file from class path resource [serverInfo.properties]
11:13:37.256 INFO [main] support.PropertySourcesPlaceholderConfigurer (PropertiesLoaderSupport.java:172) - Loading properties file from class path resource [kafka.properties]
11:13:37.258 INFO [main] annotation.AutowiredAnnotationBeanPostProcessor (AutowiredAnnotationBeanPostProcessor.java:153) - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
11:13:37.265 WARN [main] utils.GenericsUtils (GenericsUtils.java:54) - MonthlyRankingGRDao not set the actual class on superclass generic parameter
11:13:37.300 WARN [main] utils.GenericsUtils (GenericsUtils.java:54) - ClResearchgridManagedUrlDao not set the actual class on superclass generic parameter
11:13:37.300 WARN [main] utils.GenericsUtils (GenericsUtils.java:42) - RGRankingDetail202107DAO3's superclass not ParameterizedType
11:13:37.303 WARN [main] utils.GenericsUtils (GenericsUtils.java:42) - RGRankingDetail202107DAO1's superclass not ParameterizedType
11:13:37.315 WARN [main] utils.GenericsUtils (GenericsUtils.java:42) - RGRankingDetail202107DAO4's superclass not ParameterizedType
11:13:37.322 WARN [main] utils.GenericsUtils (GenericsUtils.java:42) - RGRankingDetail202107DAO2's superclass not ParameterizedType
11:13:42.219 ERROR[main] pool.DruidDataSource (DruidDataSource.java:943) - init datasource error, url: ************************************************************************************************************************************************
java.sql.SQLNonTransientConnectionException: Could not create connection to database server. Attempted reconnect 3 times. Giving up.
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:111)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:98)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:90)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:64)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:74)
	at com.mysql.cj.jdbc.ConnectionImpl.connectWithRetries(ConnectionImpl.java:895)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:820)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:446)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:239)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:188)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:118)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:232)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:112)
	at com.alibaba.druid.filter.FilterAdapter.connection_connect(FilterAdapter.java:764)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:112)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1694)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1789)
	at com.alibaba.druid.pool.DruidDataSource.init(DruidDataSource.java:939)
	at com.alibaba.druid.pool.DruidDataSource.getConnection(DruidDataSource.java:1463)
	at com.alibaba.druid.pool.DruidDataSource.getConnection(DruidDataSource.java:1459)
	at com.alibaba.druid.pool.DruidDataSource.getConnection(DruidDataSource.java:83)
	at org.springframework.jdbc.datasource.DataSourceUtils.doGetConnection(DataSourceUtils.java:111)
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:77)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:615)
	at org.springframework.jdbc.core.JdbcTemplate.query(JdbcTemplate.java:680)
	at org.springframework.jdbc.core.JdbcTemplate.query(JdbcTemplate.java:712)
	at org.springframework.jdbc.core.JdbcTemplate.query(JdbcTemplate.java:722)
	at org.springframework.jdbc.core.JdbcTemplate.query(JdbcTemplate.java:777)
	at seoclarity.backend.dao.BaseJdbcSupport.findBySql(BaseJdbcSupport.java:115)
	at seoclarity.backend.dao.actonia.OwnDomainEntityDAO.queryForAll(OwnDomainEntityDAO.java:170)
	at seoclarity.backend.dao.actonia.OwnDomainEntityDAO.getOwnDomainEntityMap(OwnDomainEntityDAO.java:174)
	at seoclarity.backend.service.CommonDataService.<clinit>(CommonDataService.java:78)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:500)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:481)
	at org.springframework.beans.BeanUtils.instantiateClass(BeanUtils.java:147)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:89)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateBean(AbstractAutowireCapableBeanFactory.java:1102)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1054)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:510)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:482)
	at org.springframework.beans.factory.support.AbstractBeanFactory$1.getObject(AbstractBeanFactory.java:306)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:230)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:302)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:197)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:778)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:839)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:538)
	at org.springframework.context.support.ClassPathXmlApplicationContext.<init>(ClassPathXmlApplicationContext.java:139)
	at org.springframework.context.support.ClassPathXmlApplicationContext.<init>(ClassPathXmlApplicationContext.java:93)
	at seoclarity.backend.utils.SpringBeanFactory.getApplicationContext(SpringBeanFactory.java:24)
	at seoclarity.backend.utils.SpringBeanFactory.getBean(SpringBeanFactory.java:31)
	at seoclarity.backend.service.ScKeywordRankManager.<clinit>(ScKeywordRankManager.java:109)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:500)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:481)
	at org.springframework.beans.BeanUtils.instantiateClass(BeanUtils.java:147)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:89)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateBean(AbstractAutowireCapableBeanFactory.java:1102)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1054)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:510)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:482)
	at org.springframework.beans.factory.support.AbstractBeanFactory$1.getObject(AbstractBeanFactory.java:306)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:230)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:302)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:197)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:778)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:839)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:538)
	at org.springframework.context.support.ClassPathXmlApplicationContext.<init>(ClassPathXmlApplicationContext.java:139)
	at org.springframework.context.support.ClassPathXmlApplicationContext.<init>(ClassPathXmlApplicationContext.java:93)
	at seoclarity.backend.utils.SpringBeanFactory.getApplicationContext(SpringBeanFactory.java:24)
	at seoclarity.backend.utils.SpringBeanFactory.getBean(SpringBeanFactory.java:31)
	at seoclarity.backend.service.GeoService.<clinit>(GeoService.java:41)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:500)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:481)
	at org.springframework.beans.BeanUtils.instantiateClass(BeanUtils.java:147)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:89)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateBean(AbstractAutowireCapableBeanFactory.java:1102)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1054)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:510)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:482)
	at org.springframework.beans.factory.support.AbstractBeanFactory$1.getObject(AbstractBeanFactory.java:306)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:230)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:302)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:197)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:778)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:839)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:538)
	at org.springframework.context.support.ClassPathXmlApplicationContext.<init>(ClassPathXmlApplicationContext.java:139)
	at org.springframework.context.support.ClassPathXmlApplicationContext.<init>(ClassPathXmlApplicationContext.java:93)
	at seoclarity.backend.utils.SpringBeanFactory.getApplicationContext(SpringBeanFactory.java:24)
	at seoclarity.backend.utils.SpringBeanFactory.getBean(SpringBeanFactory.java:31)
	at seoclarity.backend.summary.AIActionByOpenAI.<init>(AIActionByOpenAI.java:143)
	at seoclarity.backend.summary.AIActionByOpenAI.main(AIActionByOpenAI.java:175)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:500)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:481)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:62)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:150)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:166)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:582)
	at com.mysql.cj.protocol.a.NativeProtocol.readServerCapabilities(NativeProtocol.java:537)
	at com.mysql.cj.protocol.a.NativeProtocol.beforeHandshake(NativeProtocol.java:424)
	at com.mysql.cj.protocol.a.NativeProtocol.connect(NativeProtocol.java:1428)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:134)
	at com.mysql.cj.jdbc.ConnectionImpl.connectWithRetries(ConnectionImpl.java:839)
	... 96 more
Caused by: java.io.EOFException: Can not read response from server. Expected to read 4 bytes, read 0 bytes before connection was unexpectedly lost.
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:67)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:576)
	... 101 more
11:13:42.241 ERROR[main] pool.DruidDataSource (DruidDataSource.java:985) - {dataSource-1} init error
java.sql.SQLNonTransientConnectionException: Could not create connection to database server. Attempted reconnect 3 times. Giving up.
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:111)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:98)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:90)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:64)
	at com.mysql.cj.jdbc.exceptions.SQLError.createSQLException(SQLError.java:74)
	at com.mysql.cj.jdbc.ConnectionImpl.connectWithRetries(ConnectionImpl.java:895)
	at com.mysql.cj.jdbc.ConnectionImpl.createNewIO(ConnectionImpl.java:820)
	at com.mysql.cj.jdbc.ConnectionImpl.<init>(ConnectionImpl.java:446)
	at com.mysql.cj.jdbc.ConnectionImpl.getInstance(ConnectionImpl.java:239)
	at com.mysql.cj.jdbc.NonRegisteringDriver.connect(NonRegisteringDriver.java:188)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:118)
	at com.alibaba.druid.filter.stat.StatFilter.connection_connect(StatFilter.java:232)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:112)
	at com.alibaba.druid.filter.FilterAdapter.connection_connect(FilterAdapter.java:764)
	at com.alibaba.druid.filter.FilterChainImpl.connection_connect(FilterChainImpl.java:112)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1694)
	at com.alibaba.druid.pool.DruidAbstractDataSource.createPhysicalConnection(DruidAbstractDataSource.java:1789)
	at com.alibaba.druid.pool.DruidDataSource.init(DruidDataSource.java:939)
	at com.alibaba.druid.pool.DruidDataSource.getConnection(DruidDataSource.java:1463)
	at com.alibaba.druid.pool.DruidDataSource.getConnection(DruidDataSource.java:1459)
	at com.alibaba.druid.pool.DruidDataSource.getConnection(DruidDataSource.java:83)
	at org.springframework.jdbc.datasource.DataSourceUtils.doGetConnection(DataSourceUtils.java:111)
	at org.springframework.jdbc.datasource.DataSourceUtils.getConnection(DataSourceUtils.java:77)
	at org.springframework.jdbc.core.JdbcTemplate.execute(JdbcTemplate.java:615)
	at org.springframework.jdbc.core.JdbcTemplate.query(JdbcTemplate.java:680)
	at org.springframework.jdbc.core.JdbcTemplate.query(JdbcTemplate.java:712)
	at org.springframework.jdbc.core.JdbcTemplate.query(JdbcTemplate.java:722)
	at org.springframework.jdbc.core.JdbcTemplate.query(JdbcTemplate.java:777)
	at seoclarity.backend.dao.BaseJdbcSupport.findBySql(BaseJdbcSupport.java:115)
	at seoclarity.backend.dao.actonia.OwnDomainEntityDAO.queryForAll(OwnDomainEntityDAO.java:170)
	at seoclarity.backend.dao.actonia.OwnDomainEntityDAO.getOwnDomainEntityMap(OwnDomainEntityDAO.java:174)
	at seoclarity.backend.service.CommonDataService.<clinit>(CommonDataService.java:78)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:500)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:481)
	at org.springframework.beans.BeanUtils.instantiateClass(BeanUtils.java:147)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:89)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateBean(AbstractAutowireCapableBeanFactory.java:1102)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1054)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:510)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:482)
	at org.springframework.beans.factory.support.AbstractBeanFactory$1.getObject(AbstractBeanFactory.java:306)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:230)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:302)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:197)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:778)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:839)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:538)
	at org.springframework.context.support.ClassPathXmlApplicationContext.<init>(ClassPathXmlApplicationContext.java:139)
	at org.springframework.context.support.ClassPathXmlApplicationContext.<init>(ClassPathXmlApplicationContext.java:93)
	at seoclarity.backend.utils.SpringBeanFactory.getApplicationContext(SpringBeanFactory.java:24)
	at seoclarity.backend.utils.SpringBeanFactory.getBean(SpringBeanFactory.java:31)
	at seoclarity.backend.service.ScKeywordRankManager.<clinit>(ScKeywordRankManager.java:109)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:500)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:481)
	at org.springframework.beans.BeanUtils.instantiateClass(BeanUtils.java:147)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:89)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateBean(AbstractAutowireCapableBeanFactory.java:1102)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1054)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:510)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:482)
	at org.springframework.beans.factory.support.AbstractBeanFactory$1.getObject(AbstractBeanFactory.java:306)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:230)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:302)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:197)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:778)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:839)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:538)
	at org.springframework.context.support.ClassPathXmlApplicationContext.<init>(ClassPathXmlApplicationContext.java:139)
	at org.springframework.context.support.ClassPathXmlApplicationContext.<init>(ClassPathXmlApplicationContext.java:93)
	at seoclarity.backend.utils.SpringBeanFactory.getApplicationContext(SpringBeanFactory.java:24)
	at seoclarity.backend.utils.SpringBeanFactory.getBean(SpringBeanFactory.java:31)
	at seoclarity.backend.service.GeoService.<clinit>(GeoService.java:41)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:500)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:481)
	at org.springframework.beans.BeanUtils.instantiateClass(BeanUtils.java:147)
	at org.springframework.beans.factory.support.SimpleInstantiationStrategy.instantiate(SimpleInstantiationStrategy.java:89)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.instantiateBean(AbstractAutowireCapableBeanFactory.java:1102)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBeanInstance(AbstractAutowireCapableBeanFactory.java:1054)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.doCreateBean(AbstractAutowireCapableBeanFactory.java:510)
	at org.springframework.beans.factory.support.AbstractAutowireCapableBeanFactory.createBean(AbstractAutowireCapableBeanFactory.java:482)
	at org.springframework.beans.factory.support.AbstractBeanFactory$1.getObject(AbstractBeanFactory.java:306)
	at org.springframework.beans.factory.support.DefaultSingletonBeanRegistry.getSingleton(DefaultSingletonBeanRegistry.java:230)
	at org.springframework.beans.factory.support.AbstractBeanFactory.doGetBean(AbstractBeanFactory.java:302)
	at org.springframework.beans.factory.support.AbstractBeanFactory.getBean(AbstractBeanFactory.java:197)
	at org.springframework.beans.factory.support.DefaultListableBeanFactory.preInstantiateSingletons(DefaultListableBeanFactory.java:778)
	at org.springframework.context.support.AbstractApplicationContext.finishBeanFactoryInitialization(AbstractApplicationContext.java:839)
	at org.springframework.context.support.AbstractApplicationContext.refresh(AbstractApplicationContext.java:538)
	at org.springframework.context.support.ClassPathXmlApplicationContext.<init>(ClassPathXmlApplicationContext.java:139)
	at org.springframework.context.support.ClassPathXmlApplicationContext.<init>(ClassPathXmlApplicationContext.java:93)
	at seoclarity.backend.utils.SpringBeanFactory.getApplicationContext(SpringBeanFactory.java:24)
	at seoclarity.backend.utils.SpringBeanFactory.getBean(SpringBeanFactory.java:31)
	at seoclarity.backend.summary.AIActionByOpenAI.<init>(AIActionByOpenAI.java:143)
	at seoclarity.backend.summary.AIActionByOpenAI.main(AIActionByOpenAI.java:175)
Caused by: com.mysql.cj.exceptions.CJCommunicationsException: Communications link failure

The last packet sent successfully to the server was 0 milliseconds ago. The driver has not received any packets from the server.
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance0(Native Method)
	at java.base/jdk.internal.reflect.NativeConstructorAccessorImpl.newInstance(NativeConstructorAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingConstructorAccessorImpl.newInstance(DelegatingConstructorAccessorImpl.java:45)
	at java.base/java.lang.reflect.Constructor.newInstanceWithCaller(Constructor.java:500)
	at java.base/java.lang.reflect.Constructor.newInstance(Constructor.java:481)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:62)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:105)
	at com.mysql.cj.exceptions.ExceptionFactory.createException(ExceptionFactory.java:150)
	at com.mysql.cj.exceptions.ExceptionFactory.createCommunicationsException(ExceptionFactory.java:166)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:582)
	at com.mysql.cj.protocol.a.NativeProtocol.readServerCapabilities(NativeProtocol.java:537)
	at com.mysql.cj.protocol.a.NativeProtocol.beforeHandshake(NativeProtocol.java:424)
	at com.mysql.cj.protocol.a.NativeProtocol.connect(NativeProtocol.java:1428)
	at com.mysql.cj.NativeSession.connect(NativeSession.java:134)
	at com.mysql.cj.jdbc.ConnectionImpl.connectWithRetries(ConnectionImpl.java:839)
	... 96 more
Caused by: java.io.EOFException: Can not read response from server. Expected to read 4 bytes, read 0 bytes before connection was unexpectedly lost.
	at com.mysql.cj.protocol.FullReadInputStream.readFully(FullReadInputStream.java:67)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeaderLocal(SimplePacketReader.java:81)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:63)
	at com.mysql.cj.protocol.a.SimplePacketReader.readHeader(SimplePacketReader.java:45)
	at com.mysql.cj.protocol.a.NativeProtocol.readMessage(NativeProtocol.java:576)
	... 101 more
11:13:42.242 INFO [main] pool.DruidDataSource (DruidDataSource.java:1010) - {dataSource-1} inited
11:13:42.245 WARN [main] support.ClassPathXmlApplicationContext (AbstractApplicationContext.java:546) - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'commonDataService' defined in file [/Users/<USER>/projects/dking/clarity-backend-scripts/target/classes/seoclarity/backend/service/CommonDataService.class]: Instantiation of bean failed; nested exception is java.lang.ExceptionInInitializerError
11:13:42.245 INFO [Druid-ConnectionPool-Log-752148842] pool.DruidDataSourceStatLoggerImpl (DruidDataSourceStatLoggerImpl.java:80) - {"url":"************************************************************************************************************************************************","dbType":"mysql","name":"DataSource-752148842","activeCount":0,"poolingCount":0,"connectCount":0,"closeCount":0,"physicalConnectErrorCount":1}
11:13:42.247 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.247 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.248 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.250 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.250 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.250 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.251 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.251 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.251 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.251 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.251 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.251 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.251 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.252 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.252 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.252 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.252 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.252 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.252 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.253 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.253 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.253 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.253 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.253 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.253 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.253 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.253 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.254 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.254 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.254 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.254 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.254 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.254 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.254 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.255 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.255 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.255 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.255 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.255 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.255 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.255 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.255 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.255 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.256 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.256 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.256 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.256 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.256 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.256 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.256 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.256 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.256 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.257 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.257 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.257 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.257 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.257 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.257 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.257 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.257 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.257 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.257 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.258 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.258 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.258 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.258 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.258 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.259 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.259 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.259 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.259 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.259 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.259 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.259 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.259 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.259 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.259 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.259 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.260 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.260 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.260 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.260 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.260 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.260 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.260 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.260 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.260 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.261 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.261 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.261 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.261 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.261 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.261 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.261 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.261 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.261 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.261 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.261 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.261 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.262 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.262 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.262 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.262 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.262 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.262 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.262 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.262 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.262 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.262 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.262 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.263 WARN [main] support.ClassPathXmlApplicationContext (AbstractApplicationContext.java:546) - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'scKeywordRankManager' defined in file [/Users/<USER>/projects/dking/clarity-backend-scripts/target/classes/seoclarity/backend/service/ScKeywordRankManager.class]: Instantiation of bean failed; nested exception is java.lang.ExceptionInInitializerError
11:13:42.264 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.264 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.264 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.265 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.266 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.266 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.266 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.266 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.267 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.267 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.267 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.267 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.267 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.267 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.267 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.267 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.267 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.267 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.267 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.268 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.268 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.268 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.268 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.268 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.268 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.268 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.268 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.268 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.268 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.268 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.268 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.269 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.269 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.269 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.269 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.269 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.269 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.269 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.269 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.269 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.269 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.269 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.269 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.269 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.270 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.270 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.270 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.270 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.270 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.270 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.270 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.270 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.270 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.271 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.271 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.271 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.271 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.271 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.271 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.271 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.271 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.271 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.271 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.271 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.271 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.271 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.272 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.272 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.272 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.272 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.272 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.272 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.272 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.272 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.272 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.272 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.272 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.272 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.272 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.272 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.272 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.272 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.272 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.273 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.273 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.273 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.273 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.273 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.273 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.273 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.273 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.273 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.273 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.273 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.273 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.273 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.273 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.273 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.273 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.274 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.274 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.274 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.274 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.274 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.274 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.274 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.274 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.274 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.274 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.274 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.274 WARN [main] support.ClassPathXmlApplicationContext (AbstractApplicationContext.java:546) - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.beans.factory.BeanCreationException: Error creating bean with name 'geoService' defined in file [/Users/<USER>/projects/dking/clarity-backend-scripts/target/classes/seoclarity/backend/service/GeoService.class]: Instantiation of bean failed; nested exception is java.lang.ExceptionInInitializerError
11:13:42.275 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.275 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.276 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.276 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.277 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.277 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.277 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.277 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.277 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.277 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.277 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.277 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.277 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.277 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.277 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.277 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.277 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.278 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.278 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.278 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.278 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.278 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.278 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.278 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.278 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.278 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.278 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.278 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.278 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.278 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.278 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.278 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.278 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.278 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.279 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.279 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.279 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.279 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.279 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.279 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.279 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.279 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.279 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.279 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.279 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.279 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.279 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.279 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.279 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.279 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.279 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.279 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.279 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.280 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.280 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.280 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.280 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.280 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.280 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.280 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.280 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.280 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.280 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.280 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.280 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.280 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.280 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.280 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.280 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.280 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.280 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.280 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.280 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.280 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.280 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.281 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.281 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.281 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.281 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.281 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.281 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.281 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.281 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.281 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.281 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.281 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.281 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.281 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.281 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.281 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.281 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.281 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.281 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.281 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.281 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.281 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.281 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.281 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.282 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.282 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.282 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.282 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.282 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.282 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.282 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.282 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.282 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.282 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.282 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:13:42.282 INFO [main] pool.DruidDataSource (DruidDataSource.java:2170) - {dataSource-0} closing ...
11:14:45.835 INFO [main] utils.ScStringEncryptor (ScStringEncryptor.java:22) - Starting stringEncryptor configuration.
11:14:45.845 INFO [main] utils.ScStringEncryptor (ScStringEncryptor.java:33) - Finished stringEncryptor initialization.
