2025-07-01 15:14:20.576 INFO  [main] producer.ProducerConfig (ProducerConfig.java:526) - Idempotence will be disabled because acks is set to 1, not set to 'all'.
2025-07-01 15:14:20.579 INFO  [main] producer.ProducerConfig (AbstractConfig.java:376) - ProducerConfig values: 
	acks = 1
	batch.size = 1048576
	bootstrap.servers = [**************:9093, ***********:9093, ************:9093]
	buffer.memory = 268435456
	client.dns.lookup = use_all_dns_ips
	client.id = siteHealth_producer_client
	compression.type = none
	connections.max.idle.ms = 540000
	delivery.timeout.ms = 120000
	enable.idempotence = false
	interceptor.classes = []
	key.serializer = class org.apache.kafka.common.serialization.StringSerializer
	linger.ms = 100
	max.block.ms = 600000
	max.in.flight.requests.per.connection = 5
	max.request.size = 5242880
	metadata.max.age.ms = 300000
	metadata.max.idle.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partitioner.adaptive.partitioning.enable = true
	partitioner.availability.timeout.ms = 0
	partitioner.class = null
	partitioner.ignore.keys = false
	receive.buffer.bytes = 32768
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 100000
	retries = 2
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = [hidden]
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.connect.timeout.ms = null
	sasl.login.read.timeout.ms = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.login.retry.backoff.max.ms = 10000
	sasl.login.retry.backoff.ms = 100
	sasl.mechanism = PLAIN
	sasl.oauthbearer.clock.skew.seconds = 30
	sasl.oauthbearer.expected.audience = null
	sasl.oauthbearer.expected.issuer = null
	sasl.oauthbearer.jwks.endpoint.refresh.ms = 3600000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.max.ms = 10000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.ms = 100
	sasl.oauthbearer.jwks.endpoint.url = null
	sasl.oauthbearer.scope.claim.name = scope
	sasl.oauthbearer.sub.claim.name = sub
	sasl.oauthbearer.token.endpoint.url = null
	security.protocol = SASL_PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.3]
	ssl.endpoint.identification.algorithm = 
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.3
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	transaction.timeout.ms = 60000
	transactional.id = null
	value.serializer = class org.apache.kafka.common.serialization.ByteArraySerializer

2025-07-01 15:14:20.726 INFO  [main] authenticator.AbstractLogin (AbstractLogin.java:61) - Successfully logged in.
2025-07-01 15:14:20.747 WARN  [main] producer.ProducerConfig (AbstractConfig.java:385) - These configurations '[ssl.endpoint.identification.algorithm]' were supplied but are not used yet.
2025-07-01 15:14:20.748 INFO  [main] utils.AppInfoParser (AppInfoParser.java:119) - Kafka version: 3.3.1
2025-07-01 15:14:20.749 INFO  [main] utils.AppInfoParser (AppInfoParser.java:120) - Kafka commitId: e23c59d00e687ff5
2025-07-01 15:14:20.749 INFO  [main] utils.AppInfoParser (AppInfoParser.java:121) - Kafka startTimeMs: 1751354060748
2025-07-01 15:14:20.751 INFO  [main] producer.ProducerConfig (ProducerConfig.java:526) - Idempotence will be disabled because acks is set to 1, not set to 'all'.
2025-07-01 15:14:20.751 INFO  [main] producer.ProducerConfig (AbstractConfig.java:376) - ProducerConfig values: 
	acks = 1
	batch.size = 1048576
	bootstrap.servers = [**************:9093, ***********:9093, ************:9093]
	buffer.memory = 268435456
	client.dns.lookup = use_all_dns_ips
	client.id = siteHealth_producer_client
	compression.type = none
	connections.max.idle.ms = 540000
	delivery.timeout.ms = 120000
	enable.idempotence = false
	interceptor.classes = []
	key.serializer = class org.apache.kafka.common.serialization.StringSerializer
	linger.ms = 100
	max.block.ms = 600000
	max.in.flight.requests.per.connection = 5
	max.request.size = 5242880
	metadata.max.age.ms = 300000
	metadata.max.idle.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partitioner.adaptive.partitioning.enable = true
	partitioner.availability.timeout.ms = 0
	partitioner.class = null
	partitioner.ignore.keys = false
	receive.buffer.bytes = 32768
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 100000
	retries = 2
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = [hidden]
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.connect.timeout.ms = null
	sasl.login.read.timeout.ms = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.login.retry.backoff.max.ms = 10000
	sasl.login.retry.backoff.ms = 100
	sasl.mechanism = PLAIN
	sasl.oauthbearer.clock.skew.seconds = 30
	sasl.oauthbearer.expected.audience = null
	sasl.oauthbearer.expected.issuer = null
	sasl.oauthbearer.jwks.endpoint.refresh.ms = 3600000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.max.ms = 10000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.ms = 100
	sasl.oauthbearer.jwks.endpoint.url = null
	sasl.oauthbearer.scope.claim.name = scope
	sasl.oauthbearer.sub.claim.name = sub
	sasl.oauthbearer.token.endpoint.url = null
	security.protocol = SASL_PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.3]
	ssl.endpoint.identification.algorithm = 
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.3
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	transaction.timeout.ms = 60000
	transactional.id = null
	value.serializer = class org.apache.kafka.common.serialization.StringSerializer

2025-07-01 15:14:20.756 WARN  [main] producer.ProducerConfig (AbstractConfig.java:385) - These configurations '[ssl.endpoint.identification.algorithm]' were supplied but are not used yet.
2025-07-01 15:14:20.759 INFO  [main] utils.AppInfoParser (AppInfoParser.java:119) - Kafka version: 3.3.1
2025-07-01 15:14:20.759 INFO  [main] utils.AppInfoParser (AppInfoParser.java:120) - Kafka commitId: e23c59d00e687ff5
2025-07-01 15:14:20.759 INFO  [main] utils.AppInfoParser (AppInfoParser.java:121) - Kafka startTimeMs: 1751354060759
2025-07-01 15:14:20.760 WARN  [main] utils.AppInfoParser (AppInfoParser.java:68) - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.producer:type=app-info,id=siteHealth_producer_client
	at java.management/com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436)
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865)
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960)
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895)
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320)
	at java.management/com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523)
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64)
	at org.apache.kafka.clients.producer.KafkaProducer.<init>(KafkaProducer.java:462)
	at org.apache.kafka.clients.producer.KafkaProducer.<init>(KafkaProducer.java:291)
	at org.apache.kafka.clients.producer.KafkaProducer.<init>(KafkaProducer.java:318)
	at org.apache.kafka.clients.producer.KafkaProducer.<init>(KafkaProducer.java:303)
	at seoclarity.backend.kafka.clientconstructor.producer.siteHealth.SiteHealthProducerConstructor.createStringValueProducer(SiteHealthProducerConstructor.java:46)
	at seoclarity.backend.kafka.clientconstructor.producer.siteHealth.SiteHealthKafkaProducer.<init>(SiteHealthKafkaProducer.java:29)
	at seoclarity.backend.kafka.clientconstructor.producer.siteHealth.SiteHealthKafkaProducer.getInstance(SiteHealthKafkaProducer.java:19)
	at seoclarity.backend.upload.GoogleAIModeScraperUploader.<clinit>(GoogleAIModeScraperUploader.java:47)
2025-07-01 15:14:20.768 INFO  [main] utils.ScStringEncryptor (ScStringEncryptor.java:22) - Starting stringEncryptor configuration.
2025-07-01 15:14:20.773 INFO  [main] utils.ScStringEncryptor (ScStringEncryptor.java:33) - Finished stringEncryptor initialization.
2025-07-01 15:14:21.122 INFO  [main] http.AmazonHttpClient (ApacheHttpClientFactory.java:90) - Configuring Proxy. Proxy Host: 127.0.0.1 Proxy Port: 7890
2025-07-01 15:14:23.669 INFO  [kafka-producer-network-thread | siteHealth_producer_client] clients.Metadata (Metadata.java:287) - [Producer clientId=siteHealth_producer_client] Cluster ID: y9Z9s3O6SZe4iw1T2rXZ8g
2025-07-01 15:14:23.669 INFO  [kafka-producer-network-thread | siteHealth_producer_client] clients.Metadata (Metadata.java:287) - [Producer clientId=siteHealth_producer_client] Cluster ID: y9Z9s3O6SZe4iw1T2rXZ8g
2025-07-01 15:14:24.483 INFO  [main] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:102) - Starting Google AI mode scraper uploader with 10 threads
2025-07-01 15:14:24.992 INFO  [main] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:121) - No messages found, sleeping for 5 minutes...
2025-07-01 15:15:35.700 INFO  [main] producer.ProducerConfig (ProducerConfig.java:526) - Idempotence will be disabled because acks is set to 1, not set to 'all'.
2025-07-01 15:15:35.703 INFO  [main] producer.ProducerConfig (AbstractConfig.java:376) - ProducerConfig values: 
	acks = 1
	batch.size = 1048576
	bootstrap.servers = [**************:9093, ***********:9093, ************:9093]
	buffer.memory = 268435456
	client.dns.lookup = use_all_dns_ips
	client.id = siteHealth_producer_client
	compression.type = none
	connections.max.idle.ms = 540000
	delivery.timeout.ms = 120000
	enable.idempotence = false
	interceptor.classes = []
	key.serializer = class org.apache.kafka.common.serialization.StringSerializer
	linger.ms = 100
	max.block.ms = 600000
	max.in.flight.requests.per.connection = 5
	max.request.size = 5242880
	metadata.max.age.ms = 300000
	metadata.max.idle.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partitioner.adaptive.partitioning.enable = true
	partitioner.availability.timeout.ms = 0
	partitioner.class = null
	partitioner.ignore.keys = false
	receive.buffer.bytes = 32768
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 100000
	retries = 2
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = [hidden]
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.connect.timeout.ms = null
	sasl.login.read.timeout.ms = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.login.retry.backoff.max.ms = 10000
	sasl.login.retry.backoff.ms = 100
	sasl.mechanism = PLAIN
	sasl.oauthbearer.clock.skew.seconds = 30
	sasl.oauthbearer.expected.audience = null
	sasl.oauthbearer.expected.issuer = null
	sasl.oauthbearer.jwks.endpoint.refresh.ms = 3600000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.max.ms = 10000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.ms = 100
	sasl.oauthbearer.jwks.endpoint.url = null
	sasl.oauthbearer.scope.claim.name = scope
	sasl.oauthbearer.sub.claim.name = sub
	sasl.oauthbearer.token.endpoint.url = null
	security.protocol = SASL_PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.3]
	ssl.endpoint.identification.algorithm = 
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.3
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	transaction.timeout.ms = 60000
	transactional.id = null
	value.serializer = class org.apache.kafka.common.serialization.ByteArraySerializer

2025-07-01 15:15:35.804 INFO  [main] authenticator.AbstractLogin (AbstractLogin.java:61) - Successfully logged in.
2025-07-01 15:15:35.825 WARN  [main] producer.ProducerConfig (AbstractConfig.java:385) - These configurations '[ssl.endpoint.identification.algorithm]' were supplied but are not used yet.
2025-07-01 15:15:35.827 INFO  [main] utils.AppInfoParser (AppInfoParser.java:119) - Kafka version: 3.3.1
2025-07-01 15:15:35.827 INFO  [main] utils.AppInfoParser (AppInfoParser.java:120) - Kafka commitId: e23c59d00e687ff5
2025-07-01 15:15:35.827 INFO  [main] utils.AppInfoParser (AppInfoParser.java:121) - Kafka startTimeMs: 1751354135825
2025-07-01 15:15:35.828 INFO  [main] producer.ProducerConfig (ProducerConfig.java:526) - Idempotence will be disabled because acks is set to 1, not set to 'all'.
2025-07-01 15:15:35.829 INFO  [main] producer.ProducerConfig (AbstractConfig.java:376) - ProducerConfig values: 
	acks = 1
	batch.size = 1048576
	bootstrap.servers = [**************:9093, ***********:9093, ************:9093]
	buffer.memory = 268435456
	client.dns.lookup = use_all_dns_ips
	client.id = siteHealth_producer_client
	compression.type = none
	connections.max.idle.ms = 540000
	delivery.timeout.ms = 120000
	enable.idempotence = false
	interceptor.classes = []
	key.serializer = class org.apache.kafka.common.serialization.StringSerializer
	linger.ms = 100
	max.block.ms = 600000
	max.in.flight.requests.per.connection = 5
	max.request.size = 5242880
	metadata.max.age.ms = 300000
	metadata.max.idle.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partitioner.adaptive.partitioning.enable = true
	partitioner.availability.timeout.ms = 0
	partitioner.class = null
	partitioner.ignore.keys = false
	receive.buffer.bytes = 32768
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 100000
	retries = 2
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = [hidden]
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.connect.timeout.ms = null
	sasl.login.read.timeout.ms = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.login.retry.backoff.max.ms = 10000
	sasl.login.retry.backoff.ms = 100
	sasl.mechanism = PLAIN
	sasl.oauthbearer.clock.skew.seconds = 30
	sasl.oauthbearer.expected.audience = null
	sasl.oauthbearer.expected.issuer = null
	sasl.oauthbearer.jwks.endpoint.refresh.ms = 3600000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.max.ms = 10000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.ms = 100
	sasl.oauthbearer.jwks.endpoint.url = null
	sasl.oauthbearer.scope.claim.name = scope
	sasl.oauthbearer.sub.claim.name = sub
	sasl.oauthbearer.token.endpoint.url = null
	security.protocol = SASL_PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.3]
	ssl.endpoint.identification.algorithm = 
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.3
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	transaction.timeout.ms = 60000
	transactional.id = null
	value.serializer = class org.apache.kafka.common.serialization.StringSerializer

2025-07-01 15:15:35.832 WARN  [main] producer.ProducerConfig (AbstractConfig.java:385) - These configurations '[ssl.endpoint.identification.algorithm]' were supplied but are not used yet.
2025-07-01 15:15:35.833 INFO  [main] utils.AppInfoParser (AppInfoParser.java:119) - Kafka version: 3.3.1
2025-07-01 15:15:35.833 INFO  [main] utils.AppInfoParser (AppInfoParser.java:120) - Kafka commitId: e23c59d00e687ff5
2025-07-01 15:15:35.833 INFO  [main] utils.AppInfoParser (AppInfoParser.java:121) - Kafka startTimeMs: 1751354135833
2025-07-01 15:15:35.833 WARN  [main] utils.AppInfoParser (AppInfoParser.java:68) - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.producer:type=app-info,id=siteHealth_producer_client
	at java.management/com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436)
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865)
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960)
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895)
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320)
	at java.management/com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523)
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64)
	at org.apache.kafka.clients.producer.KafkaProducer.<init>(KafkaProducer.java:462)
	at org.apache.kafka.clients.producer.KafkaProducer.<init>(KafkaProducer.java:291)
	at org.apache.kafka.clients.producer.KafkaProducer.<init>(KafkaProducer.java:318)
	at org.apache.kafka.clients.producer.KafkaProducer.<init>(KafkaProducer.java:303)
	at seoclarity.backend.kafka.clientconstructor.producer.siteHealth.SiteHealthProducerConstructor.createStringValueProducer(SiteHealthProducerConstructor.java:46)
	at seoclarity.backend.kafka.clientconstructor.producer.siteHealth.SiteHealthKafkaProducer.<init>(SiteHealthKafkaProducer.java:29)
	at seoclarity.backend.kafka.clientconstructor.producer.siteHealth.SiteHealthKafkaProducer.getInstance(SiteHealthKafkaProducer.java:19)
	at seoclarity.backend.upload.GoogleAIModeScraperUploader.<clinit>(GoogleAIModeScraperUploader.java:47)
2025-07-01 15:15:35.839 INFO  [main] utils.ScStringEncryptor (ScStringEncryptor.java:22) - Starting stringEncryptor configuration.
2025-07-01 15:15:35.844 INFO  [main] utils.ScStringEncryptor (ScStringEncryptor.java:33) - Finished stringEncryptor initialization.
2025-07-01 15:15:36.236 INFO  [main] http.AmazonHttpClient (ApacheHttpClientFactory.java:90) - Configuring Proxy. Proxy Host: 127.0.0.1 Proxy Port: 7890
2025-07-01 15:15:38.747 INFO  [kafka-producer-network-thread | siteHealth_producer_client] clients.Metadata (Metadata.java:287) - [Producer clientId=siteHealth_producer_client] Cluster ID: y9Z9s3O6SZe4iw1T2rXZ8g
2025-07-01 15:15:38.747 INFO  [kafka-producer-network-thread | siteHealth_producer_client] clients.Metadata (Metadata.java:287) - [Producer clientId=siteHealth_producer_client] Cluster ID: y9Z9s3O6SZe4iw1T2rXZ8g
2025-07-01 15:15:39.656 INFO  [main] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:102) - Starting Google AI mode scraper uploader with 10 threads
2025-07-01 15:15:40.196 INFO  [main] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:121) - No messages found, sleeping for 5 minutes...
2025-07-01 15:16:21.169 INFO [main] producer.ProducerConfig (ProducerConfig.java:526) - Idempotence will be disabled because acks is set to 1, not set to 'all'.
2025-07-01 15:16:21.171 INFO [main] producer.ProducerConfig (AbstractConfig.java:376) - ProducerConfig values: 
	acks = 1
	batch.size = 1048576
	bootstrap.servers = [**************:9093, ***********:9093, ************:9093]
	buffer.memory = 268435456
	client.dns.lookup = use_all_dns_ips
	client.id = siteHealth_producer_client
	compression.type = none
	connections.max.idle.ms = 540000
	delivery.timeout.ms = 120000
	enable.idempotence = false
	interceptor.classes = []
	key.serializer = class org.apache.kafka.common.serialization.StringSerializer
	linger.ms = 100
	max.block.ms = 600000
	max.in.flight.requests.per.connection = 5
	max.request.size = 5242880
	metadata.max.age.ms = 300000
	metadata.max.idle.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partitioner.adaptive.partitioning.enable = true
	partitioner.availability.timeout.ms = 0
	partitioner.class = null
	partitioner.ignore.keys = false
	receive.buffer.bytes = 32768
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 100000
	retries = 2
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = [hidden]
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.connect.timeout.ms = null
	sasl.login.read.timeout.ms = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.login.retry.backoff.max.ms = 10000
	sasl.login.retry.backoff.ms = 100
	sasl.mechanism = PLAIN
	sasl.oauthbearer.clock.skew.seconds = 30
	sasl.oauthbearer.expected.audience = null
	sasl.oauthbearer.expected.issuer = null
	sasl.oauthbearer.jwks.endpoint.refresh.ms = 3600000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.max.ms = 10000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.ms = 100
	sasl.oauthbearer.jwks.endpoint.url = null
	sasl.oauthbearer.scope.claim.name = scope
	sasl.oauthbearer.sub.claim.name = sub
	sasl.oauthbearer.token.endpoint.url = null
	security.protocol = SASL_PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.3]
	ssl.endpoint.identification.algorithm = 
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.3
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	transaction.timeout.ms = 60000
	transactional.id = null
	value.serializer = class org.apache.kafka.common.serialization.ByteArraySerializer

2025-07-01 15:16:21.270 INFO [main] authenticator.AbstractLogin (AbstractLogin.java:61) - Successfully logged in.
2025-07-01 15:16:21.287 WARN [main] producer.ProducerConfig (AbstractConfig.java:385) - These configurations '[ssl.endpoint.identification.algorithm]' were supplied but are not used yet.
2025-07-01 15:16:21.288 INFO [main] utils.AppInfoParser (AppInfoParser.java:119) - Kafka version: 3.3.1
2025-07-01 15:16:21.288 INFO [main] utils.AppInfoParser (AppInfoParser.java:120) - Kafka commitId: e23c59d00e687ff5
2025-07-01 15:16:21.288 INFO [main] utils.AppInfoParser (AppInfoParser.java:121) - Kafka startTimeMs: 1751354181287
2025-07-01 15:16:21.289 INFO [main] producer.ProducerConfig (ProducerConfig.java:526) - Idempotence will be disabled because acks is set to 1, not set to 'all'.
2025-07-01 15:16:21.290 INFO [main] producer.ProducerConfig (AbstractConfig.java:376) - ProducerConfig values: 
	acks = 1
	batch.size = 1048576
	bootstrap.servers = [**************:9093, ***********:9093, ************:9093]
	buffer.memory = 268435456
	client.dns.lookup = use_all_dns_ips
	client.id = siteHealth_producer_client
	compression.type = none
	connections.max.idle.ms = 540000
	delivery.timeout.ms = 120000
	enable.idempotence = false
	interceptor.classes = []
	key.serializer = class org.apache.kafka.common.serialization.StringSerializer
	linger.ms = 100
	max.block.ms = 600000
	max.in.flight.requests.per.connection = 5
	max.request.size = 5242880
	metadata.max.age.ms = 300000
	metadata.max.idle.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partitioner.adaptive.partitioning.enable = true
	partitioner.availability.timeout.ms = 0
	partitioner.class = null
	partitioner.ignore.keys = false
	receive.buffer.bytes = 32768
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 100000
	retries = 2
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = [hidden]
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.connect.timeout.ms = null
	sasl.login.read.timeout.ms = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.login.retry.backoff.max.ms = 10000
	sasl.login.retry.backoff.ms = 100
	sasl.mechanism = PLAIN
	sasl.oauthbearer.clock.skew.seconds = 30
	sasl.oauthbearer.expected.audience = null
	sasl.oauthbearer.expected.issuer = null
	sasl.oauthbearer.jwks.endpoint.refresh.ms = 3600000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.max.ms = 10000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.ms = 100
	sasl.oauthbearer.jwks.endpoint.url = null
	sasl.oauthbearer.scope.claim.name = scope
	sasl.oauthbearer.sub.claim.name = sub
	sasl.oauthbearer.token.endpoint.url = null
	security.protocol = SASL_PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.3]
	ssl.endpoint.identification.algorithm = 
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.3
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	transaction.timeout.ms = 60000
	transactional.id = null
	value.serializer = class org.apache.kafka.common.serialization.StringSerializer

2025-07-01 15:16:21.293 WARN [main] producer.ProducerConfig (AbstractConfig.java:385) - These configurations '[ssl.endpoint.identification.algorithm]' were supplied but are not used yet.
2025-07-01 15:16:21.293 INFO [main] utils.AppInfoParser (AppInfoParser.java:119) - Kafka version: 3.3.1
2025-07-01 15:16:21.294 INFO [main] utils.AppInfoParser (AppInfoParser.java:120) - Kafka commitId: e23c59d00e687ff5
2025-07-01 15:16:21.294 INFO [main] utils.AppInfoParser (AppInfoParser.java:121) - Kafka startTimeMs: 1751354181293
2025-07-01 15:16:21.294 WARN [main] utils.AppInfoParser (AppInfoParser.java:68) - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.producer:type=app-info,id=siteHealth_producer_client
	at java.management/com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436)
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865)
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960)
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895)
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320)
	at java.management/com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523)
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64)
	at org.apache.kafka.clients.producer.KafkaProducer.<init>(KafkaProducer.java:462)
	at org.apache.kafka.clients.producer.KafkaProducer.<init>(KafkaProducer.java:291)
	at org.apache.kafka.clients.producer.KafkaProducer.<init>(KafkaProducer.java:318)
	at org.apache.kafka.clients.producer.KafkaProducer.<init>(KafkaProducer.java:303)
	at seoclarity.backend.kafka.clientconstructor.producer.siteHealth.SiteHealthProducerConstructor.createStringValueProducer(SiteHealthProducerConstructor.java:46)
	at seoclarity.backend.kafka.clientconstructor.producer.siteHealth.SiteHealthKafkaProducer.<init>(SiteHealthKafkaProducer.java:29)
	at seoclarity.backend.kafka.clientconstructor.producer.siteHealth.SiteHealthKafkaProducer.getInstance(SiteHealthKafkaProducer.java:19)
	at seoclarity.backend.upload.GoogleAIModeScraperUploader.<clinit>(GoogleAIModeScraperUploader.java:47)
2025-07-01 15:16:21.299 INFO [main] utils.ScStringEncryptor (ScStringEncryptor.java:22) - Starting stringEncryptor configuration.
2025-07-01 15:16:21.304 INFO [main] utils.ScStringEncryptor (ScStringEncryptor.java:33) - Finished stringEncryptor initialization.
2025-07-01 15:16:21.641 INFO [main] http.AmazonHttpClient (ApacheHttpClientFactory.java:90) - Configuring Proxy. Proxy Host: 127.0.0.1 Proxy Port: 7890
2025-07-01 15:16:24.084 INFO [kafka-producer-network-thread | siteHealth_producer_client] clients.Metadata (Metadata.java:287) - [Producer clientId=siteHealth_producer_client] Cluster ID: y9Z9s3O6SZe4iw1T2rXZ8g
2025-07-01 15:16:24.151 INFO [kafka-producer-network-thread | siteHealth_producer_client] clients.Metadata (Metadata.java:287) - [Producer clientId=siteHealth_producer_client] Cluster ID: y9Z9s3O6SZe4iw1T2rXZ8g
2025-07-01 15:16:24.789 INFO [main] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:102) - Starting Google AI mode scraper uploader with 10 threads
2025-07-01 15:16:25.281 INFO [main] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:121) - No messages found, sleeping for 5 minutes...
2025-07-01 15:26:28.146 INFO [main] producer.ProducerConfig (ProducerConfig.java:526) - Idempotence will be disabled because acks is set to 1, not set to 'all'.
2025-07-01 15:26:28.149 INFO [main] producer.ProducerConfig (AbstractConfig.java:376) - ProducerConfig values: 
	acks = 1
	batch.size = 1048576
	bootstrap.servers = [**************:9093, ***********:9093, ************:9093]
	buffer.memory = 268435456
	client.dns.lookup = use_all_dns_ips
	client.id = siteHealth_producer_client
	compression.type = none
	connections.max.idle.ms = 540000
	delivery.timeout.ms = 120000
	enable.idempotence = false
	interceptor.classes = []
	key.serializer = class org.apache.kafka.common.serialization.StringSerializer
	linger.ms = 100
	max.block.ms = 600000
	max.in.flight.requests.per.connection = 5
	max.request.size = 5242880
	metadata.max.age.ms = 300000
	metadata.max.idle.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partitioner.adaptive.partitioning.enable = true
	partitioner.availability.timeout.ms = 0
	partitioner.class = null
	partitioner.ignore.keys = false
	receive.buffer.bytes = 32768
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 100000
	retries = 2
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = [hidden]
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.connect.timeout.ms = null
	sasl.login.read.timeout.ms = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.login.retry.backoff.max.ms = 10000
	sasl.login.retry.backoff.ms = 100
	sasl.mechanism = PLAIN
	sasl.oauthbearer.clock.skew.seconds = 30
	sasl.oauthbearer.expected.audience = null
	sasl.oauthbearer.expected.issuer = null
	sasl.oauthbearer.jwks.endpoint.refresh.ms = 3600000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.max.ms = 10000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.ms = 100
	sasl.oauthbearer.jwks.endpoint.url = null
	sasl.oauthbearer.scope.claim.name = scope
	sasl.oauthbearer.sub.claim.name = sub
	sasl.oauthbearer.token.endpoint.url = null
	security.protocol = SASL_PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.3]
	ssl.endpoint.identification.algorithm = 
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.3
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	transaction.timeout.ms = 60000
	transactional.id = null
	value.serializer = class org.apache.kafka.common.serialization.ByteArraySerializer

2025-07-01 15:26:28.257 INFO [main] authenticator.AbstractLogin (AbstractLogin.java:61) - Successfully logged in.
2025-07-01 15:26:28.278 WARN [main] producer.ProducerConfig (AbstractConfig.java:385) - These configurations '[ssl.endpoint.identification.algorithm]' were supplied but are not used yet.
2025-07-01 15:26:28.280 INFO [main] utils.AppInfoParser (AppInfoParser.java:119) - Kafka version: 3.3.1
2025-07-01 15:26:28.280 INFO [main] utils.AppInfoParser (AppInfoParser.java:120) - Kafka commitId: e23c59d00e687ff5
2025-07-01 15:26:28.280 INFO [main] utils.AppInfoParser (AppInfoParser.java:121) - Kafka startTimeMs: 1751354788278
2025-07-01 15:26:28.281 INFO [main] producer.ProducerConfig (ProducerConfig.java:526) - Idempotence will be disabled because acks is set to 1, not set to 'all'.
2025-07-01 15:26:28.282 INFO [main] producer.ProducerConfig (AbstractConfig.java:376) - ProducerConfig values: 
	acks = 1
	batch.size = 1048576
	bootstrap.servers = [**************:9093, ***********:9093, ************:9093]
	buffer.memory = 268435456
	client.dns.lookup = use_all_dns_ips
	client.id = siteHealth_producer_client
	compression.type = none
	connections.max.idle.ms = 540000
	delivery.timeout.ms = 120000
	enable.idempotence = false
	interceptor.classes = []
	key.serializer = class org.apache.kafka.common.serialization.StringSerializer
	linger.ms = 100
	max.block.ms = 600000
	max.in.flight.requests.per.connection = 5
	max.request.size = 5242880
	metadata.max.age.ms = 300000
	metadata.max.idle.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partitioner.adaptive.partitioning.enable = true
	partitioner.availability.timeout.ms = 0
	partitioner.class = null
	partitioner.ignore.keys = false
	receive.buffer.bytes = 32768
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 100000
	retries = 2
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = [hidden]
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.connect.timeout.ms = null
	sasl.login.read.timeout.ms = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.login.retry.backoff.max.ms = 10000
	sasl.login.retry.backoff.ms = 100
	sasl.mechanism = PLAIN
	sasl.oauthbearer.clock.skew.seconds = 30
	sasl.oauthbearer.expected.audience = null
	sasl.oauthbearer.expected.issuer = null
	sasl.oauthbearer.jwks.endpoint.refresh.ms = 3600000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.max.ms = 10000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.ms = 100
	sasl.oauthbearer.jwks.endpoint.url = null
	sasl.oauthbearer.scope.claim.name = scope
	sasl.oauthbearer.sub.claim.name = sub
	sasl.oauthbearer.token.endpoint.url = null
	security.protocol = SASL_PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.3]
	ssl.endpoint.identification.algorithm = 
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.3
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	transaction.timeout.ms = 60000
	transactional.id = null
	value.serializer = class org.apache.kafka.common.serialization.StringSerializer

2025-07-01 15:26:28.287 WARN [main] producer.ProducerConfig (AbstractConfig.java:385) - These configurations '[ssl.endpoint.identification.algorithm]' were supplied but are not used yet.
2025-07-01 15:26:28.287 INFO [main] utils.AppInfoParser (AppInfoParser.java:119) - Kafka version: 3.3.1
2025-07-01 15:26:28.287 INFO [main] utils.AppInfoParser (AppInfoParser.java:120) - Kafka commitId: e23c59d00e687ff5
2025-07-01 15:26:28.287 INFO [main] utils.AppInfoParser (AppInfoParser.java:121) - Kafka startTimeMs: 1751354788287
2025-07-01 15:26:28.288 WARN [main] utils.AppInfoParser (AppInfoParser.java:68) - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.producer:type=app-info,id=siteHealth_producer_client
	at java.management/com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436)
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865)
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960)
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895)
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320)
	at java.management/com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523)
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64)
	at org.apache.kafka.clients.producer.KafkaProducer.<init>(KafkaProducer.java:462)
	at org.apache.kafka.clients.producer.KafkaProducer.<init>(KafkaProducer.java:291)
	at org.apache.kafka.clients.producer.KafkaProducer.<init>(KafkaProducer.java:318)
	at org.apache.kafka.clients.producer.KafkaProducer.<init>(KafkaProducer.java:303)
	at seoclarity.backend.kafka.clientconstructor.producer.siteHealth.SiteHealthProducerConstructor.createStringValueProducer(SiteHealthProducerConstructor.java:46)
	at seoclarity.backend.kafka.clientconstructor.producer.siteHealth.SiteHealthKafkaProducer.<init>(SiteHealthKafkaProducer.java:29)
	at seoclarity.backend.kafka.clientconstructor.producer.siteHealth.SiteHealthKafkaProducer.getInstance(SiteHealthKafkaProducer.java:19)
	at seoclarity.backend.upload.GoogleAIModeScraperUploader.<clinit>(GoogleAIModeScraperUploader.java:47)
2025-07-01 15:26:28.294 INFO [main] utils.ScStringEncryptor (ScStringEncryptor.java:22) - Starting stringEncryptor configuration.
2025-07-01 15:26:28.299 INFO [main] utils.ScStringEncryptor (ScStringEncryptor.java:33) - Finished stringEncryptor initialization.
2025-07-01 15:26:28.628 INFO [main] http.AmazonHttpClient (ApacheHttpClientFactory.java:90) - Configuring Proxy. Proxy Host: 127.0.0.1 Proxy Port: 7890
2025-07-01 15:26:30.983 INFO [kafka-producer-network-thread | siteHealth_producer_client] clients.Metadata (Metadata.java:287) - [Producer clientId=siteHealth_producer_client] Cluster ID: y9Z9s3O6SZe4iw1T2rXZ8g
2025-07-01 15:26:31.007 INFO [kafka-producer-network-thread | siteHealth_producer_client] clients.Metadata (Metadata.java:287) - [Producer clientId=siteHealth_producer_client] Cluster ID: y9Z9s3O6SZe4iw1T2rXZ8g
2025-07-01 15:26:31.796 INFO [main] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:102) - Starting Google AI mode scraper uploader with 10 threads
2025-07-01 15:26:32.315 INFO [main] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:121) - No messages found, sleeping for 5 minutes...
15:26:41.967 INFO [main] producer.ProducerConfig (ProducerConfig.java:526) - Idempotence will be disabled because acks is set to 1, not set to 'all'.
15:26:41.969 INFO [main] producer.ProducerConfig (AbstractConfig.java:376) - ProducerConfig values: 
	acks = 1
	batch.size = 1048576
	bootstrap.servers = [**************:9093, ***********:9093, ************:9093]
	buffer.memory = 268435456
	client.dns.lookup = use_all_dns_ips
	client.id = siteHealth_producer_client
	compression.type = none
	connections.max.idle.ms = 540000
	delivery.timeout.ms = 120000
	enable.idempotence = false
	interceptor.classes = []
	key.serializer = class org.apache.kafka.common.serialization.StringSerializer
	linger.ms = 100
	max.block.ms = 600000
	max.in.flight.requests.per.connection = 5
	max.request.size = 5242880
	metadata.max.age.ms = 300000
	metadata.max.idle.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partitioner.adaptive.partitioning.enable = true
	partitioner.availability.timeout.ms = 0
	partitioner.class = null
	partitioner.ignore.keys = false
	receive.buffer.bytes = 32768
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 100000
	retries = 2
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = [hidden]
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.connect.timeout.ms = null
	sasl.login.read.timeout.ms = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.login.retry.backoff.max.ms = 10000
	sasl.login.retry.backoff.ms = 100
	sasl.mechanism = PLAIN
	sasl.oauthbearer.clock.skew.seconds = 30
	sasl.oauthbearer.expected.audience = null
	sasl.oauthbearer.expected.issuer = null
	sasl.oauthbearer.jwks.endpoint.refresh.ms = 3600000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.max.ms = 10000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.ms = 100
	sasl.oauthbearer.jwks.endpoint.url = null
	sasl.oauthbearer.scope.claim.name = scope
	sasl.oauthbearer.sub.claim.name = sub
	sasl.oauthbearer.token.endpoint.url = null
	security.protocol = SASL_PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.3]
	ssl.endpoint.identification.algorithm = 
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.3
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	transaction.timeout.ms = 60000
	transactional.id = null
	value.serializer = class org.apache.kafka.common.serialization.ByteArraySerializer

15:26:42.061 INFO [main] authenticator.AbstractLogin (AbstractLogin.java:61) - Successfully logged in.
15:26:42.080 WARN [main] producer.ProducerConfig (AbstractConfig.java:385) - These configurations '[ssl.endpoint.identification.algorithm]' were supplied but are not used yet.
15:26:42.081 INFO [main] utils.AppInfoParser (AppInfoParser.java:119) - Kafka version: 3.3.1
15:26:42.081 INFO [main] utils.AppInfoParser (AppInfoParser.java:120) - Kafka commitId: e23c59d00e687ff5
15:26:42.082 INFO [main] utils.AppInfoParser (AppInfoParser.java:121) - Kafka startTimeMs: 1751354802080
15:26:42.083 INFO [main] producer.ProducerConfig (ProducerConfig.java:526) - Idempotence will be disabled because acks is set to 1, not set to 'all'.
15:26:42.083 INFO [main] producer.ProducerConfig (AbstractConfig.java:376) - ProducerConfig values: 
	acks = 1
	batch.size = 1048576
	bootstrap.servers = [**************:9093, ***********:9093, ************:9093]
	buffer.memory = 268435456
	client.dns.lookup = use_all_dns_ips
	client.id = siteHealth_producer_client
	compression.type = none
	connections.max.idle.ms = 540000
	delivery.timeout.ms = 120000
	enable.idempotence = false
	interceptor.classes = []
	key.serializer = class org.apache.kafka.common.serialization.StringSerializer
	linger.ms = 100
	max.block.ms = 600000
	max.in.flight.requests.per.connection = 5
	max.request.size = 5242880
	metadata.max.age.ms = 300000
	metadata.max.idle.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partitioner.adaptive.partitioning.enable = true
	partitioner.availability.timeout.ms = 0
	partitioner.class = null
	partitioner.ignore.keys = false
	receive.buffer.bytes = 32768
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 100000
	retries = 2
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = [hidden]
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.connect.timeout.ms = null
	sasl.login.read.timeout.ms = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.login.retry.backoff.max.ms = 10000
	sasl.login.retry.backoff.ms = 100
	sasl.mechanism = PLAIN
	sasl.oauthbearer.clock.skew.seconds = 30
	sasl.oauthbearer.expected.audience = null
	sasl.oauthbearer.expected.issuer = null
	sasl.oauthbearer.jwks.endpoint.refresh.ms = 3600000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.max.ms = 10000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.ms = 100
	sasl.oauthbearer.jwks.endpoint.url = null
	sasl.oauthbearer.scope.claim.name = scope
	sasl.oauthbearer.sub.claim.name = sub
	sasl.oauthbearer.token.endpoint.url = null
	security.protocol = SASL_PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.3]
	ssl.endpoint.identification.algorithm = 
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.3
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	transaction.timeout.ms = 60000
	transactional.id = null
	value.serializer = class org.apache.kafka.common.serialization.StringSerializer

15:26:42.087 WARN [main] producer.ProducerConfig (AbstractConfig.java:385) - These configurations '[ssl.endpoint.identification.algorithm]' were supplied but are not used yet.
15:26:42.087 INFO [main] utils.AppInfoParser (AppInfoParser.java:119) - Kafka version: 3.3.1
15:26:42.087 INFO [main] utils.AppInfoParser (AppInfoParser.java:120) - Kafka commitId: e23c59d00e687ff5
15:26:42.087 INFO [main] utils.AppInfoParser (AppInfoParser.java:121) - Kafka startTimeMs: 1751354802087
15:26:42.087 WARN [main] utils.AppInfoParser (AppInfoParser.java:68) - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.producer:type=app-info,id=siteHealth_producer_client
	at java.management/com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436)
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865)
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960)
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895)
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320)
	at java.management/com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523)
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64)
	at org.apache.kafka.clients.producer.KafkaProducer.<init>(KafkaProducer.java:462)
	at org.apache.kafka.clients.producer.KafkaProducer.<init>(KafkaProducer.java:291)
	at org.apache.kafka.clients.producer.KafkaProducer.<init>(KafkaProducer.java:318)
	at org.apache.kafka.clients.producer.KafkaProducer.<init>(KafkaProducer.java:303)
	at seoclarity.backend.kafka.clientconstructor.producer.siteHealth.SiteHealthProducerConstructor.createStringValueProducer(SiteHealthProducerConstructor.java:46)
	at seoclarity.backend.kafka.clientconstructor.producer.siteHealth.SiteHealthKafkaProducer.<init>(SiteHealthKafkaProducer.java:29)
	at seoclarity.backend.kafka.clientconstructor.producer.siteHealth.SiteHealthKafkaProducer.getInstance(SiteHealthKafkaProducer.java:19)
	at seoclarity.backend.upload.GoogleAIModeScraperUploader.<clinit>(GoogleAIModeScraperUploader.java:47)
15:26:42.093 INFO [main] utils.ScStringEncryptor (ScStringEncryptor.java:22) - Starting stringEncryptor configuration.
15:26:42.097 INFO [main] utils.ScStringEncryptor (ScStringEncryptor.java:33) - Finished stringEncryptor initialization.
15:26:42.370 INFO [main] http.AmazonHttpClient (ApacheHttpClientFactory.java:90) - Configuring Proxy. Proxy Host: 127.0.0.1 Proxy Port: 7890
15:26:44.994 INFO [kafka-producer-network-thread | siteHealth_producer_client] clients.Metadata (Metadata.java:287) - [Producer clientId=siteHealth_producer_client] Cluster ID: y9Z9s3O6SZe4iw1T2rXZ8g
15:26:45.068 INFO [kafka-producer-network-thread | siteHealth_producer_client] clients.Metadata (Metadata.java:287) - [Producer clientId=siteHealth_producer_client] Cluster ID: y9Z9s3O6SZe4iw1T2rXZ8g
15:26:48.323 INFO [main] producer.ProducerConfig (ProducerConfig.java:526) - Idempotence will be disabled because acks is set to 1, not set to 'all'.
15:26:48.325 INFO [main] producer.ProducerConfig (AbstractConfig.java:376) - ProducerConfig values: 
	acks = 1
	batch.size = 1048576
	bootstrap.servers = [**************:9093, ***********:9093, ************:9093]
	buffer.memory = 268435456
	client.dns.lookup = use_all_dns_ips
	client.id = siteHealth_producer_client
	compression.type = none
	connections.max.idle.ms = 540000
	delivery.timeout.ms = 120000
	enable.idempotence = false
	interceptor.classes = []
	key.serializer = class org.apache.kafka.common.serialization.StringSerializer
	linger.ms = 100
	max.block.ms = 600000
	max.in.flight.requests.per.connection = 5
	max.request.size = 5242880
	metadata.max.age.ms = 300000
	metadata.max.idle.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partitioner.adaptive.partitioning.enable = true
	partitioner.availability.timeout.ms = 0
	partitioner.class = null
	partitioner.ignore.keys = false
	receive.buffer.bytes = 32768
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 100000
	retries = 2
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = [hidden]
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.connect.timeout.ms = null
	sasl.login.read.timeout.ms = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.login.retry.backoff.max.ms = 10000
	sasl.login.retry.backoff.ms = 100
	sasl.mechanism = PLAIN
	sasl.oauthbearer.clock.skew.seconds = 30
	sasl.oauthbearer.expected.audience = null
	sasl.oauthbearer.expected.issuer = null
	sasl.oauthbearer.jwks.endpoint.refresh.ms = 3600000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.max.ms = 10000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.ms = 100
	sasl.oauthbearer.jwks.endpoint.url = null
	sasl.oauthbearer.scope.claim.name = scope
	sasl.oauthbearer.sub.claim.name = sub
	sasl.oauthbearer.token.endpoint.url = null
	security.protocol = SASL_PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.3]
	ssl.endpoint.identification.algorithm = 
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.3
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	transaction.timeout.ms = 60000
	transactional.id = null
	value.serializer = class org.apache.kafka.common.serialization.ByteArraySerializer

15:26:48.423 INFO [main] authenticator.AbstractLogin (AbstractLogin.java:61) - Successfully logged in.
15:26:48.444 WARN [main] producer.ProducerConfig (AbstractConfig.java:385) - These configurations '[ssl.endpoint.identification.algorithm]' were supplied but are not used yet.
15:26:48.445 INFO [main] utils.AppInfoParser (AppInfoParser.java:119) - Kafka version: 3.3.1
15:26:48.445 INFO [main] utils.AppInfoParser (AppInfoParser.java:120) - Kafka commitId: e23c59d00e687ff5
15:26:48.446 INFO [main] utils.AppInfoParser (AppInfoParser.java:121) - Kafka startTimeMs: 1751354808444
15:26:48.447 INFO [main] producer.ProducerConfig (ProducerConfig.java:526) - Idempotence will be disabled because acks is set to 1, not set to 'all'.
15:26:48.447 INFO [main] producer.ProducerConfig (AbstractConfig.java:376) - ProducerConfig values: 
	acks = 1
	batch.size = 1048576
	bootstrap.servers = [**************:9093, ***********:9093, ************:9093]
	buffer.memory = 268435456
	client.dns.lookup = use_all_dns_ips
	client.id = siteHealth_producer_client
	compression.type = none
	connections.max.idle.ms = 540000
	delivery.timeout.ms = 120000
	enable.idempotence = false
	interceptor.classes = []
	key.serializer = class org.apache.kafka.common.serialization.StringSerializer
	linger.ms = 100
	max.block.ms = 600000
	max.in.flight.requests.per.connection = 5
	max.request.size = 5242880
	metadata.max.age.ms = 300000
	metadata.max.idle.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partitioner.adaptive.partitioning.enable = true
	partitioner.availability.timeout.ms = 0
	partitioner.class = null
	partitioner.ignore.keys = false
	receive.buffer.bytes = 32768
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 100000
	retries = 2
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = [hidden]
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.connect.timeout.ms = null
	sasl.login.read.timeout.ms = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.login.retry.backoff.max.ms = 10000
	sasl.login.retry.backoff.ms = 100
	sasl.mechanism = PLAIN
	sasl.oauthbearer.clock.skew.seconds = 30
	sasl.oauthbearer.expected.audience = null
	sasl.oauthbearer.expected.issuer = null
	sasl.oauthbearer.jwks.endpoint.refresh.ms = 3600000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.max.ms = 10000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.ms = 100
	sasl.oauthbearer.jwks.endpoint.url = null
	sasl.oauthbearer.scope.claim.name = scope
	sasl.oauthbearer.sub.claim.name = sub
	sasl.oauthbearer.token.endpoint.url = null
	security.protocol = SASL_PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.3]
	ssl.endpoint.identification.algorithm = 
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.3
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	transaction.timeout.ms = 60000
	transactional.id = null
	value.serializer = class org.apache.kafka.common.serialization.StringSerializer

15:26:48.451 WARN [main] producer.ProducerConfig (AbstractConfig.java:385) - These configurations '[ssl.endpoint.identification.algorithm]' were supplied but are not used yet.
15:26:48.451 INFO [main] utils.AppInfoParser (AppInfoParser.java:119) - Kafka version: 3.3.1
15:26:48.451 INFO [main] utils.AppInfoParser (AppInfoParser.java:120) - Kafka commitId: e23c59d00e687ff5
15:26:48.451 INFO [main] utils.AppInfoParser (AppInfoParser.java:121) - Kafka startTimeMs: 1751354808451
15:26:48.452 WARN [main] utils.AppInfoParser (AppInfoParser.java:68) - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.producer:type=app-info,id=siteHealth_producer_client
	at java.management/com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436)
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865)
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960)
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895)
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320)
	at java.management/com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523)
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64)
	at org.apache.kafka.clients.producer.KafkaProducer.<init>(KafkaProducer.java:462)
	at org.apache.kafka.clients.producer.KafkaProducer.<init>(KafkaProducer.java:291)
	at org.apache.kafka.clients.producer.KafkaProducer.<init>(KafkaProducer.java:318)
	at org.apache.kafka.clients.producer.KafkaProducer.<init>(KafkaProducer.java:303)
	at seoclarity.backend.kafka.clientconstructor.producer.siteHealth.SiteHealthProducerConstructor.createStringValueProducer(SiteHealthProducerConstructor.java:46)
	at seoclarity.backend.kafka.clientconstructor.producer.siteHealth.SiteHealthKafkaProducer.<init>(SiteHealthKafkaProducer.java:29)
	at seoclarity.backend.kafka.clientconstructor.producer.siteHealth.SiteHealthKafkaProducer.getInstance(SiteHealthKafkaProducer.java:19)
	at seoclarity.backend.upload.GoogleAIModeScraperUploader.<clinit>(GoogleAIModeScraperUploader.java:47)
15:26:48.457 INFO [main] utils.ScStringEncryptor (ScStringEncryptor.java:22) - Starting stringEncryptor configuration.
15:26:48.461 INFO [main] utils.ScStringEncryptor (ScStringEncryptor.java:33) - Finished stringEncryptor initialization.
15:26:48.777 INFO [main] http.AmazonHttpClient (ApacheHttpClientFactory.java:90) - Configuring Proxy. Proxy Host: 127.0.0.1 Proxy Port: 7890
15:26:51.210 INFO [kafka-producer-network-thread | siteHealth_producer_client] clients.Metadata (Metadata.java:287) - [Producer clientId=siteHealth_producer_client] Cluster ID: y9Z9s3O6SZe4iw1T2rXZ8g
15:26:51.249 INFO [kafka-producer-network-thread | siteHealth_producer_client] clients.Metadata (Metadata.java:287) - [Producer clientId=siteHealth_producer_client] Cluster ID: y9Z9s3O6SZe4iw1T2rXZ8g
15:26:51.912 INFO [main] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:102) - Starting Google AI mode scraper uploader with 10 threads
15:26:52.413 INFO [main] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:121) - No messages found, sleeping for 5 minutes...
15:27:40.995 INFO [main] producer.ProducerConfig (ProducerConfig.java:526) - Idempotence will be disabled because acks is set to 1, not set to 'all'.
15:27:40.998 INFO [main] producer.ProducerConfig (AbstractConfig.java:376) - ProducerConfig values: 
	acks = 1
	batch.size = 1048576
	bootstrap.servers = [**************:9093, ***********:9093, ************:9093]
	buffer.memory = 268435456
	client.dns.lookup = use_all_dns_ips
	client.id = siteHealth_producer_client
	compression.type = none
	connections.max.idle.ms = 540000
	delivery.timeout.ms = 120000
	enable.idempotence = false
	interceptor.classes = []
	key.serializer = class org.apache.kafka.common.serialization.StringSerializer
	linger.ms = 100
	max.block.ms = 600000
	max.in.flight.requests.per.connection = 5
	max.request.size = 5242880
	metadata.max.age.ms = 300000
	metadata.max.idle.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partitioner.adaptive.partitioning.enable = true
	partitioner.availability.timeout.ms = 0
	partitioner.class = null
	partitioner.ignore.keys = false
	receive.buffer.bytes = 32768
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 100000
	retries = 2
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = [hidden]
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.connect.timeout.ms = null
	sasl.login.read.timeout.ms = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.login.retry.backoff.max.ms = 10000
	sasl.login.retry.backoff.ms = 100
	sasl.mechanism = PLAIN
	sasl.oauthbearer.clock.skew.seconds = 30
	sasl.oauthbearer.expected.audience = null
	sasl.oauthbearer.expected.issuer = null
	sasl.oauthbearer.jwks.endpoint.refresh.ms = 3600000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.max.ms = 10000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.ms = 100
	sasl.oauthbearer.jwks.endpoint.url = null
	sasl.oauthbearer.scope.claim.name = scope
	sasl.oauthbearer.sub.claim.name = sub
	sasl.oauthbearer.token.endpoint.url = null
	security.protocol = SASL_PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.3]
	ssl.endpoint.identification.algorithm = 
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.3
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	transaction.timeout.ms = 60000
	transactional.id = null
	value.serializer = class org.apache.kafka.common.serialization.ByteArraySerializer

15:27:41.158 INFO [main] authenticator.AbstractLogin (AbstractLogin.java:61) - Successfully logged in.
15:27:41.194 WARN [main] producer.ProducerConfig (AbstractConfig.java:385) - These configurations '[ssl.endpoint.identification.algorithm]' were supplied but are not used yet.
15:27:41.195 INFO [main] utils.AppInfoParser (AppInfoParser.java:119) - Kafka version: 3.3.1
15:27:41.196 INFO [main] utils.AppInfoParser (AppInfoParser.java:120) - Kafka commitId: e23c59d00e687ff5
15:27:41.196 INFO [main] utils.AppInfoParser (AppInfoParser.java:121) - Kafka startTimeMs: 1751354861194
15:27:41.197 INFO [main] producer.ProducerConfig (ProducerConfig.java:526) - Idempotence will be disabled because acks is set to 1, not set to 'all'.
15:27:41.198 INFO [main] producer.ProducerConfig (AbstractConfig.java:376) - ProducerConfig values: 
	acks = 1
	batch.size = 1048576
	bootstrap.servers = [**************:9093, ***********:9093, ************:9093]
	buffer.memory = 268435456
	client.dns.lookup = use_all_dns_ips
	client.id = siteHealth_producer_client
	compression.type = none
	connections.max.idle.ms = 540000
	delivery.timeout.ms = 120000
	enable.idempotence = false
	interceptor.classes = []
	key.serializer = class org.apache.kafka.common.serialization.StringSerializer
	linger.ms = 100
	max.block.ms = 600000
	max.in.flight.requests.per.connection = 5
	max.request.size = 5242880
	metadata.max.age.ms = 300000
	metadata.max.idle.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partitioner.adaptive.partitioning.enable = true
	partitioner.availability.timeout.ms = 0
	partitioner.class = null
	partitioner.ignore.keys = false
	receive.buffer.bytes = 32768
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 100000
	retries = 2
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = [hidden]
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.connect.timeout.ms = null
	sasl.login.read.timeout.ms = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.login.retry.backoff.max.ms = 10000
	sasl.login.retry.backoff.ms = 100
	sasl.mechanism = PLAIN
	sasl.oauthbearer.clock.skew.seconds = 30
	sasl.oauthbearer.expected.audience = null
	sasl.oauthbearer.expected.issuer = null
	sasl.oauthbearer.jwks.endpoint.refresh.ms = 3600000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.max.ms = 10000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.ms = 100
	sasl.oauthbearer.jwks.endpoint.url = null
	sasl.oauthbearer.scope.claim.name = scope
	sasl.oauthbearer.sub.claim.name = sub
	sasl.oauthbearer.token.endpoint.url = null
	security.protocol = SASL_PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.3]
	ssl.endpoint.identification.algorithm = 
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.3
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	transaction.timeout.ms = 60000
	transactional.id = null
	value.serializer = class org.apache.kafka.common.serialization.StringSerializer

15:27:41.201 WARN [main] producer.ProducerConfig (AbstractConfig.java:385) - These configurations '[ssl.endpoint.identification.algorithm]' were supplied but are not used yet.
15:27:41.201 INFO [main] utils.AppInfoParser (AppInfoParser.java:119) - Kafka version: 3.3.1
15:27:41.201 INFO [main] utils.AppInfoParser (AppInfoParser.java:120) - Kafka commitId: e23c59d00e687ff5
15:27:41.201 INFO [main] utils.AppInfoParser (AppInfoParser.java:121) - Kafka startTimeMs: 1751354861201
15:27:41.202 WARN [main] utils.AppInfoParser (AppInfoParser.java:68) - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.producer:type=app-info,id=siteHealth_producer_client
	at java.management/com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436)
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865)
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960)
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895)
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320)
	at java.management/com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523)
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64)
	at org.apache.kafka.clients.producer.KafkaProducer.<init>(KafkaProducer.java:462)
	at org.apache.kafka.clients.producer.KafkaProducer.<init>(KafkaProducer.java:291)
	at org.apache.kafka.clients.producer.KafkaProducer.<init>(KafkaProducer.java:318)
	at org.apache.kafka.clients.producer.KafkaProducer.<init>(KafkaProducer.java:303)
	at seoclarity.backend.kafka.clientconstructor.producer.siteHealth.SiteHealthProducerConstructor.createStringValueProducer(SiteHealthProducerConstructor.java:46)
	at seoclarity.backend.kafka.clientconstructor.producer.siteHealth.SiteHealthKafkaProducer.<init>(SiteHealthKafkaProducer.java:29)
	at seoclarity.backend.kafka.clientconstructor.producer.siteHealth.SiteHealthKafkaProducer.getInstance(SiteHealthKafkaProducer.java:19)
	at seoclarity.backend.upload.GoogleAIModeScraperUploader.<clinit>(GoogleAIModeScraperUploader.java:47)
15:27:41.208 INFO [main] utils.ScStringEncryptor (ScStringEncryptor.java:22) - Starting stringEncryptor configuration.
15:27:41.212 INFO [main] utils.ScStringEncryptor (ScStringEncryptor.java:33) - Finished stringEncryptor initialization.
15:27:41.566 INFO [main] http.AmazonHttpClient (ApacheHttpClientFactory.java:90) - Configuring Proxy. Proxy Host: 127.0.0.1 Proxy Port: 7890
15:27:44.025 INFO [kafka-producer-network-thread | siteHealth_producer_client] clients.Metadata (Metadata.java:287) - [Producer clientId=siteHealth_producer_client] Cluster ID: y9Z9s3O6SZe4iw1T2rXZ8g
15:27:44.036 INFO [kafka-producer-network-thread | siteHealth_producer_client] clients.Metadata (Metadata.java:287) - [Producer clientId=siteHealth_producer_client] Cluster ID: y9Z9s3O6SZe4iw1T2rXZ8g
15:27:44.973 INFO [main] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:102) - Starting Google AI mode scraper uploader with 10 threads
15:27:45.482 INFO [main] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:121) - No messages found, sleeping for 5 minutes...
