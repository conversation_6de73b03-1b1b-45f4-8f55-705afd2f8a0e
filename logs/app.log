10:32:56.356 INFO [main] support.ClassPathXmlApplicationContext (AbstractApplicationContext.java:578) - Refreshing org.springframework.context.support.ClassPathXmlApplicationContext@36bc55de: startup date [Mon Aug 04 10:32:56 CST 2025]; root of context hierarchy
10:32:56.371 INFO [main] xml.XmlBeanDefinitionReader (XmlBeanDefinitionReader.java:317) - Loading XML bean definitions from class path resource [applicationContext.xml]
10:32:57.093 INFO [main] xml.XmlBeanDefinitionReader (XmlBeanDefinitionReader.java:317) - Loading XML bean definitions from class path resource [applicationContext-dao.xml]
10:32:57.136 INFO [main] xml.XmlBeanDefinitionReader (XmlBeanDefinitionReader.java:317) - Loading XML bean definitions from class path resource [applicationContext-email.xml]
10:32:57.310 INFO [main] support.PropertySourcesPlaceholderConfigurer (PropertiesLoaderSupport.java:172) - Loading properties file from class path resource [jdbc.properties]
10:32:57.321 INFO [main] support.PropertySourcesPlaceholderConfigurer (PropertiesLoaderSupport.java:172) - Loading properties file from class path resource [mailjet.properties]
10:32:57.324 INFO [main] support.PropertySourcesPlaceholderConfigurer (PropertiesLoaderSupport.java:172) - Loading properties file from class path resource [serverInfo.properties]
10:32:57.325 INFO [main] support.PropertySourcesPlaceholderConfigurer (PropertiesLoaderSupport.java:172) - Loading properties file from class path resource [kafka.properties]
10:32:57.329 INFO [main] annotation.AutowiredAnnotationBeanPostProcessor (AutowiredAnnotationBeanPostProcessor.java:153) - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
10:32:57.411 WARN [main] utils.GenericsUtils (GenericsUtils.java:54) - MonthlyRankingGRDao not set the actual class on superclass generic parameter
10:32:57.548 WARN [main] utils.GenericsUtils (GenericsUtils.java:54) - SplitTestCentralDAO not set the actual class on superclass generic parameter
10:32:57.563 WARN [main] utils.GenericsUtils (GenericsUtils.java:54) - ClResearchgridManagedUrlDao not set the actual class on superclass generic parameter
10:32:57.563 WARN [main] utils.GenericsUtils (GenericsUtils.java:42) - RGRankingDetail202107DAO3's superclass not ParameterizedType
10:32:57.581 WARN [main] utils.GenericsUtils (GenericsUtils.java:42) - RGRankingDetail202107DAO1's superclass not ParameterizedType
10:32:57.667 WARN [main] utils.GenericsUtils (GenericsUtils.java:42) - RGRankingDetail202107DAO4's superclass not ParameterizedType
10:32:57.708 WARN [main] utils.GenericsUtils (GenericsUtils.java:42) - RGRankingDetail202107DAO2's superclass not ParameterizedType
10:32:57.885 INFO [main] support.ClassPathXmlApplicationContext (AbstractApplicationContext.java:578) - Refreshing org.springframework.context.support.ClassPathXmlApplicationContext@462abec3: startup date [Mon Aug 04 10:32:57 CST 2025]; root of context hierarchy
10:32:57.886 INFO [main] xml.XmlBeanDefinitionReader (XmlBeanDefinitionReader.java:317) - Loading XML bean definitions from class path resource [applicationContext.xml]
10:32:58.194 INFO [main] xml.XmlBeanDefinitionReader (XmlBeanDefinitionReader.java:317) - Loading XML bean definitions from class path resource [applicationContext-dao.xml]
10:32:58.213 INFO [main] xml.XmlBeanDefinitionReader (XmlBeanDefinitionReader.java:317) - Loading XML bean definitions from class path resource [applicationContext-email.xml]
10:32:58.263 INFO [main] support.PropertySourcesPlaceholderConfigurer (PropertiesLoaderSupport.java:172) - Loading properties file from class path resource [jdbc.properties]
10:32:58.268 INFO [main] support.PropertySourcesPlaceholderConfigurer (PropertiesLoaderSupport.java:172) - Loading properties file from class path resource [mailjet.properties]
10:32:58.269 INFO [main] support.PropertySourcesPlaceholderConfigurer (PropertiesLoaderSupport.java:172) - Loading properties file from class path resource [serverInfo.properties]
10:32:58.270 INFO [main] support.PropertySourcesPlaceholderConfigurer (PropertiesLoaderSupport.java:172) - Loading properties file from class path resource [kafka.properties]
10:32:58.273 INFO [main] annotation.AutowiredAnnotationBeanPostProcessor (AutowiredAnnotationBeanPostProcessor.java:153) - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
10:32:58.283 WARN [main] utils.GenericsUtils (GenericsUtils.java:54) - MonthlyRankingGRDao not set the actual class on superclass generic parameter
10:32:58.308 WARN [main] utils.GenericsUtils (GenericsUtils.java:54) - SplitTestCentralDAO not set the actual class on superclass generic parameter
10:32:58.313 WARN [main] utils.GenericsUtils (GenericsUtils.java:54) - ClResearchgridManagedUrlDao not set the actual class on superclass generic parameter
10:32:58.313 WARN [main] utils.GenericsUtils (GenericsUtils.java:42) - RGRankingDetail202107DAO3's superclass not ParameterizedType
10:32:58.317 WARN [main] utils.GenericsUtils (GenericsUtils.java:42) - RGRankingDetail202107DAO1's superclass not ParameterizedType
10:32:58.333 WARN [main] utils.GenericsUtils (GenericsUtils.java:42) - RGRankingDetail202107DAO4's superclass not ParameterizedType
10:32:58.345 WARN [main] utils.GenericsUtils (GenericsUtils.java:42) - RGRankingDetail202107DAO2's superclass not ParameterizedType
10:32:58.437 INFO [main] support.ClassPathXmlApplicationContext (AbstractApplicationContext.java:578) - Refreshing org.springframework.context.support.ClassPathXmlApplicationContext@100bba26: startup date [Mon Aug 04 10:32:58 CST 2025]; root of context hierarchy
10:32:58.437 INFO [main] xml.XmlBeanDefinitionReader (XmlBeanDefinitionReader.java:317) - Loading XML bean definitions from class path resource [applicationContext.xml]
10:32:58.696 INFO [main] xml.XmlBeanDefinitionReader (XmlBeanDefinitionReader.java:317) - Loading XML bean definitions from class path resource [applicationContext-dao.xml]
10:32:58.709 INFO [main] xml.XmlBeanDefinitionReader (XmlBeanDefinitionReader.java:317) - Loading XML bean definitions from class path resource [applicationContext-email.xml]
10:32:58.754 INFO [main] support.PropertySourcesPlaceholderConfigurer (PropertiesLoaderSupport.java:172) - Loading properties file from class path resource [jdbc.properties]
10:32:58.756 INFO [main] support.PropertySourcesPlaceholderConfigurer (PropertiesLoaderSupport.java:172) - Loading properties file from class path resource [mailjet.properties]
10:32:58.757 INFO [main] support.PropertySourcesPlaceholderConfigurer (PropertiesLoaderSupport.java:172) - Loading properties file from class path resource [serverInfo.properties]
10:32:58.759 INFO [main] support.PropertySourcesPlaceholderConfigurer (PropertiesLoaderSupport.java:172) - Loading properties file from class path resource [kafka.properties]
10:32:58.761 INFO [main] annotation.AutowiredAnnotationBeanPostProcessor (AutowiredAnnotationBeanPostProcessor.java:153) - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
10:32:58.768 WARN [main] utils.GenericsUtils (GenericsUtils.java:54) - MonthlyRankingGRDao not set the actual class on superclass generic parameter
10:32:58.795 WARN [main] utils.GenericsUtils (GenericsUtils.java:54) - SplitTestCentralDAO not set the actual class on superclass generic parameter
10:32:58.807 WARN [main] utils.GenericsUtils (GenericsUtils.java:54) - ClResearchgridManagedUrlDao not set the actual class on superclass generic parameter
10:32:58.807 WARN [main] utils.GenericsUtils (GenericsUtils.java:42) - RGRankingDetail202107DAO3's superclass not ParameterizedType
10:32:58.811 WARN [main] utils.GenericsUtils (GenericsUtils.java:42) - RGRankingDetail202107DAO1's superclass not ParameterizedType
10:32:58.826 WARN [main] utils.GenericsUtils (GenericsUtils.java:42) - RGRankingDetail202107DAO4's superclass not ParameterizedType
10:32:58.834 WARN [main] utils.GenericsUtils (GenericsUtils.java:42) - RGRankingDetail202107DAO2's superclass not ParameterizedType
10:32:58.852 INFO [main] support.ClassPathXmlApplicationContext (AbstractApplicationContext.java:578) - Refreshing org.springframework.context.support.ClassPathXmlApplicationContext@6ae6d078: startup date [Mon Aug 04 10:32:58 CST 2025]; root of context hierarchy
10:32:58.852 INFO [main] xml.XmlBeanDefinitionReader (XmlBeanDefinitionReader.java:317) - Loading XML bean definitions from class path resource [applicationContext.xml]
10:32:59.301 INFO [main] xml.XmlBeanDefinitionReader (XmlBeanDefinitionReader.java:317) - Loading XML bean definitions from class path resource [applicationContext-dao.xml]
10:32:59.316 INFO [main] xml.XmlBeanDefinitionReader (XmlBeanDefinitionReader.java:317) - Loading XML bean definitions from class path resource [applicationContext-email.xml]
10:32:59.363 INFO [main] support.PropertySourcesPlaceholderConfigurer (PropertiesLoaderSupport.java:172) - Loading properties file from class path resource [jdbc.properties]
10:32:59.659 INFO [main] support.PropertySourcesPlaceholderConfigurer (PropertiesLoaderSupport.java:172) - Loading properties file from class path resource [mailjet.properties]
10:32:59.660 INFO [main] support.PropertySourcesPlaceholderConfigurer (PropertiesLoaderSupport.java:172) - Loading properties file from class path resource [serverInfo.properties]
10:32:59.661 INFO [main] support.PropertySourcesPlaceholderConfigurer (PropertiesLoaderSupport.java:172) - Loading properties file from class path resource [kafka.properties]
10:32:59.663 INFO [main] annotation.AutowiredAnnotationBeanPostProcessor (AutowiredAnnotationBeanPostProcessor.java:153) - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
10:32:59.985 WARN [main] utils.GenericsUtils (GenericsUtils.java:54) - MonthlyRankingGRDao not set the actual class on superclass generic parameter
10:33:00.023 WARN [main] utils.GenericsUtils (GenericsUtils.java:54) - SplitTestCentralDAO not set the actual class on superclass generic parameter
10:33:00.027 WARN [main] utils.GenericsUtils (GenericsUtils.java:54) - ClResearchgridManagedUrlDao not set the actual class on superclass generic parameter
10:33:00.027 WARN [main] utils.GenericsUtils (GenericsUtils.java:42) - RGRankingDetail202107DAO3's superclass not ParameterizedType
10:33:00.031 WARN [main] utils.GenericsUtils (GenericsUtils.java:42) - RGRankingDetail202107DAO1's superclass not ParameterizedType
10:33:00.045 WARN [main] utils.GenericsUtils (GenericsUtils.java:42) - RGRankingDetail202107DAO4's superclass not ParameterizedType
10:33:00.052 WARN [main] utils.GenericsUtils (GenericsUtils.java:42) - RGRankingDetail202107DAO2's superclass not ParameterizedType
10:33:00.074 INFO [main] support.ClassPathXmlApplicationContext (AbstractApplicationContext.java:578) - Refreshing org.springframework.context.support.ClassPathXmlApplicationContext@543ba2e1: startup date [Mon Aug 04 10:33:00 CST 2025]; root of context hierarchy
10:33:00.074 INFO [main] xml.XmlBeanDefinitionReader (XmlBeanDefinitionReader.java:317) - Loading XML bean definitions from class path resource [applicationContext.xml]
10:33:00.368 INFO [main] xml.XmlBeanDefinitionReader (XmlBeanDefinitionReader.java:317) - Loading XML bean definitions from class path resource [applicationContext-dao.xml]
10:33:00.380 INFO [main] xml.XmlBeanDefinitionReader (XmlBeanDefinitionReader.java:317) - Loading XML bean definitions from class path resource [applicationContext-email.xml]
10:33:00.447 INFO [main] support.PropertySourcesPlaceholderConfigurer (PropertiesLoaderSupport.java:172) - Loading properties file from class path resource [jdbc.properties]
10:33:00.453 INFO [main] support.PropertySourcesPlaceholderConfigurer (PropertiesLoaderSupport.java:172) - Loading properties file from class path resource [mailjet.properties]
10:33:00.455 INFO [main] support.PropertySourcesPlaceholderConfigurer (PropertiesLoaderSupport.java:172) - Loading properties file from class path resource [serverInfo.properties]
10:33:00.460 INFO [main] support.PropertySourcesPlaceholderConfigurer (PropertiesLoaderSupport.java:172) - Loading properties file from class path resource [kafka.properties]
10:33:00.469 INFO [main] annotation.AutowiredAnnotationBeanPostProcessor (AutowiredAnnotationBeanPostProcessor.java:153) - JSR-330 'javax.inject.Inject' annotation found and supported for autowiring
10:33:00.505 WARN [main] utils.GenericsUtils (GenericsUtils.java:54) - MonthlyRankingGRDao not set the actual class on superclass generic parameter
10:33:00.545 WARN [main] utils.GenericsUtils (GenericsUtils.java:54) - SplitTestCentralDAO not set the actual class on superclass generic parameter
10:33:00.554 WARN [main] utils.GenericsUtils (GenericsUtils.java:54) - ClResearchgridManagedUrlDao not set the actual class on superclass generic parameter
10:33:00.554 WARN [main] utils.GenericsUtils (GenericsUtils.java:42) - RGRankingDetail202107DAO3's superclass not ParameterizedType
10:33:00.559 WARN [main] utils.GenericsUtils (GenericsUtils.java:42) - RGRankingDetail202107DAO1's superclass not ParameterizedType
10:33:00.575 WARN [main] utils.GenericsUtils (GenericsUtils.java:42) - RGRankingDetail202107DAO4's superclass not ParameterizedType
10:33:00.585 WARN [main] utils.GenericsUtils (GenericsUtils.java:42) - RGRankingDetail202107DAO2's superclass not ParameterizedType
