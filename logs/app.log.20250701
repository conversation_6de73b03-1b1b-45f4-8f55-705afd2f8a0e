15:28:58.049 INFO [main] producer.ProducerConfig (ProducerConfig.java:526) - Idempotence will be disabled because acks is set to 1, not set to 'all'.
15:28:58.051 INFO [main] producer.ProducerConfig (AbstractConfig.java:376) - ProducerConfig values: 
	acks = 1
	batch.size = 1048576
	bootstrap.servers = [**************:9093, ***********:9093, ************:9093]
	buffer.memory = 268435456
	client.dns.lookup = use_all_dns_ips
	client.id = siteHealth_producer_client
	compression.type = none
	connections.max.idle.ms = 540000
	delivery.timeout.ms = 120000
	enable.idempotence = false
	interceptor.classes = []
	key.serializer = class org.apache.kafka.common.serialization.StringSerializer
	linger.ms = 100
	max.block.ms = 600000
	max.in.flight.requests.per.connection = 5
	max.request.size = 5242880
	metadata.max.age.ms = 300000
	metadata.max.idle.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partitioner.adaptive.partitioning.enable = true
	partitioner.availability.timeout.ms = 0
	partitioner.class = null
	partitioner.ignore.keys = false
	receive.buffer.bytes = 32768
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 100000
	retries = 2
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = [hidden]
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.connect.timeout.ms = null
	sasl.login.read.timeout.ms = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.login.retry.backoff.max.ms = 10000
	sasl.login.retry.backoff.ms = 100
	sasl.mechanism = PLAIN
	sasl.oauthbearer.clock.skew.seconds = 30
	sasl.oauthbearer.expected.audience = null
	sasl.oauthbearer.expected.issuer = null
	sasl.oauthbearer.jwks.endpoint.refresh.ms = 3600000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.max.ms = 10000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.ms = 100
	sasl.oauthbearer.jwks.endpoint.url = null
	sasl.oauthbearer.scope.claim.name = scope
	sasl.oauthbearer.sub.claim.name = sub
	sasl.oauthbearer.token.endpoint.url = null
	security.protocol = SASL_PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.3]
	ssl.endpoint.identification.algorithm = 
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.3
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	transaction.timeout.ms = 60000
	transactional.id = null
	value.serializer = class org.apache.kafka.common.serialization.ByteArraySerializer

15:28:58.154 INFO [main] authenticator.AbstractLogin (AbstractLogin.java:61) - Successfully logged in.
15:28:58.179 WARN [main] producer.ProducerConfig (AbstractConfig.java:385) - These configurations '[ssl.endpoint.identification.algorithm]' were supplied but are not used yet.
15:28:58.181 INFO [main] utils.AppInfoParser (AppInfoParser.java:119) - Kafka version: 3.3.1
15:28:58.181 INFO [main] utils.AppInfoParser (AppInfoParser.java:120) - Kafka commitId: e23c59d00e687ff5
15:28:58.181 INFO [main] utils.AppInfoParser (AppInfoParser.java:121) - Kafka startTimeMs: 1751354938179
15:28:58.182 INFO [main] producer.ProducerConfig (ProducerConfig.java:526) - Idempotence will be disabled because acks is set to 1, not set to 'all'.
15:28:58.182 INFO [main] producer.ProducerConfig (AbstractConfig.java:376) - ProducerConfig values: 
	acks = 1
	batch.size = 1048576
	bootstrap.servers = [**************:9093, ***********:9093, ************:9093]
	buffer.memory = 268435456
	client.dns.lookup = use_all_dns_ips
	client.id = siteHealth_producer_client
	compression.type = none
	connections.max.idle.ms = 540000
	delivery.timeout.ms = 120000
	enable.idempotence = false
	interceptor.classes = []
	key.serializer = class org.apache.kafka.common.serialization.StringSerializer
	linger.ms = 100
	max.block.ms = 600000
	max.in.flight.requests.per.connection = 5
	max.request.size = 5242880
	metadata.max.age.ms = 300000
	metadata.max.idle.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partitioner.adaptive.partitioning.enable = true
	partitioner.availability.timeout.ms = 0
	partitioner.class = null
	partitioner.ignore.keys = false
	receive.buffer.bytes = 32768
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 100000
	retries = 2
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = [hidden]
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.connect.timeout.ms = null
	sasl.login.read.timeout.ms = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.login.retry.backoff.max.ms = 10000
	sasl.login.retry.backoff.ms = 100
	sasl.mechanism = PLAIN
	sasl.oauthbearer.clock.skew.seconds = 30
	sasl.oauthbearer.expected.audience = null
	sasl.oauthbearer.expected.issuer = null
	sasl.oauthbearer.jwks.endpoint.refresh.ms = 3600000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.max.ms = 10000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.ms = 100
	sasl.oauthbearer.jwks.endpoint.url = null
	sasl.oauthbearer.scope.claim.name = scope
	sasl.oauthbearer.sub.claim.name = sub
	sasl.oauthbearer.token.endpoint.url = null
	security.protocol = SASL_PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.3]
	ssl.endpoint.identification.algorithm = 
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.3
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	transaction.timeout.ms = 60000
	transactional.id = null
	value.serializer = class org.apache.kafka.common.serialization.StringSerializer

15:28:58.187 WARN [main] producer.ProducerConfig (AbstractConfig.java:385) - These configurations '[ssl.endpoint.identification.algorithm]' were supplied but are not used yet.
15:28:58.187 INFO [main] utils.AppInfoParser (AppInfoParser.java:119) - Kafka version: 3.3.1
15:28:58.187 INFO [main] utils.AppInfoParser (AppInfoParser.java:120) - Kafka commitId: e23c59d00e687ff5
15:28:58.187 INFO [main] utils.AppInfoParser (AppInfoParser.java:121) - Kafka startTimeMs: 1751354938187
15:28:58.188 WARN [main] utils.AppInfoParser (AppInfoParser.java:68) - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.producer:type=app-info,id=siteHealth_producer_client
	at java.management/com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436)
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865)
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960)
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895)
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320)
	at java.management/com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523)
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64)
	at org.apache.kafka.clients.producer.KafkaProducer.<init>(KafkaProducer.java:462)
	at org.apache.kafka.clients.producer.KafkaProducer.<init>(KafkaProducer.java:291)
	at org.apache.kafka.clients.producer.KafkaProducer.<init>(KafkaProducer.java:318)
	at org.apache.kafka.clients.producer.KafkaProducer.<init>(KafkaProducer.java:303)
	at seoclarity.backend.kafka.clientconstructor.producer.siteHealth.SiteHealthProducerConstructor.createStringValueProducer(SiteHealthProducerConstructor.java:46)
	at seoclarity.backend.kafka.clientconstructor.producer.siteHealth.SiteHealthKafkaProducer.<init>(SiteHealthKafkaProducer.java:29)
	at seoclarity.backend.kafka.clientconstructor.producer.siteHealth.SiteHealthKafkaProducer.getInstance(SiteHealthKafkaProducer.java:19)
	at seoclarity.backend.upload.GoogleAIModeScraperUploader.<clinit>(GoogleAIModeScraperUploader.java:47)
15:28:58.195 INFO [main] utils.ScStringEncryptor (ScStringEncryptor.java:22) - Starting stringEncryptor configuration.
15:28:58.199 INFO [main] utils.ScStringEncryptor (ScStringEncryptor.java:33) - Finished stringEncryptor initialization.
15:28:58.580 INFO [main] http.AmazonHttpClient (ApacheHttpClientFactory.java:90) - Configuring Proxy. Proxy Host: 127.0.0.1 Proxy Port: 7890
15:29:00.976 INFO [kafka-producer-network-thread | siteHealth_producer_client] clients.Metadata (Metadata.java:287) - [Producer clientId=siteHealth_producer_client] Cluster ID: y9Z9s3O6SZe4iw1T2rXZ8g
15:29:00.976 INFO [kafka-producer-network-thread | siteHealth_producer_client] clients.Metadata (Metadata.java:287) - [Producer clientId=siteHealth_producer_client] Cluster ID: y9Z9s3O6SZe4iw1T2rXZ8g
15:29:01.900 INFO [main] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:102) - Starting Google AI mode scraper uploader with 10 threads
15:29:02.414 INFO [main] upload.GoogleAIModeScraperUploader (GoogleAIModeScraperUploader.java:121) - No messages found, sleeping for 5 minutes...
15:29:58.534 INFO [main] producer.ProducerConfig (ProducerConfig.java:526) - Idempotence will be disabled because acks is set to 1, not set to 'all'.
15:29:58.536 INFO [main] producer.ProducerConfig (AbstractConfig.java:376) - ProducerConfig values: 
	acks = 1
	batch.size = 1048576
	bootstrap.servers = [**************:9093, ***********:9093, ************:9093]
	buffer.memory = 268435456
	client.dns.lookup = use_all_dns_ips
	client.id = siteHealth_producer_client
	compression.type = none
	connections.max.idle.ms = 540000
	delivery.timeout.ms = 120000
	enable.idempotence = false
	interceptor.classes = []
	key.serializer = class org.apache.kafka.common.serialization.StringSerializer
	linger.ms = 100
	max.block.ms = 600000
	max.in.flight.requests.per.connection = 5
	max.request.size = 5242880
	metadata.max.age.ms = 300000
	metadata.max.idle.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partitioner.adaptive.partitioning.enable = true
	partitioner.availability.timeout.ms = 0
	partitioner.class = null
	partitioner.ignore.keys = false
	receive.buffer.bytes = 32768
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 100000
	retries = 2
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = [hidden]
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.connect.timeout.ms = null
	sasl.login.read.timeout.ms = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.login.retry.backoff.max.ms = 10000
	sasl.login.retry.backoff.ms = 100
	sasl.mechanism = PLAIN
	sasl.oauthbearer.clock.skew.seconds = 30
	sasl.oauthbearer.expected.audience = null
	sasl.oauthbearer.expected.issuer = null
	sasl.oauthbearer.jwks.endpoint.refresh.ms = 3600000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.max.ms = 10000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.ms = 100
	sasl.oauthbearer.jwks.endpoint.url = null
	sasl.oauthbearer.scope.claim.name = scope
	sasl.oauthbearer.sub.claim.name = sub
	sasl.oauthbearer.token.endpoint.url = null
	security.protocol = SASL_PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.3]
	ssl.endpoint.identification.algorithm = 
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.3
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	transaction.timeout.ms = 60000
	transactional.id = null
	value.serializer = class org.apache.kafka.common.serialization.ByteArraySerializer

15:29:58.633 INFO [main] authenticator.AbstractLogin (AbstractLogin.java:61) - Successfully logged in.
15:29:58.650 WARN [main] producer.ProducerConfig (AbstractConfig.java:385) - These configurations '[ssl.endpoint.identification.algorithm]' were supplied but are not used yet.
15:29:58.651 INFO [main] utils.AppInfoParser (AppInfoParser.java:119) - Kafka version: 3.3.1
15:29:58.651 INFO [main] utils.AppInfoParser (AppInfoParser.java:120) - Kafka commitId: e23c59d00e687ff5
15:29:58.652 INFO [main] utils.AppInfoParser (AppInfoParser.java:121) - Kafka startTimeMs: 1751354998650
15:29:58.653 INFO [main] producer.ProducerConfig (ProducerConfig.java:526) - Idempotence will be disabled because acks is set to 1, not set to 'all'.
15:29:58.653 INFO [main] producer.ProducerConfig (AbstractConfig.java:376) - ProducerConfig values: 
	acks = 1
	batch.size = 1048576
	bootstrap.servers = [**************:9093, ***********:9093, ************:9093]
	buffer.memory = 268435456
	client.dns.lookup = use_all_dns_ips
	client.id = siteHealth_producer_client
	compression.type = none
	connections.max.idle.ms = 540000
	delivery.timeout.ms = 120000
	enable.idempotence = false
	interceptor.classes = []
	key.serializer = class org.apache.kafka.common.serialization.StringSerializer
	linger.ms = 100
	max.block.ms = 600000
	max.in.flight.requests.per.connection = 5
	max.request.size = 5242880
	metadata.max.age.ms = 300000
	metadata.max.idle.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partitioner.adaptive.partitioning.enable = true
	partitioner.availability.timeout.ms = 0
	partitioner.class = null
	partitioner.ignore.keys = false
	receive.buffer.bytes = 32768
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 100000
	retries = 2
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = [hidden]
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.connect.timeout.ms = null
	sasl.login.read.timeout.ms = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.login.retry.backoff.max.ms = 10000
	sasl.login.retry.backoff.ms = 100
	sasl.mechanism = PLAIN
	sasl.oauthbearer.clock.skew.seconds = 30
	sasl.oauthbearer.expected.audience = null
	sasl.oauthbearer.expected.issuer = null
	sasl.oauthbearer.jwks.endpoint.refresh.ms = 3600000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.max.ms = 10000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.ms = 100
	sasl.oauthbearer.jwks.endpoint.url = null
	sasl.oauthbearer.scope.claim.name = scope
	sasl.oauthbearer.sub.claim.name = sub
	sasl.oauthbearer.token.endpoint.url = null
	security.protocol = SASL_PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.3]
	ssl.endpoint.identification.algorithm = 
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.3
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	transaction.timeout.ms = 60000
	transactional.id = null
	value.serializer = class org.apache.kafka.common.serialization.StringSerializer

15:29:58.656 WARN [main] producer.ProducerConfig (AbstractConfig.java:385) - These configurations '[ssl.endpoint.identification.algorithm]' were supplied but are not used yet.
15:29:58.657 INFO [main] utils.AppInfoParser (AppInfoParser.java:119) - Kafka version: 3.3.1
15:29:58.657 INFO [main] utils.AppInfoParser (AppInfoParser.java:120) - Kafka commitId: e23c59d00e687ff5
15:29:58.657 INFO [main] utils.AppInfoParser (AppInfoParser.java:121) - Kafka startTimeMs: 1751354998657
15:29:58.657 WARN [main] utils.AppInfoParser (AppInfoParser.java:68) - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.producer:type=app-info,id=siteHealth_producer_client
	at java.management/com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436)
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865)
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960)
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895)
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320)
	at java.management/com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523)
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64)
	at org.apache.kafka.clients.producer.KafkaProducer.<init>(KafkaProducer.java:462)
	at org.apache.kafka.clients.producer.KafkaProducer.<init>(KafkaProducer.java:291)
	at org.apache.kafka.clients.producer.KafkaProducer.<init>(KafkaProducer.java:318)
	at org.apache.kafka.clients.producer.KafkaProducer.<init>(KafkaProducer.java:303)
	at seoclarity.backend.kafka.clientconstructor.producer.siteHealth.SiteHealthProducerConstructor.createStringValueProducer(SiteHealthProducerConstructor.java:46)
	at seoclarity.backend.kafka.clientconstructor.producer.siteHealth.SiteHealthKafkaProducer.<init>(SiteHealthKafkaProducer.java:29)
	at seoclarity.backend.kafka.clientconstructor.producer.siteHealth.SiteHealthKafkaProducer.getInstance(SiteHealthKafkaProducer.java:19)
	at seoclarity.backend.upload.GoogleAIModeScraperUploader.<clinit>(GoogleAIModeScraperUploader.java:47)
15:29:58.662 INFO [main] utils.ScStringEncryptor (ScStringEncryptor.java:22) - Starting stringEncryptor configuration.
15:29:58.666 INFO [main] utils.ScStringEncryptor (ScStringEncryptor.java:33) - Finished stringEncryptor initialization.
15:29:58.969 INFO [main] http.AmazonHttpClient (ApacheHttpClientFactory.java:90) - Configuring Proxy. Proxy Host: 127.0.0.1 Proxy Port: 7890
15:30:01.435 INFO [kafka-producer-network-thread | siteHealth_producer_client] clients.Metadata (Metadata.java:287) - [Producer clientId=siteHealth_producer_client] Cluster ID: y9Z9s3O6SZe4iw1T2rXZ8g
15:30:01.435 INFO [kafka-producer-network-thread | siteHealth_producer_client] clients.Metadata (Metadata.java:287) - [Producer clientId=siteHealth_producer_client] Cluster ID: y9Z9s3O6SZe4iw1T2rXZ8g
18:00:56.659 INFO [main] producer.ProducerConfig (ProducerConfig.java:526) - Idempotence will be disabled because acks is set to 1, not set to 'all'.
18:00:56.662 INFO [main] producer.ProducerConfig (AbstractConfig.java:376) - ProducerConfig values: 
	acks = 1
	batch.size = 1048576
	bootstrap.servers = [**************:9093, ***********:9093, ************:9093]
	buffer.memory = 268435456
	client.dns.lookup = use_all_dns_ips
	client.id = siteHealth_producer_client
	compression.type = none
	connections.max.idle.ms = 540000
	delivery.timeout.ms = 120000
	enable.idempotence = false
	interceptor.classes = []
	key.serializer = class org.apache.kafka.common.serialization.StringSerializer
	linger.ms = 100
	max.block.ms = 600000
	max.in.flight.requests.per.connection = 5
	max.request.size = 5242880
	metadata.max.age.ms = 300000
	metadata.max.idle.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partitioner.adaptive.partitioning.enable = true
	partitioner.availability.timeout.ms = 0
	partitioner.class = null
	partitioner.ignore.keys = false
	receive.buffer.bytes = 32768
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 100000
	retries = 2
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = [hidden]
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.connect.timeout.ms = null
	sasl.login.read.timeout.ms = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.login.retry.backoff.max.ms = 10000
	sasl.login.retry.backoff.ms = 100
	sasl.mechanism = PLAIN
	sasl.oauthbearer.clock.skew.seconds = 30
	sasl.oauthbearer.expected.audience = null
	sasl.oauthbearer.expected.issuer = null
	sasl.oauthbearer.jwks.endpoint.refresh.ms = 3600000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.max.ms = 10000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.ms = 100
	sasl.oauthbearer.jwks.endpoint.url = null
	sasl.oauthbearer.scope.claim.name = scope
	sasl.oauthbearer.sub.claim.name = sub
	sasl.oauthbearer.token.endpoint.url = null
	security.protocol = SASL_PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.3]
	ssl.endpoint.identification.algorithm = 
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.3
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	transaction.timeout.ms = 60000
	transactional.id = null
	value.serializer = class org.apache.kafka.common.serialization.ByteArraySerializer

18:00:56.783 INFO [main] authenticator.AbstractLogin (AbstractLogin.java:61) - Successfully logged in.
18:00:56.803 WARN [main] producer.ProducerConfig (AbstractConfig.java:385) - These configurations '[ssl.endpoint.identification.algorithm]' were supplied but are not used yet.
18:00:56.804 INFO [main] utils.AppInfoParser (AppInfoParser.java:119) - Kafka version: 3.3.1
18:00:56.804 INFO [main] utils.AppInfoParser (AppInfoParser.java:120) - Kafka commitId: e23c59d00e687ff5
18:00:56.804 INFO [main] utils.AppInfoParser (AppInfoParser.java:121) - Kafka startTimeMs: 1751364056803
18:00:56.805 INFO [main] producer.ProducerConfig (ProducerConfig.java:526) - Idempotence will be disabled because acks is set to 1, not set to 'all'.
18:00:56.806 INFO [main] producer.ProducerConfig (AbstractConfig.java:376) - ProducerConfig values: 
	acks = 1
	batch.size = 1048576
	bootstrap.servers = [**************:9093, ***********:9093, ************:9093]
	buffer.memory = 268435456
	client.dns.lookup = use_all_dns_ips
	client.id = siteHealth_producer_client
	compression.type = none
	connections.max.idle.ms = 540000
	delivery.timeout.ms = 120000
	enable.idempotence = false
	interceptor.classes = []
	key.serializer = class org.apache.kafka.common.serialization.StringSerializer
	linger.ms = 100
	max.block.ms = 600000
	max.in.flight.requests.per.connection = 5
	max.request.size = 5242880
	metadata.max.age.ms = 300000
	metadata.max.idle.ms = 300000
	metric.reporters = []
	metrics.num.samples = 2
	metrics.recording.level = INFO
	metrics.sample.window.ms = 30000
	partitioner.adaptive.partitioning.enable = true
	partitioner.availability.timeout.ms = 0
	partitioner.class = null
	partitioner.ignore.keys = false
	receive.buffer.bytes = 32768
	reconnect.backoff.max.ms = 1000
	reconnect.backoff.ms = 50
	request.timeout.ms = 100000
	retries = 2
	retry.backoff.ms = 100
	sasl.client.callback.handler.class = null
	sasl.jaas.config = [hidden]
	sasl.kerberos.kinit.cmd = /usr/bin/kinit
	sasl.kerberos.min.time.before.relogin = 60000
	sasl.kerberos.service.name = null
	sasl.kerberos.ticket.renew.jitter = 0.05
	sasl.kerberos.ticket.renew.window.factor = 0.8
	sasl.login.callback.handler.class = null
	sasl.login.class = null
	sasl.login.connect.timeout.ms = null
	sasl.login.read.timeout.ms = null
	sasl.login.refresh.buffer.seconds = 300
	sasl.login.refresh.min.period.seconds = 60
	sasl.login.refresh.window.factor = 0.8
	sasl.login.refresh.window.jitter = 0.05
	sasl.login.retry.backoff.max.ms = 10000
	sasl.login.retry.backoff.ms = 100
	sasl.mechanism = PLAIN
	sasl.oauthbearer.clock.skew.seconds = 30
	sasl.oauthbearer.expected.audience = null
	sasl.oauthbearer.expected.issuer = null
	sasl.oauthbearer.jwks.endpoint.refresh.ms = 3600000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.max.ms = 10000
	sasl.oauthbearer.jwks.endpoint.retry.backoff.ms = 100
	sasl.oauthbearer.jwks.endpoint.url = null
	sasl.oauthbearer.scope.claim.name = scope
	sasl.oauthbearer.sub.claim.name = sub
	sasl.oauthbearer.token.endpoint.url = null
	security.protocol = SASL_PLAINTEXT
	security.providers = null
	send.buffer.bytes = 131072
	socket.connection.setup.timeout.max.ms = 30000
	socket.connection.setup.timeout.ms = 10000
	ssl.cipher.suites = null
	ssl.enabled.protocols = [TLSv1.2, TLSv1.3]
	ssl.endpoint.identification.algorithm = 
	ssl.engine.factory.class = null
	ssl.key.password = null
	ssl.keymanager.algorithm = SunX509
	ssl.keystore.certificate.chain = null
	ssl.keystore.key = null
	ssl.keystore.location = null
	ssl.keystore.password = null
	ssl.keystore.type = JKS
	ssl.protocol = TLSv1.3
	ssl.provider = null
	ssl.secure.random.implementation = null
	ssl.trustmanager.algorithm = PKIX
	ssl.truststore.certificates = null
	ssl.truststore.location = null
	ssl.truststore.password = null
	ssl.truststore.type = JKS
	transaction.timeout.ms = 60000
	transactional.id = null
	value.serializer = class org.apache.kafka.common.serialization.StringSerializer

18:00:56.810 WARN [main] producer.ProducerConfig (AbstractConfig.java:385) - These configurations '[ssl.endpoint.identification.algorithm]' were supplied but are not used yet.
18:00:56.810 INFO [main] utils.AppInfoParser (AppInfoParser.java:119) - Kafka version: 3.3.1
18:00:56.810 INFO [main] utils.AppInfoParser (AppInfoParser.java:120) - Kafka commitId: e23c59d00e687ff5
18:00:56.810 INFO [main] utils.AppInfoParser (AppInfoParser.java:121) - Kafka startTimeMs: 1751364056810
18:00:56.810 WARN [main] utils.AppInfoParser (AppInfoParser.java:68) - Error registering AppInfo mbean
javax.management.InstanceAlreadyExistsException: kafka.producer:type=app-info,id=siteHealth_producer_client
	at java.management/com.sun.jmx.mbeanserver.Repository.addMBean(Repository.java:436)
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerWithRepository(DefaultMBeanServerInterceptor.java:1865)
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerDynamicMBean(DefaultMBeanServerInterceptor.java:960)
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerObject(DefaultMBeanServerInterceptor.java:895)
	at java.management/com.sun.jmx.interceptor.DefaultMBeanServerInterceptor.registerMBean(DefaultMBeanServerInterceptor.java:320)
	at java.management/com.sun.jmx.mbeanserver.JmxMBeanServer.registerMBean(JmxMBeanServer.java:523)
	at org.apache.kafka.common.utils.AppInfoParser.registerAppInfo(AppInfoParser.java:64)
	at org.apache.kafka.clients.producer.KafkaProducer.<init>(KafkaProducer.java:462)
	at org.apache.kafka.clients.producer.KafkaProducer.<init>(KafkaProducer.java:291)
	at org.apache.kafka.clients.producer.KafkaProducer.<init>(KafkaProducer.java:318)
	at org.apache.kafka.clients.producer.KafkaProducer.<init>(KafkaProducer.java:303)
	at seoclarity.backend.kafka.clientconstructor.producer.siteHealth.SiteHealthProducerConstructor.createStringValueProducer(SiteHealthProducerConstructor.java:46)
	at seoclarity.backend.kafka.clientconstructor.producer.siteHealth.SiteHealthKafkaProducer.<init>(SiteHealthKafkaProducer.java:29)
	at seoclarity.backend.kafka.clientconstructor.producer.siteHealth.SiteHealthKafkaProducer.getInstance(SiteHealthKafkaProducer.java:19)
	at seoclarity.backend.upload.GoogleAIModeScraperUploader.<clinit>(GoogleAIModeScraperUploader.java:47)
